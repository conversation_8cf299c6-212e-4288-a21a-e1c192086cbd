﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<script src="~/lib/report/echarts/echarts.min.js"></script>
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="funRoomEvaluateProjectId" col="FunRoomEvaluateProjectId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="level" col="Level" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="oneClassId" col="OneClassId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <div id="container">
                <div id="container_0" style="height: 300px; margin-bottom: 50px; margin-bottom: 30px; "></div>
                <div id="container_1" style="height: 300px; margin-bottom: 50px; margin-bottom: 30px; "></div>
                <div id="container_2" style="height: 300px; margin-bottom: 50px; margin-bottom: 30px; "></div>
            </div>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

    <script type="text/javascript">
    $(function () {
         if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#liCountyId').show();
            loadCounty();
        }

        loadFunRoomEvaluateProject();
        loadSchoolStage();
        loadOneClass();
        loadLevel();
        
        
    });

    function loadCounty() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#countyId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '区县名称'
                    });
                }
            }
        });
    }

    function loadFunRoomEvaluateProject() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/GetProjectComboJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#funRoomEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        $('#funRoomEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    loadData();
                }
            }
        });
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }

    function loadLevel() {
        $('#level').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=@DicTypeCodeEnum.StandardLevel.ParseToInt()",
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '达标级别'
        });
        }

    function loadOneClass() {
        $("#oneClassId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.FunRoomClass.ParseToInt()&OptType=7&Pid=0',
            defaultName: '一级分类',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function searchGrid() {
        if ($('#funRoomEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            loadData();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }

    function resetGrid() {
        //清空条件
        $('#schoolStageId').ysComboBox('setValue', -1);
        $('#level').ysComboBox('setValue', -1);
        $('#oneClassId').ysComboBox('setValue', -1);
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            $('#schoolId').ysComboBox('setValue', -1);
        }
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', -1);
        }
        loadData();
    }

    function loadData() {
        var funRoomEvaluateProjectId = $('#funRoomEvaluateProjectId').ysComboBox('getValue');
        if (!funRoomEvaluateProjectId) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        $('#container').html('<div id="container"><div id = "container_0" style = "height:300px;margin-bottom: 50px; margin-bottom: 30px;" ></div ><div id="container_1" style="height:300px;margin-bottom: 50px; margin-bottom: 30px;"></div><div id="container_2" style="height:300px;margin-bottom: 50px; margin-bottom: 30px;"></div></div >');

        var queryUrl = '';
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            queryUrl = '@Url.Content("~/EvaluateManage/FunRoomAttendStatic/GetCityFunRoomStandardAnalysisListJson")' + '?FunRoomEvaluateProjectId=' + funRoomEvaluateProjectId + '&SchoolStageId=' + $('#schoolStageId').ysComboBox('getValue') + '&Level=' + $('#level').ysComboBox('getValue') + '&OneClassId=' + $('#oneClassId').ysComboBox('getValue') + '&CountyId='+ $('#countyId').ysComboBox('getValue')
        }
        else {
            queryUrl = '@Url.Content("~/EvaluateManage/FunRoomAttendStatic/GetCountyFunRoomStandardAnalysisListJson")' + '?FunRoomEvaluateProjectId=' + funRoomEvaluateProjectId + '&SchoolStageId=' + $('#schoolStageId').ysComboBox('getValue') + '&Level=' + $('#level').ysComboBox('getValue') + '&OneClassId=' + $('#oneClassId').ysComboBox('getValue')
        }
        ys.ajax({
            url: queryUrl,
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $.each(obj.Data, function (index, v) {
                        let courseNameList = []; //学科名称数组
                        let roomStandardList = []; //实验专用室数量达标值数组
                        let areaStandardList = []; //实验专用室面积达标值数组
                        $.each(v.CourseList, function (k, course) {
                            courseNameList.push(course.CourseName);
                            roomStandardList.push(course.RoomStandardRate);
                            areaStandardList.push(course.AreaStandardRate);
                        });

                        var dom = document.getElementById('container_' + index);
                        var myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        var option;

                        option = {
                            title: {
                                text: v.StageName,
                                x: 'center',
                                textStyle: {
                                    color: '#8D8D8D',
                                    fontSize: 14
                                },
                                textAlign: 'left'
                            },
                            tooltip: {
                                trigger: 'axis',
                                formatter: function (params) {
                                    var relVal = params[0].name + '<br/>';
                                    for (var i = 0, l = params.length; i < l; i++) {
                                        relVal += params[i].seriesName + ' : ' + params[i].value + '%' + '<br/>';
                                    }
                                    return relVal;
                                }
                            },
                            legend: {
                                data: ['数量', '面积'],
                                x: 'center',
                                y: 'bottom',
                            },
                            toolbox: {
                                show: true,

                            },
                            calculable: true,
                            xAxis: {
                                type: 'category',
                                data: courseNameList ,//['科学', '音乐', '美术', '书法', '舞蹈', '体育', '心理健康', '信息技术', '劳动技术', '语文', '数学', '外语', '道德与法治'],
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: {
                                    show: true,
                                    interval: 'auto',
                                    formatter: '{value} %'
                                },
                                show: true
                            },
                            series: [
                                {
                                    name: '数量',
                                    type: 'bar',
                                    barWidth: 40,//柱图宽度
                                    data: roomStandardList,   //[2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 35.6, 62.2],
                                    color: '#5C9BD1',
                                    itemStyle: {
                                        normal: {
                                            label: {
                                                show: true,		//开启显示
                                                position: 'top',	//在上方显示
                                                textStyle: {	    //数值样式
                                                    color: '#333',
                                                    fontSize: 12
                                                },
                                                formatter: function (value) {
                                                    return value.data + "%";
                                                }
                                            }
                                        }
                                    },

                                },
                                {
                                    name: '面积',
                                    type: 'bar',
                                    barWidth: 40,//柱图宽度
                                    data: areaStandardList,//[2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 75.6, 82.2],
                                    color: '#ED7D31',
                                    itemStyle: {
                                        normal: {
                                            label: {
                                                show: true,		//开启显示
                                                position: 'top',	//在上方显示
                                                textStyle: {	    //数值样式
                                                    color: '#333',
                                                    fontSize: 12
                                                },
                                                formatter: function (value) {
                                                    return value.data + "%";
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        };

                       if (option && typeof option === 'object') {
                            myChart.setOption(option);
                       }
                    });
                }
            }
        });
    }

    </script>
