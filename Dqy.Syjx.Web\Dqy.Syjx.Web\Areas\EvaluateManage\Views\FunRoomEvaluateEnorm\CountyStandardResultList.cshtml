﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="funRoomEvaluateProjectId" col="funRoomEvaluateProjectId" style="display:inline-block;width:180px;"></span>
                    </li>
                    <li>
                        <span id="schoolId" col="schoolId" style="display:inline-block;width:180px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="level" col="Level" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="oneClassId" col="OneClassId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="指标名称" style="width:150px" />
                    </li>
                    <li>
                        <div id="isOnlyShowNoStandard" col="IsOnlyShowNoStandard" style="display:inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        $('#isOnlyShowNoStandard').ysCheckBox({
            data: [{ Key: 1, Value: '只显示不达标' }]
        });

        loadFunRoomEvaluateProjectIdId();
        loadSchoolStage();
        loadSubject();
        loadOneClass();
        $('#level').ysComboBox({ url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1011", key: 'DictionaryId', value: 'DicName',defaultName:'达标级别' });
        
        
    });

    function initGrid() {
        if (!$('#funRoomEvaluateProjectId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        var queryUrl = '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/GetStandardPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { field: 'SchoolStageName', title: '学段', halign: 'center', align: 'center', sortable: true },
                { field: 'OneClassName', title: '一级分类', halign: 'center', align: 'center', sortable: true },
                { field: 'TargetName', title: '指标名称', halign: 'center', align: 'center', sortable: true },
                { field: 'CourseName', title: '适用学科', halign: 'center', align: 'center', sortable: true },
                { field: 'RoomNum', title: '指标数量（间）', halign: 'center', align: 'center', sortable: true },
                { field: 'FunRoomNum', title: '实际数量（间）', halign: 'center', align: 'center', sortable: true },
                {
                    field: 'RoomNumDifference', title: '数量差额', halign: 'center', align: 'center', sortable: true,
                    formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                },
                { field: 'RoomArea', title: '指标面积（㎡）', halign: 'center', align: 'center', sortable: true },
                { field: 'UseArea', title: '实际面积（㎡）', halign: 'center', align: 'center', sortable: true },
                {
                    field: 'RoomAreaDifference', title: '面积差额', halign: 'center', align: 'center', sortable: true ,
                    formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function loadFunRoomEvaluateProjectIdId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/GetProjectComboJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#funRoomEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        $('#funRoomEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    loadSchool();
                }
            }
        });
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }

    function loadSubject() {
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadOneClass() {
        $("#oneClassId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.FunRoomClass.ParseToInt()&OptType=7&Pid=0',
            defaultName: '一级分类',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadSchool() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000",
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '学校名称'
                    });
                    $('#schoolId').ysComboBox('setValue', obj.Data[0].Id);

                    initGrid();
                }
            }
        });
    }

    function searchGrid() {
        if ($('#funRoomEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }

    function resetGrid() {
        //清空条件
        $('#schoolStageId').ysComboBox('setValue', -1);
        $('#level').ysComboBox('setValue', -1);
        $('#oneClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>
