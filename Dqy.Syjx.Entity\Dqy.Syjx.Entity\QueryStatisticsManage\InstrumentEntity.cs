﻿using Dqy.Syjx.Entity.BusinessManage;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using NPOI.SS.UserModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Dqy.Syjx.Entity.QueryStatisticsManage
{
    public class InstrumentEntity: BaseExtensionEntity
    {
        /// <summary>
        /// 仪器分类Id（对应仪器分类标准库eq_InstrumentStandard表Id）
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long InstrumentStandardId { get; set; }

        /// <summary>
        /// 仪器名称
        /// </summary>
        /// <returns></returns>
        [Description("仪器名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 20)]
        public string Name { get; set; }

        /// <summary>
        /// 分类代码
        /// </summary>
        [Description("分类代码")]
        public string Code { get; set; }

        /// <summary>
        /// 规格属性Id（对应仪器分类标准库eq_InstrumentStandard表Id）
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long ModelStandardId { get; set; }

        /// <summary>
        /// 功能室Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long FunRoomId { get; set; }

        /// <summary>
        /// 专用室橱柜Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long CupboardId { get; set; }

        /// <summary>
        /// 规格属性
        /// </summary>
        /// <returns></returns>
        [Description("规格属性")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string Model { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        /// <returns></returns>
        [Description("单位")]
        [ExportExcelAttribute(HorizontalAlignment.Center,10)]
        public string UnitName { get; set; }

        /// <summary>
        /// 录入数量
        /// </summary>
        [Description("录入数量")]
        public decimal InputNum { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        /// <returns></returns>
        [Description("数量")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 10)]
        public decimal Num { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        /// <returns></returns>
        [Description("单价")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 10)]
        public decimal Price { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [Description("金额")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 12)]
        public decimal SumMoney { get; set; }

        /// <summary>
        /// 适用学段
        /// </summary>
        /// <returns></returns>
        [Description("适用学段")]
        public string Stage { get; set; }


        /// <summary>
        /// 适用学科
        /// </summary>
        /// <returns></returns>
        [Description("适用学科")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 12)]
        public string Course { get; set; }

        /// <summary>
        /// 适用学科Id
        /// </summary>
        /// <returns></returns>
        public string CourseId { get; set; }

        /// <summary>
        /// 采购日期
        /// </summary>
        /// <returns></returns>
        [Description("采购日期")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// 保修期（月）
        /// </summary>
        /// <returns></returns>
        [Description("保修期（月）")]
        public int? WarrantyMonth { get; set; }

        /// <summary>
        /// 供应商全称
        /// </summary>
        /// <returns></returns>
        [Description("供应商全称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        /// <returns></returns>
        [Description("品牌")]
        public string Brand { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        /// <returns></returns>
        public int Statuz { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SchoolId { get; set; }

        /// <summary>
        /// 层次
        /// </summary>
        public string Floor { get; set; }


        /// <summary>
        /// 专用室
        /// </summary>
        [NotMapped]
        [Description("存放地")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 15)]
        public string FunRoom { get; set; }

        /// <summary>
        /// 橱柜
        /// </summary>
        [NotMapped]
        public string Cupboard { get; set; }

        /// <summary>
        /// 仪器属性
        /// </summary>
        public string VarietyAttribute { get; set; }

        /// <summary>
        /// 管理部门
        /// </summary>
        [Description("管理部门")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 管理人
        /// </summary>
        [Description("管理人")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 15)]
        public string RealName { get; set; }

        /// <summary>
        /// 管理部门Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? SysDepartmentId { get; set; }

        /// <summary>
        /// 管理人Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long? SysUserId { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        [NotMapped]
        public List<AttachmentEntity> AttachmentList { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        [Description("单位名称")]
        [NotMapped]
        public string SchoolName { get; set; }
        /// <summary>
        /// 单位编号
        /// </summary>
        [NotMapped]
        [Description("单位编号")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string SchoolCode { get; set; }

        /// <summary>
        /// 仪器属性名称
        /// </summary>
        [Description("仪器属性")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Center, 12)]
        public string VarietyAttributeName { get; set; }

        /// <summary>
        /// 是否危化品
        /// </summary>
        [NotMapped]
        public int? IsDangerChemical { get; set; }

        /// <summary>
        /// 区县对应区域名称
        /// </summary>
        [NotMapped]
        public string AreaName { get; set; }

        /// <summary>
        /// 是否自制教具
        /// </summary>
        public int IsSelfMade { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [NotMapped]
        public decimal StockNum { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [NotMapped]
        public decimal LendNum { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [NotMapped]
        public decimal StockSum { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [NotMapped]
        public decimal LendSum { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [NotMapped]
        [Description("序号")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 7)]
        public int RowNumber { get; set; }

        /// <summary>
        /// 原始分类代码(用户输入的分类代码)
        /// </summary>
        [NotMapped]
        public string OriginalCode { get; set; }
    }


    /// <summary>
    /// 安全保障实体
    /// </summary>
    public class SafeGuaranteeEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 学校名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 制度与队伍建设Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long BaseFieldConfigId { get; set; }

        /// <summary>
        /// 培训与安全教育数量
        /// </summary>
        public int TotalEdu { get; set; }

        /// <summary>
        /// 应急预案与演练数量
        /// </summary>
        public int TotalPlan { get; set; }

        /// <summary>
        /// 培训与安全教育最近实施时间
        /// </summary>
        public DateTime LastEduTime { get; set; }

        /// <summary>
        /// 应急预案与演练最近实施时间
        /// </summary>
        public DateTime LastPlanTime { get; set; }
    }

    /// <summary>
    /// 减少仪器统计实体
    /// </summary>
    public class ReduceInstrumentEntity
    {
        /// <summary>
        /// 用户Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long UserId { get; set; }

        /// <summary>
        /// 分类代码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 学校名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long SchoolId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Num { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        public DateTime RegData { get; set; }

        /// <summary>
        /// 仪器名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 计量单位
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 总价
        /// </summary>
        public decimal SumMoney { get; set; }


        /// <summary>
        /// 适用学科
        /// </summary>
        /// <returns></returns>
        public string Course { get; set; }

        /// <summary>
        /// 仪器属性
        /// </summary>
        public string VarietyAttribute { get; set; }

        /// <summary>
        /// 管理部门
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 管理人
        /// </summary>
        public string RealName { get; set; }

        /// <summary>
        /// 仪器属性名称
        /// </summary>
        public string VarietyAttributeName { get; set; }

    }


    /// <summary>
    /// 区县存量仪器实体
    /// </summary>
    public class CountyInstrumentEntity
    {

        /// <summary>
        /// 单位Id
        /// </summary>
        [JsonConverter(typeof(StringJsonConverter))]
        public long Id { get; set; }

        /// <summary>
        /// 学校Id
        /// </summary>
        public string SchoolId { get; set; }

        /// <summary>
        /// 排序值
        /// </summary>
        public int Sort { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        public string SchoolName { get; set; }

        /// <summary>
        /// 存量
        /// </summary>
        public decimal StockNum { get; set; } = 0;

        /// <summary>
        /// 
        /// </summary>
        public decimal LendNum { get; set; } = 0;

        /// <summary>
        /// 新增量/待入库数量
        /// </summary>
        public decimal Num { get; set; } = 0;

        /// <summary>
        /// 减少量
        /// </summary>
        public decimal OutNum { get; set; }


        /// <summary>
        /// 增加量
        /// </summary>
        public decimal AddNum { get; set; }
    }
}
