﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .span-left-tag {
        margin-left: 82px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="CourseId" col="CourseId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" placeholder="课程名称" style="display: inline-block;width:180px;" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnPublish" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 发布计划</a>
            <a id="btnCancel" class="btn btn-success" onclick="showCancelForm(0)"><i class="fa fa-plus"></i> 撤销发布</a>
            <a id="btnLook" class="btn btn-success" onclick="showLookForm()"><i class="fa fa-plus"></i> 查看信息</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    $(function () {
        loadCourse();
        
        initGrid();


    });

    function loadCourse() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data);
                } else {
                    bindCourseData({});
                }
            }
        });
    }
    function bindCourseData(data) {
        $('#CourseId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '课程'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/GetOutPageListedJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                { checkbox: true, visible: true },
                {
                    field: 'SchoolYearStart', title: '学期', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2);
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== value) {
                            html += '@SchoolTermEnum.NextSemester.GetDescription()';
                        } else {
                            html += '@SchoolTermEnum.LastSemester.GetDescription()';
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '适用学科', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'Name', title: '课程名称', sortable: true, width: 300, halign: 'center', valign: 'middle' },
                { field: 'FunRoomName', title: '上课地点', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'ClassTimeStr', title: '上课时间', sortable: true, width: 180, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'StudentNumLimit', title: '学生限额', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'UseNum', title: '实验清单', width: 70, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-info btn-xs" href="#" onclick="openLookDetailForm(\'' + row.Id + '\')"><i class="fa fa-eye"></i>查看</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { field: 'Num', title: '实验数量', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Statuz', title: '发布状态', sortable: true, width: 60, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.StatuzName;
                    }
                },
                { field: 'StudentNumed', title: '已约人数', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'RealName', title: '编制人', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#CourseId").ysComboBox('setValue', -1);
        $("#Name").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    } 
    //查看
    function showLookForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (!ys.checkRowEdit(selectedRow)) {
            return;
        }
        else {
            if (selectedRow.length != 1) {
                ys.msgError('查看只能选择一条数据，请选择一条数据查看。');
                return false;
            }
        }
        ys.openDialog({
            title: '查看信息',
            content: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/QrCodeForm")' + '?id=' + ys.getIds(selectedRow),
            width: '668px',
            height: '440px;',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function openLookDetailForm(id) {
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddDetailList")' + '?id=' + id + "&v=1";
        createMenuItem(url, "实验清单");
    }

    function removeConfirm() {
        $(".layui-layer-btn0").hide();
    }
</script>
