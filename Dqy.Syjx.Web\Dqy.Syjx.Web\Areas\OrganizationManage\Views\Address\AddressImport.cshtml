﻿@{ Layout = "~/Views/Shared/_FormWhite.cshtml"; }

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row">
            <label class="col-sm-2 control-label ">选择文件</label>
            <div class="col-sm-10">
                <input id="importFile" type="file">
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label "> </label>
            <div class="col-sm-10 control-label" style="text-align:left !important;">
                <a href='@Url.Content("~/template/导入地址模板.xlsx")' class="btn btn-secondary btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 control-label "></label>
            <div class="col-sm-10 text-danger">
                提示：仅允许导入“xls”或“xlsx”格式文件！
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var filePath = undefined;
    $(document).ready(function () {
        $("#importFile").fileinput({
            language: 'zh',
            'uploadUrl': '@Url.Content("~/File/UploadExcel")' + '?fileModule=@UploadFileType.Import.ParseToInt()',
            showPreview: false,
            allowedFileExtensions: ['xls', 'xlsx']
        }).on("fileuploaded", function (event, data) {
            var obj = data.response;
            if (obj.Tag == 1) {
                filePath = obj.Data;
            }
            else {
                filePath = '';
            }
        });
    });

    function saveForm(index) {
        if (!filePath) {
            ys.alertError('文件未上传或者上传失败');
            return;
        }

        var postData =$("#form").getWebControls();
        postData.FilePath = filePath;
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Address/ImportAddressJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess('导入成功');
                    parent.$('#gridTable').ysTable('refresh');parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>