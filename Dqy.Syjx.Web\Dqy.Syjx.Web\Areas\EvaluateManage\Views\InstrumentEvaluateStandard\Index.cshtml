﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container{width:100% !important;}
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolStage" col="SchoolStage" style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1005" col="DictionaryId1005" style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="VersionName" col="VersionName" type="text" style="display:inline-block;width:160px;" placeholder="版本名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>
            <a id="btnUpdate" class="btn btn-primary" onclick="showUpdate()"><i class="fa fa-refresh"></i> 仪器达标数据更新</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSchoolStage();
        loadSubject(0);
        initGrid();
        
        
    });
    function loadSchoolStage() {
        $('#SchoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                //var selectid = $('#SchoolStage').ysComboBox('getValue');
                //loadSubject(selectid);
            }
        });
    }
    function loadSubject(selectid) {
        $("#DictionaryId1005").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                //var selectid = $('#DictionaryId1005').ysComboBox('getValue');
                //loadClassA(selectid);
            }

        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/EvaluateManage/InstrumentEvaluateStandard/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { checkbox: true, visible: true },
                { field: 'VersionName', title: '版本名称', width:200, sortable: true, halign: 'center', valign: 'middle', },
                {
                    field: 'SchoolStage', title: '学段', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SchoolStageName;
                    }
                },
                {
                    field: 'DictionaryId1005', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SubjectName;
                    }
                },
                {
                    title: '仪器清单', width: 100, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showListForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>查看</a>&nbsp;');
                        return actions.join('');
                    }
                },
                {
                    title: '操作',
                    halign: 'center',
                    align: 'center',
                    width: 150,
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="updateForm(\'' + row.Id + '\')"><i class="fa fa-refresh"></i>更新</a>&nbsp;');
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $('#SchoolStage').ysComboBox('setValue', '-1');
        $('#DictionaryId1005').ysComboBox('setValue', '-1');
        $("#VersionName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateStandard/Form")' + '?id=' + id,
            width: '768px',
            height: '460px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm(id) {
        var ids = '';
        var tagMsg = '';
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (selectedRow.length == 0) {
                ys.msgError("请选择需要删除的数据。");
                return;
            }
            tagMsg = '确认要删除选中的' + selectedRow.length + '条数据吗？';
            ids = ys.getIds(selectedRow);
        } else {
            ids = id;
            tagMsg = '确认要删除当前这条数据吗？';
        }
        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateStandard/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    function showListForm(id) {
        var url = '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/Index")' + '?id=' + id;
        createMenuItem(url, "仪器清单");
    }

    /**
     * 
     *更新仪器数据
     */
    function showUpdate(){
        ys.confirm("此操作仅用于修复全平台仪器达标数据，请慎重操作！此操作耗时可能较长，操作过程中请勿关闭此页面！", function () {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/BatchCalculateEvaluate")',
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    /**
     * 更新信息
     */
    function updateForm(id) {
        var url = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/SingleCalculateEvaluate")' + '?id=' + id;
        ys.ajax({
            url: url,
            type: 'post',
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>
