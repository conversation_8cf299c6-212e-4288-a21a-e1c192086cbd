﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="instrumentEvaluateProjectId" col="InstrumentEvaluateProjectId" style="display:inline-block;width:250px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                   
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="allocateType" col="AllocateType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnLookBySchool" class="btn btn-success btn-sm" onclick="switchLookType()"><i class="fa fa-navicon"></i>&nbsp;按学校查看</a>
            <a id="btnDetailExport" class="btn btn-warning btn-sm" onclick="exportDetailForm()"><i class="fa fa-download"></i>&nbsp;按学校明细导出</a>
            <a id="btnExport" class="btn btn-warning btn-sm" onclick="exportForm()"><i class="fa fa-download"></i>&nbsp;按仪器导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
     var schoolId = ys.request("schoolId");

    var listLookType = 1; //列表查看类型，1：按学校查看，2：按区县查看
    var SortName = '';  //列表默认排序
    var queryUrl = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/GetGroupPageListJson")'; //查询方法

    var evaluateProjectId = 0;
    var BasePageCode = 101009;//仪器达标结果
    var ShowExportSetBtn = false;
    $(function () {
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            loadSchool(0);
            $('#liSchoolId').show();
            SortName = 'SchoolSort asc ,SchoolId asc';
            ShowExportSetBtn = true;
        }
        else if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            listLookType = 2;
            loadCounty();
            $('#liCountyId').show();
            SortName = 'CountySort asc ,CountyId asc';
            queryUrl  = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/GetCityGroupPageListJson")'; //查询方法
            c = true;
        }
        else {
            SortName = 'StageId asc ,CourseId asc';
        }

        $("#allocateType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(AllocateTypeEnum).EnumToDictionaryString())), defaultName: '配备要求' });

        loadInstrumentEvaluateProjectVersionId();
        loadSchoolStage();
        loadSubject();
        
        
    });

    function initGrid() {
        if (!$('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: SortName,
            showExportSetBtn : ShowExportSetBtn,
            showExportSetCode: BasePageCode,
            exportSetMultipleCode:[{BasePageCode:"101008",Title:"按学校明细导出"},{BasePageCode:"101009",Title:"按仪器导出"}],
            columns: [
                {
                    field: 'opt', title: '达标明细', halign: 'center', align: 'center', width: 50, visible: (listLookType == 1) ,
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(this)" instrumentevaluateprojectId="{0}" allocatetype="{1}" schoolid="{2}" countyid="{3}" stageid="{4}" courseid="{5}"><i class="fa fa-eye"></i> 查看</a> '
                            , row.InstrumentEvaluateProjectId, row.AllocateType, row.SchoolId, row.CountyId, row.StageId, row.CourseId);
                    }
                },
                { field: 'AreaName', title: '区县名称', sortable: true, halign: 'center', align: 'center', visible: (@UnitType == @UnitTypeEnum.City.ParseToInt()), width: 100 },
                { field: 'SchoolName', title: '单位名称', sortable: true, halign: 'center', align: 'left', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt() && listLookType == 1), width: 150 },
                { field: 'StageName', title: '学段', halign: 'center', align: 'center', sortable: true, width: 50 },
                { field: 'CourseName', title: '学科', halign: 'center', align: 'center', sortable: true, width: 80 },
                {
                    field: 'AllocateType', title: '配备要求', halign: 'center', align: 'center', sortable: true, width: 50,
                    formatter: function (value, row, index) {
                        return value == 1 ? '必配' : '选配';
                    }
                },
                {
                    field: 'StandardRate', title: '达标率', halign: 'center', align: 'center', sortable: true, width: 50,
                    formatter: function (value, row, index) {
                        return value + '%';
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function loadInstrumentEvaluateProjectVersionId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectBySchool")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#instrumentEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length> 0) {
                        evaluateProjectId = obj.Data[0].Id;
                        $('#instrumentEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    initGrid();
                }
            }
        });
    }


    function searchGrid() {
        if ($('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }
    function loadSchool(schoolStageId) {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=" + schoolStageId,
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });

                     if (schoolId != undefined){
                        $('#schoolId').ysComboBox('setValue', schoolId);
                    }
                }
            }
        });
    }

    function loadCounty() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#countyId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '区县名称',
                        onChange: function () {
                            if (listLookType == 1)
                                loadSchoolByCountyId($('#countyId').ysComboBox('getValue'));
                        }
                    });
                }
            }
        });
    }

    function loadSchoolByCountyId(countyId) {
        if (countyId > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                data: null,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        $('#schoolId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Name',
                            defaultName: '单位名称'
                        });
                    }
                }
            });
        }
        else {
            $('#schoolId').ysComboBox({ defaultName: '单位名称' });
        }
    }

    function resetGrid() {
        //清空条件
        if (@UnitType != @UnitTypeEnum.School.ParseToInt()) {
            $('#schoolId').ysComboBox('setValue', -1);
            if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                loadSchool(0);
            }
        }
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', -1);
        }
        $("#schoolStageId").ysComboBox('setValue', -1);
        $("#courseId").ysComboBox('setValue', -1);
        $("#allocateType").ysComboBox('setValue', -1);
        $('#instrumentEvaluateProjectId').ysComboBox('setValue', evaluateProjectId);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();

    }

    //查看达标明细
    function lookForm(obj){
        var instrumentEvaluateProjectId = $(obj).attr('instrumentevaluateprojectId');
        var allocateType = $(obj).attr('allocatetype');
        var schoolId = $(obj).attr('schoolid');
        var countyId = $(obj).attr('countyid');
        var stageId = $(obj).attr('stageid');
        var courseId = $(obj).attr('courseid');

        var url = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/InstrumentAttendStaticIndex")' + '?instrumentEvaluateProjectId=' + instrumentEvaluateProjectId + '&allocateType=' + allocateType
            + '&schoolId=' + schoolId + '&countyId=' + countyId + '&stageId=' + stageId + '&courseId=' + courseId;

        if (schoolId != undefined) {
            createNewMenuItem(url, "查看达标明细");
        }else{
            createMenuItem(url, "查看达标明细");
        }
    }

    function createNewMenuItem(dataUrl, menuName) {
        var dataIndex = ys.getGuid();
        if (dataUrl == undefined || $.trim(dataUrl).length == 0) return false;
        var topWindow = $(window.parent.parent.document);
        // 关闭跳转的tab
        $('.menuTab', topWindow).each(function () {
            if ($(this).data('id').split('?')[0] == dataUrl.split('?')[0]) {
                // 移除tab对应的内容区
                $('.mainContent .YiSha_iframe', topWindow).each(function () {
                    if ($(this).data('id').split('?')[0] == dataUrl.split('?')[0]) {
                        $(this).remove();
                        return false;
                    }
                });

                // 移除tab
                $(this).remove();
                return false;
            }
        });
        // 选项卡菜单不存在
        var str = '<a href="javascript:;" class="active menuTab" data-id="' + dataUrl + '">' + menuName + ' <i class="fa fa-times-circle"></i></a>';
        $('.menuTab', topWindow).removeClass('active');

        // 添加选项卡对应的iframe
        var str1 = '<iframe class="YiSha_iframe" name="iframe' + dataIndex + '" width="100%" height="100%" src="' + dataUrl + '" frameborder="0" data-id="' + dataUrl + '" seamless></iframe>';
        $('.mainContent', topWindow).find('iframe.YiSha_iframe').hide().parents('.mainContent').append(str1);

        // 添加选项卡
        $('.menuTabs .page-tabs-content', topWindow).append(str);
        return false;
    }

    //市级查询时，切换按学校/区县查看
    function switchLookType() {
        //列表查看类型，1：按学校查看，2：按区县查看
        if (listLookType == 1) {
            listLookType = 2;
            SortName = 'CountySort asc ,CountyId asc';
            queryUrl = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/GetCityGroupPageListJson")'; //查询方法
            $('#liSchoolId').hide();
            $('#btnLookBySchool').html('<i class="fa fa-navicon"></i>&nbsp;按学校查看');
        }
        else {
            listLookType = 1;
            SortName = 'CountySort asc ,CountyId asc ,SchoolSort asc ,SchoolId asc';
            queryUrl = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/GetGroupPageListJson")'; //查询方法
            $('#liSchoolId').show();
            loadSchoolByCountyId($('#countyId').ysComboBox('getValue'));
            $('#btnLookBySchool').html('<i class="fa fa-navicon"></i>&nbsp;按区县查看');
        }
        $('#gridTable').bootstrapTable('destroy');
        initGrid();
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                    var schoolStageId = $('#schoolStageId').ysComboBox("getValue");
                    if (schoolStageId > 0) {
                        loadSchool(schoolStageId);
                    }
                }
            }
        });
    }

    function loadSubject() {
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }


    function exportDetailForm(){
        var url = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/ExportDetailStatic")';//仪器达标结果(101008)
        var postData = $("#searchDiv").getWebControls();
             postData.BasePageCode = 101008;
        ys.exportExcel(url, postData);
    }

    function exportForm() {
        var url = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/ExportInstrumentStatic")';//仪器达标结果(101009)
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = BasePageCode;//仪器达标结果
        ys.exportExcel(url, postData);
    }
</script>
