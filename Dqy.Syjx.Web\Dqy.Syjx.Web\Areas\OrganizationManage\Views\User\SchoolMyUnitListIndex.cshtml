﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.js"))


}
@{
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
}

<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

</style>
<div class="ui-layout-west">
    <div class="main-content">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    所属部门
                </div>
                <div class="box-tools pull-right">
                    <a type="button" class="btn btn-box-tool menuItem" href="#" onclick="showDepartmentForm()" title="管理部门"><i class="fa fa-edit"></i></a>
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div id="departmentTree" class="ztree"></div>
            </div>
        </div>
    </div>
</div>

<div class="container-div ui-layout-center">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <input type="hidden" id="departmentId" col="DepartmentId">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="roleId" col="RoleId" style="display: inline-block;width:150px;"></span>
                    </li>
                    <li>
                        <span id="userStatus" col="UserStatus" style="display: inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="realName" col="RealName" type="text" placeholder="姓名、手机号、登录账号" style="display: inline-block;width:160px;"/>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div id="toolbar" class="btn-group d-flex" role="group">
            @if (operatorInfo.IsThirdLogin == 0)
            {
                <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 添加</a>
            }
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showEditForm()"><i class="fa fa-edit"></i> 修改</a>
            @*<a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>*@

            @if (operatorInfo.IsThirdLogin == 0)
            {
                <a id="btnImport" class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a>
            }

            <a id="btnDelete" class="btn btn-warning disabled" onclick="showDepartForm()"><i class="fa fa-edit"></i> 批量设置所属部门</a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        showAddImpByThird();

        initGrid();

        initTree();

        $('body').layout({ west__size: 185 });

        $("#userStatus").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())),
            defaultName: '用户状态',
        });


        $("#roleId").ysComboBox({
            url: '@Url.Content("~/SystemManage/Role/GetRoleListJson")',
            key: "Id",
            value: "RoleName",
            defaultName: '用户角色',
        });

        $('#btnExpand').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });



        $('#btnRefresh').click(function () {
            initTree();
        });

        
        
    });

   function initTree() {
        $('#departmentTree').ysTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            async: true,
            expandLevel: 2,
            maxHeight: "700px",
            callback: {
                onClick: function (event, treeId, treeNode) {
                    $("#departmentId").val(treeNode.id);
                    searchGrid();
                }
            }
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/User/GetUserListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                {
                    title: '操作',
                    align: 'center', width: 120, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        //actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;&nbsp;');
                        //actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="showResetPasswordForm(\'' + row.Id + '\')"><i class="fa fa-key"></i>重置</a>&nbsp;&nbsp;');
                        if (row.UserStatus == "@StatusEnum.Yes.ParseToInt()") {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'禁用\')"><i class="fa fa-check"></i>禁用</a>&nbsp;&nbsp;');
                        } else {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'启用\')"><i class="fa fa-check"></i>启用</a>&nbsp;&nbsp;');
                        }
                        //actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="DelUser(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;');
                        actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="UnlockUser(\'' + row.Id + '\')"><i class="fa fa-key"></i>解锁</a>');
                        return actions.join('');
                    }
                },
                { field: 'DepartmentNames', title: '所属部门',width:120, halign: 'center', align: 'center' },
                { field: 'RealName', title: '姓名', width: 120, sortable: true, halign: 'center', align: 'center',},
                { field: 'Mobile', title: '手机号', width: 100, sortable: true, halign: 'center', align: 'center', },
                { field: 'UserName', title: '登录账号', width: 120, sortable: true, halign: 'center', align: 'center',},
                { field: 'RoleNames', title: '用户角色', width: 200, halign: 'center', align: 'center',},
                {
                    field: 'UserStatus', title: '用户状态', width: 80, sortable: true, halign: 'center', align: 'center', formatter: function (value, row, index) {
                        if (row.UserStatus == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $("#searchDiv").getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function clearGrid() {
        $('#roleId').ysComboBox('setValue', -1)
        $('#userStatus').ysComboBox('setValue', -1)
        initTree();
        $("#departmentId").val(null);
        $("#realName").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(id) {
        ys.openDialog({
            title: id > 0 ? "编辑用户" : "添加用户",
            height: "600px",
            content: '@Url.Content("~/OrganizationManage/User/SchoolMyUnitListForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showEditForm() {
        var id = 0;
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        if (!ys.checkRowEdit(selectedRow)) {
            return;
        }
        else {
            id = selectedRow[0].Id;
        }
      ys.openDialog({
            title: id > 0 ? "编辑用户" : "添加用户",
            height: "600px",
            content: '@Url.Content("~/OrganizationManage/User/SchoolMyUnitListForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function ChangeStatuz(Id, strStatuz) {
        ys.confirm("确认要" + strStatuz +"吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/UpdateStatuzFormJson")' + '?id=' + Id,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(strStatuz+" 成功");
                            searchGrid();
                        }
                        else {
                            ys.msgError(strStatuz + " 失败!"+obj.Message);
                        }
                    }
                });
        });
    }

    function DelUser(id) {
         ys.confirm("确认要删除此条数据吗？", function () {
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/User/DeleteUserFormJsonById")' + '?id=' + id,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
    }

    function deleteForm() {
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm("确认要删除选中的" + selectedRow.length + "条数据吗？", function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/User/DeleteUserFormJson")' + '?ids=' + ids,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    //批量修改所属部门
    function showDepartForm() {
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        if (ys.checkRowDelete(selectedRow)) {
            var ids = ys.getIds(selectedRow);
            ys.openDialog({
                title: "批量修改部门",
                height: "600px",
                content: '@Url.Content("~/OrganizationManage/User/DepartmentForm")' + '?ids=' + ids,
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }
    }


    function showDepartmentForm() {
        var url = '@Url.Content("~/OrganizationManage/Department/DepartmentIndex")';
        createMenuItem(url, "部门管理");
    }

    function importForm() {
      ys.openDialog({
            title: "导入用户数据",
            content: '@Url.Content("~/OrganizationManage/User/SchoolMyUnitImport")',
            height: "280px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showResetPasswordForm(id) {
        ys.openDialog({
            title: "重置密码",
            content: '@Url.Content("~/OrganizationManage/User/ResetPassword")' + '?id=' + id,
            height: "220px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showAddImpByThird() {
        //ys.ajax({
        //    url: '@Url.Content("~/OrganizationManage/User/IsThirdUser")',
        //    type: "get",
        //    error: ys.ajaxError,
        //    success: function (obj) {
        //        if (obj.Tag == 1) {
        //            if (obj.Data == 1) {
        //                $("#btnAdd").remove();
        //                $("#btnImport").remove();
        //            }
        //        }
        //        else {
        //            ys.msgError(obj.Message);
        //        }
        //    }
        //});
    }

    function UnlockUser(id) {
        ys.confirm("确认要解锁此用户吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/SchoolUnlockUser")' + '?id=' + id,
                type: "post",
                error: ys.ajaxError,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

</script>
