﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }
<style type="text/css">
    .select2-container { width: 100% !important; }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="UnitType" col="UnitType" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" type="text" placeholder="单位名称、单位编码" style="display: inline-block;width:200px;"/>
                    </li>
                    <li>
                        @await Html.PartialAsync("/Areas/OrganizationManage/Shared/EnableDisableIndexPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" role="group">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,'0')"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary" onclick="showSaveForm(false,'0')"><i class="fa fa-edit"></i> 修改</a>
            @*<a id="btnDelete" class="btn btn-danger" onclick="deleteForm('0')"><i class="fa fa-remove"></i> 删除</a>*@
           @*  <a class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a> *@
            <a id="btnSync" class="btn btn-info" onclick="syncOrg()"><i class="fa fa-random"></i> 同步连云港海州区组织机构</a>
            <a id="btnSync" class="btn btn-info" onclick="syncWxsOrg()"><i class="fa fa-random"></i> 同步无锡市组织机构</a>
            <a id="btnSync" class="btn btn-info" onclick="syncWxsSchool()"><i class="fa fa-random"></i> 同步无锡市学校</a>

        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        $("#UnitType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(UnitTypeEnum).EnumToDictionaryString())), defaultName: '单位类型' });
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/Unit/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Sort',
            sortOrder: 'ASC',
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'UnitType', title: '单位类型', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id!=undefined) {
                            html = '--';
                            if (row.UnitType == 1) {
                                html = '市级';
                            } else if (row.UnitType == 2) {
                                html = '区县';
                            } else if (row.UnitType == 3) {
                                html = '学校';
                            } else if (row.UnitType == 0) {
                                html = '系统';
                            }
                        }
                        return html;
                    }
                },
                { field: 'Code', title: '单位编号', sortable: true, width:120, halign: 'center', valign: 'middle', align: 'center'},
                { field: 'Name', title: '单位名称', sortable: true, halign: 'center', valign: 'middle' },
                { field: 'Sort', title: '排序号', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Statuz', title: '单位状态', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html = '--';
                            if (value > 0) {
                                html = top.getDataDictValue('EnableStatuz', value);
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'opt1', title: '操作', width: 120, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html += $.Format('&nbsp;<a onclick="showSaveForm(false,\'{0}\');">修改</a>&nbsp;', row.Id);
                            html += $.Format('&nbsp;<a onclick="showSortForm(\'{0}\');">排序</a>&nbsp;', row.Id);
                            if (row.Statuz==1) {
                                html += $.Format('&nbsp;<a onclick="ShowIsEnableForm(2,\'{0}\');">禁用</a>&nbsp;', row.Id);
                            } else {
                                html += $.Format('&nbsp;<a onclick="ShowIsEnableForm(1,\'{0}\');">启用</a>&nbsp;', row.Id);
                            }

                            //html += $.Format('&nbsp;<a onclick="deleteForm(\'{0}\');">删除</a>&nbsp;', row.Id);
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() { 
        $("#Name").val('');
        $("#UnitType").ysComboBox('setValue', -1);
        $("#Statuz").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,editid) {
        var id = 0;
        if (editid == '0' && !bAdd) {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        } else {
            id = editid;
        }
        ys.openDialog({
            title: id > 0 ? "修改单位信息" : "添加单位信息",
            content: '@Url.Content("~/OrganizationManage/Unit/UnitSysForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSortForm(editid) {
        var id = 0;
        if (editid == '0') {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        } else {
            id = editid;
        }
        ys.openDialog({
            title: "设置单位排序信息",
            content: '@Url.Content("~/OrganizationManage/Unit/UnitSortForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(editid) {
        var ids = editid;
        var message = '确认要删除该数据吗？';
        if (editid == '0') {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (ys.checkRowDelete(selectedRow)) {
                message = ("确认要删除选中的" + selectedRow.length + "条数据吗？");
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/DeleteFormJson")' + '?ids=' + ids,
                type: "post",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function ShowIsEnableForm(IsEnable, editid) {
        var ids = editid + '';
        var message = '确定禁用';
        if (IsEnable==1) {
            message = '确定启用';
        }
        if (editid != '0') {
            message += "该单位信息？";
        } else {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (ys.checkRowDelete(selectedRow)) {
                message += ("选中的" + selectedRow.length + "条数据吗？");
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgError("请选择要启用禁用的数据？");
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/SaveStatuzFormJson")' + '?ids=' + ids + '&isenable=' + IsEnable,
                type: "post",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function syncOrg(){ //同步组织机构数据（连云港海州区专用）
        ys.ajax({
            url: '@Url.Content("~/AccountThird/SyncOrgData")',
            type: "post",
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    searchGrid();
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function syncWxsOrg(){ 
        ys.ajax({
            url: '@Url.Content("~/AccountThird/InitWxszhjyOrgan")',
            type: "post",
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    searchGrid();
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function syncWxsSchool(){ 
        ys.ajax({
            url: '@Url.Content("~/AccountThird/InitWxszhjySchools")',
            type: "post",
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    searchGrid();
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>
