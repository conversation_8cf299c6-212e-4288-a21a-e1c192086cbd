{"version": 3, "sources": ["../src/util.js", "../src/index.js"], "names": ["<PERSON><PERSON>", "$", "TRANSITION_END", "[object Object]", "element", "trigger", "supportsTransitionEnd", "Boolean", "transitionEndEmulator", "duration", "called", "this", "one", "setTimeout", "triggerTransitionEnd", "fn", "mmEmulateTransitionEnd", "event", "special", "bindType", "delegateType", "target", "is", "handleObj", "handler", "apply", "arguments", "NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "TRANSITION_DURATION", "<PERSON><PERSON><PERSON>", "toggle", "preventDefault", "triggerElement", "parentTrigger", "subMenu", "Event", "SHOW", "SHOWN", "HIDE", "HIDDEN", "CLICK_DATA_API", "ClassName", "METIS", "ACTIVE", "COLLAPSE", "COLLAPSING", "COLLAPSED", "MetisMenu", "config", "transitioning", "init", "self", "conf", "el", "addClass", "find", "children", "attr", "parents", "has", "not", "on", "e", "eTar", "paRent", "parent", "sibLi", "siblings", "sib<PERSON><PERSON>ger", "hasClass", "removeActive", "setActive", "onTransitionStart", "li", "ul", "length", "show", "removeClass", "hide", "elem", "startEvent", "isDefaultPrevented", "toggleElem", "height", "setTransitioning", "scrollHeight", "offsetHeight", "complete", "onTransitionEnd", "css", "isTransitioning", "removeData", "off", "each", "$this", "data", "undefined", "Error", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;sBAEA,MAAMA,KAAO,CAAEC,IACb,MAAMC,EAAiB,gBAEjBF,EAAO,CACXE,eAAgB,kBAEhBC,qBAAqBC,GACnBH,EAAEG,GAASC,QAAQH,IAGrBI,sBAAqB,IACZC,QAAQL,IAoBnB,SAASM,EAAsBC,GAC7B,IAAIC,GAAS,EAYb,OAVAT,EAAEU,MAAMC,IAAIZ,EAAKE,gBAAgB,KAC/BQ,GAAS,KAGXG,YAAW,KACJH,GACHV,EAAKc,qBAAqBH,QAE3BF,GAEIE,KAWT,OAPEV,EAAEc,GAAGC,uBAAyBR,EAE9BP,EAAEgB,MAAMC,QAAQlB,EAAKE,gBAlCd,CACLiB,SAAUjB,EACVkB,aAAclB,EACdC,OAAOc,GACL,GAAIhB,EAAEgB,EAAMI,QAAQC,GAAGX,MACrB,OAAOM,EACJM,UACAC,QACAC,MAAMd,KAAMe,aA+BhB1B,GAvDI,CAwDVC,GCvDG0B,KAAO,YACPC,SAAW,YACXC,UAAY,aACZC,aAAe,YACfC,mBAAqB9B,EAAEc,GAAGY,MAC1BK,oBAAsB,IAEtBC,QAAU,CACdC,QAAQ,EACRC,gBAAgB,EAChBC,eAAgB,IAChBC,cAAe,KACfC,QAAS,MAGLC,MAAQ,CACZC,KAAM,iBACNC,MAAO,kBACPC,KAAM,iBACNC,OAAQ,mBACRC,eAAgB,4BAGZC,UAAY,CAChBC,MAAO,YACPC,OAAQ,YACRP,KAAM,UACNQ,SAAU,cACVC,WAAY,gBACZC,UAAW,gBAGb,MAAMC,UAEJhD,YAAYC,EAASgD,GACnBzC,KAAKP,QAAUA,EACfO,KAAKyC,OAAS,IACTnB,WACAmB,GAELzC,KAAK0C,cAAgB,KAErB1C,KAAK2C,OAGPnD,OACE,MAAMoD,EAAO5C,KACP6C,EAAO7C,KAAKyC,OACZK,EAAKxD,EAAEU,KAAKP,SAElBqD,EAAGC,SAASb,UAAUC,OAEtBW,EAAGE,KAAK,GAAGH,EAAKnB,iBAAiBQ,UAAUE,UACxCa,SAASJ,EAAKpB,gBACdyB,KAAK,gBAAiB,QAEzBJ,EAAGE,KAAK,GAAGH,EAAKnB,iBAAiBQ,UAAUE,UACxCe,QAAQN,EAAKnB,eACbqB,SAASb,UAAUE,QAEtBU,EAAGE,KAAK,GAAGH,EAAKnB,iBAAiBQ,UAAUE,UACxCe,QAAQN,EAAKnB,eACbuB,SAASJ,EAAKpB,gBACdyB,KAAK,gBAAiB,QAEzBJ,EAAGE,KAAK,GAAGH,EAAKnB,iBAAiBQ,UAAUE,UACxCgB,IAAIP,EAAKlB,SACTsB,SAASJ,EAAKlB,SACdoB,SAAS,GAAGb,UAAUG,YAAYH,UAAUL,QAE/CiB,EACGE,KAAKH,EAAKnB,eACV2B,IAAI,IAAInB,UAAUE,UAClBgB,IAAIP,EAAKlB,SACTsB,SAASJ,EAAKlB,SACdoB,SAASb,UAAUG,UAEtBS,EACGE,KAAKH,EAAKnB,eAEVuB,SAASJ,EAAKpB,gBACd6B,GAAG1B,MAAMK,gBAAgB,SAAUsB,GAClC,MAAMC,EAAOlE,EAAEU,MAEf,GAAmC,SAA/BwD,EAAKN,KAAK,iBACZ,OAGEL,EAAKrB,gBAAwC,MAAtBgC,EAAKN,KAAK,SACnCK,EAAE/B,iBAGJ,MAAMiC,EAASD,EAAKE,OAAOb,EAAKnB,eAC1BiC,EAAQF,EAAOG,SAASf,EAAKnB,eAC7BmC,EAAaF,EAAMV,SAASJ,EAAKpB,gBAEnCgC,EAAOK,SAAS5B,UAAUE,SAC5BoB,EAAKN,KAAK,gBAAiB,SAC3BN,EAAKmB,aAAaN,KAElBD,EAAKN,KAAK,gBAAiB,QAC3BN,EAAKoB,UAAUP,GACXZ,EAAKtB,SACPqB,EAAKmB,aAAaJ,GAClBE,EAAWX,KAAK,gBAAiB,WAIjCL,EAAKoB,mBACPpB,EAAKoB,kBAAkBV,MAK/B/D,UAAU0E,GACR5E,EAAE4E,GAAInB,SAASb,UAAUE,QACzB,MAAM+B,EAAK7E,EAAE4E,GAAIjB,SAASjD,KAAKyC,OAAOd,SAClCwC,EAAGC,OAAS,IAAMD,EAAGL,SAAS5B,UAAUL,OAC1C7B,KAAKqE,KAAKF,GAId3E,aAAa0E,GACX5E,EAAE4E,GAAII,YAAYpC,UAAUE,QAC5B,MAAM+B,EAAK7E,EAAE4E,GAAIjB,SAAS,GAAGjD,KAAKyC,OAAOd,WAAWO,UAAUL,QAC1DsC,EAAGC,OAAS,GACdpE,KAAKuE,KAAKJ,GAId3E,KAAKC,GACH,GAAIO,KAAK0C,eAAiBpD,EAAEG,GAASqE,SAAS5B,UAAUI,YACtD,OAEF,MAAMkC,EAAOlF,EAAEG,GAETgF,EAAanF,EAAEsC,MAAMA,MAAMC,MAGjC,GAFA2C,EAAK9E,QAAQ+E,GAETA,EAAWC,qBACb,OAKF,GAFAF,EAAKd,OAAO1D,KAAKyC,OAAOf,eAAeqB,SAASb,UAAUE,QAEtDpC,KAAKyC,OAAOlB,OAAQ,CACtB,MAAMoD,EAAaH,EAAKd,OAAO1D,KAAKyC,OAAOf,eAAekC,WAAWX,SAAS,GAAGjD,KAAKyC,OAAOd,WAAWO,UAAUL,QAClH7B,KAAKuE,KAAKI,GAGZH,EACGF,YAAYpC,UAAUG,UACtBU,SAASb,UAAUI,YACnBsC,OAAO,GAEV5E,KAAK6E,kBAAiB,GAiBtBL,EACGI,OAAOnF,EAAQ,GAAGqF,cAClB7E,IAAIZ,KAAKE,gBAjBK,KAEVS,KAAKyC,QAAWzC,KAAKP,UAG1B+E,EACGF,YAAYpC,UAAUI,YACtBS,SAAS,GAAGb,UAAUG,YAAYH,UAAUL,QAC5C+C,OAAO,IAEV5E,KAAK6E,kBAAiB,GAEtBL,EAAK9E,QAAQkC,MAAME,WAMlBzB,uBA1KqB,KA6K1Bb,KAAKC,GACH,GACEO,KAAK0C,gBAAkBpD,EAAEG,GAASqE,SAAS5B,UAAUL,MAErD,OAGF,MAAM2C,EAAOlF,EAAEG,GAETgF,EAAanF,EAAEsC,MAAMA,MAAMG,MAGjC,GAFAyC,EAAK9E,QAAQ+E,GAETA,EAAWC,qBACb,OAGFF,EAAKd,OAAO1D,KAAKyC,OAAOf,eAAe4C,YAAYpC,UAAUE,QAE7DoC,EAAKI,OAAOJ,EAAKI,UAAU,GAAGG,aAE9BP,EACGzB,SAASb,UAAUI,YACnBgC,YAAYpC,UAAUG,UACtBiC,YAAYpC,UAAUL,MAEzB7B,KAAK6E,kBAAiB,GAEtB,MAAMG,EAAW,KAEVhF,KAAKyC,QAAWzC,KAAKP,UAGtBO,KAAK0C,eAAiB1C,KAAKyC,OAAOwC,iBACpCjF,KAAKyC,OAAOwC,kBAGdjF,KAAK6E,kBAAiB,GACtBL,EAAK9E,QAAQkC,MAAMI,QAEnBwC,EACGF,YAAYpC,UAAUI,YACtBS,SAASb,UAAUG,YAGF,IAAlBmC,EAAKI,UAA0C,SAAxBJ,EAAKU,IAAI,WAClCF,IAEAR,EACGI,OAAO,GACP3E,IAAIZ,KAAKE,eAAgByF,GACzB3E,uBA/NmB,KAmO1Bb,iBAAiB2F,GACfnF,KAAK0C,cAAgByC,EAGvB3F,UACEF,EAAE8F,WAAWpF,KAAKP,QAASwB,UAE3B3B,EAAEU,KAAKP,SACJuD,KAAKhD,KAAKyC,OAAOf,eAEjBuB,SAASjD,KAAKyC,OAAOhB,gBACrB4D,IAAIzD,MAAMK,gBAEbjC,KAAK0C,cAAgB,KACrB1C,KAAKyC,OAAS,KACdzC,KAAKP,QAAU,KAGjBD,uBAAuBiD,GAErB,OAAOzC,KAAKsF,MAAK,WACf,MAAMC,EAAQjG,EAAEU,MAChB,IAAIwF,EAAOD,EAAMC,KAAKvE,UACtB,MAAM4B,EAAO,IACRvB,WACAiE,EAAMC,UACa,iBAAX/C,GAAuBA,EAASA,EAAS,IAQtD,GALK+C,IACHA,EAAO,IAAIhD,UAAUxC,KAAM6C,GAC3B0C,EAAMC,KAAKvE,SAAUuE,IAGD,iBAAX/C,EAAqB,CAC9B,QAAqBgD,IAAjBD,EAAK/C,GACP,MAAM,IAAIiD,MAAM,oBAAoBjD,MAEtC+C,EAAK/C,UAWbnD,EAAEc,GAAGY,MAAQwB,UAAUmD,gBACvBrG,EAAEc,GAAGY,MAAM4E,YAAcpD,UACzBlD,EAAEc,GAAGY,MAAM6E,WAAa,KAEtBvG,EAAEc,GAAGY,MAAQI,mBACNoB,UAAUmD", "sourcesContent": ["import $ from 'jquery';\n\nconst Util = (($) => { // eslint-disable-line no-shadow\n  const TRANSITION_END = 'transitionend';\n\n  const Util = { // eslint-disable-line no-shadow\n    TRANSITION_END: 'mmTransitionEnd',\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(TRANSITION_END);\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(TRANSITION_END);\n    },\n  };\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: TRANSITION_END,\n      delegateType: TRANSITION_END,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event\n            .handleObj\n            .handler\n            .apply(this, arguments); // eslint-disable-line prefer-rest-params\n        }\n        return undefined;\n      },\n    };\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false;\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true;\n    });\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this);\n      }\n    }, duration);\n\n    return this;\n  }\n\n  function setTransitionEndSupport() {\n    $.fn.mmEmulateTransitionEnd = transitionEndEmulator; // eslint-disable-line no-param-reassign\n    // eslint-disable-next-line no-param-reassign\n    $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent();\n  }\n\n  setTransitionEndSupport();\n\n  return Util;\n})($);\n\nexport default Util;\n", "import $ from 'jquery';\nimport Util from './util';\n\nconst NAME = 'metisMenu';\nconst DATA_KEY = 'metisMenu';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst DATA_API_KEY = '.data-api';\nconst JQUERY_NO_CONFLICT = $.fn[NAME];\nconst TRANSITION_DURATION = 350;\n\nconst Default = {\n  toggle: true,\n  preventDefault: true,\n  triggerElement: 'a',\n  parentTrigger: 'li',\n  subMenu: 'ul',\n};\n\nconst Event = {\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  CLICK_DATA_API: `click${EVENT_KEY}${DATA_API_KEY}`,\n};\n\nconst ClassName = {\n  METIS: 'metismenu',\n  ACTIVE: 'mm-active',\n  SHOW: 'mm-show',\n  COLLAPSE: 'mm-collapse',\n  COLLAPSING: 'mm-collapsing',\n  COLLAPSED: 'mm-collapsed',\n};\n\nclass MetisMenu {\n  // eslint-disable-line no-shadow\n  constructor(element, config) {\n    this.element = element;\n    this.config = {\n      ...Default,\n      ...config,\n    };\n    this.transitioning = null;\n\n    this.init();\n  }\n\n  init() {\n    const self = this;\n    const conf = this.config;\n    const el = $(this.element);\n\n    el.addClass(ClassName.METIS); // add metismenu class to element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the trigger element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .addClass(ClassName.ACTIVE);\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the triggers of all parents\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`);\n\n    el\n      .find(conf.parentTrigger)\n      .not(`.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(ClassName.COLLAPSE);\n\n    el\n      .find(conf.parentTrigger)\n      // .has(conf.subMenu)\n      .children(conf.triggerElement)\n      .on(Event.CLICK_DATA_API, function (e) { // eslint-disable-line func-names\n        const eTar = $(this);\n\n        if (eTar.attr('aria-disabled') === 'true') {\n          return;\n        }\n\n        if (conf.preventDefault && eTar.attr('href') === '#') {\n          e.preventDefault();\n        }\n\n        const paRent = eTar.parent(conf.parentTrigger);\n        const sibLi = paRent.siblings(conf.parentTrigger);\n        const sibTrigger = sibLi.children(conf.triggerElement);\n\n        if (paRent.hasClass(ClassName.ACTIVE)) {\n          eTar.attr('aria-expanded', 'false');\n          self.removeActive(paRent);\n        } else {\n          eTar.attr('aria-expanded', 'true');\n          self.setActive(paRent);\n          if (conf.toggle) {\n            self.removeActive(sibLi);\n            sibTrigger.attr('aria-expanded', 'false');\n          }\n        }\n\n        if (conf.onTransitionStart) {\n          conf.onTransitionStart(e);\n        }\n      });\n  }\n\n  setActive(li) {\n    $(li).addClass(ClassName.ACTIVE);\n    const ul = $(li).children(this.config.subMenu);\n    if (ul.length > 0 && !ul.hasClass(ClassName.SHOW)) {\n      this.show(ul);\n    }\n  }\n\n  removeActive(li) {\n    $(li).removeClass(ClassName.ACTIVE);\n    const ul = $(li).children(`${this.config.subMenu}.${ClassName.SHOW}`);\n    if (ul.length > 0) {\n      this.hide(ul);\n    }\n  }\n\n  show(element) {\n    if (this.transitioning || $(element).hasClass(ClassName.COLLAPSING)) {\n      return;\n    }\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.SHOW);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).addClass(ClassName.ACTIVE);\n\n    if (this.config.toggle) {\n      const toggleElem = elem.parent(this.config.parentTrigger).siblings().children(`${this.config.subMenu}.${ClassName.SHOW}`);\n      this.hide(toggleElem);\n    }\n\n    elem\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n      .height(0);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`)\n        .height('');\n\n      this.setTransitioning(false);\n\n      elem.trigger(Event.SHOWN);\n    };\n\n    elem\n      .height(element[0].scrollHeight)\n      .one(Util.TRANSITION_END, complete)\n      .mmEmulateTransitionEnd(TRANSITION_DURATION);\n  }\n\n  hide(element) {\n    if (\n      this.transitioning || !$(element).hasClass(ClassName.SHOW)\n    ) {\n      return;\n    }\n\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.HIDE);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).removeClass(ClassName.ACTIVE);\n    // eslint-disable-next-line no-unused-expressions\n    elem.height(elem.height())[0].offsetHeight;\n\n    elem\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      if (this.transitioning && this.config.onTransitionEnd) {\n        this.config.onTransitionEnd();\n      }\n\n      this.setTransitioning(false);\n      elem.trigger(Event.HIDDEN);\n\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE);\n    };\n\n    if (elem.height() === 0 || elem.css('display') === 'none') {\n      complete();\n    } else {\n      elem\n        .height(0)\n        .one(Util.TRANSITION_END, complete)\n        .mmEmulateTransitionEnd(TRANSITION_DURATION);\n    }\n  }\n\n  setTransitioning(isTransitioning) {\n    this.transitioning = isTransitioning;\n  }\n\n  dispose() {\n    $.removeData(this.element, DATA_KEY);\n\n    $(this.element)\n      .find(this.config.parentTrigger)\n      // .has(this.config.subMenu)\n      .children(this.config.triggerElement)\n      .off(Event.CLICK_DATA_API);\n\n    this.transitioning = null;\n    this.config = null;\n    this.element = null;\n  }\n\n  static jQueryInterface(config) {\n    // eslint-disable-next-line func-names\n    return this.each(function () {\n      const $this = $(this);\n      let data = $this.data(DATA_KEY);\n      const conf = {\n        ...Default,\n        ...$this.data(),\n        ...(typeof config === 'object' && config ? config : {}),\n      };\n\n      if (!data) {\n        data = new MetisMenu(this, conf);\n        $this.data(DATA_KEY, data);\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined) {\n          throw new Error(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = MetisMenu.jQueryInterface; // eslint-disable-line no-param-reassign\n$.fn[NAME].Constructor = MetisMenu; // eslint-disable-line no-param-reassign\n$.fn[NAME].noConflict = () => {\n  // eslint-disable-line no-param-reassign\n  $.fn[NAME] = JQUERY_NO_CONFLICT; // eslint-disable-line no-param-reassign\n  return MetisMenu.jQueryInterface;\n};\n\nexport default MetisMenu;\n"]}