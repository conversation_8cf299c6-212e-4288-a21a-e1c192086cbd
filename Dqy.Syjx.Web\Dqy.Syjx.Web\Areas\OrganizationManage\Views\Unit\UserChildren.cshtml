﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        单位性质：<input id="searchNature" type="text" />
                    </li>
                    <li>
                        单位属性：<input id="departmentName" col="DepartmentName" type="text" />
                    </li>
                    <li>
                        单位名称：<input id="searchName" type="text" />
                    </li>
                    <li>
                        单位状态：<input id="searchStatuz" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchTreeGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" role="group">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
            <a class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Id',
            sortOrder: 'Desc',
            columns: [
                { checkbox: true, visible: true },
                { field: 'Id', title: 'Id', visible: false },
                {
                    field: 'SchoolNature', title: '单位性质', formatter: function (value, row, index) {
                        //var html = '';
                        //if (row.Id!=undefined) {
                        //    html = '--';
                        //    if (row.SchoolNature == 1) {
                        //        html = '公办';
                        //    } else if (row.SchoolNature == 1) {
                        //        html = '民办';
                        //    }
                        //}
                        //return html;
                    }
                },
                { field: 'Mobile', title: '单位名称' },
                { field: 'Sort', title: '排序号' },
                {
                    field: 'Statuz', title: '单位状态', formatter: function (value, row, index) {
                        //var html = '';
                        //if (row.Id != undefined) {
                        //    html = '--';
                        //    if (value == 1) {
                        //        html = '启用';
                        //    } else if (value == 2) {
                        //        html = '禁用';
                        //    }
                        //}
                        //return html;
                    }
                },
                {
                    field: 'NewsType', title: '文章类型', formatter: function (value, row, index) {
                     /*   return top.getDataDictValue('NewsType', value);*/
                    }
                },
                {
                    field: 'opt1', title: '操作', formatter: function (value, row, index) {
                        //var html = '';
                        //if (row.Id != undefined) {
                        //    html += '&nbsp;<a onclick="">修改</a>&nbsp;';
                        //    html += '&nbsp;<a>排序</a>&nbsp;';
                        //    html += '&nbsp;<a>禁用</a>&nbsp;';
                        //    html += '&nbsp;<a>删除</a>&nbsp;';
                        //}
                        //return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? "编辑文章" : "添加文章",
            content: '@Url.Content("~/OrganizationManage/Unit/UnitForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm() {
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm("确认要删除选中的" + selectedRow.length + "条数据吗？", function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/News/DeleteFormJson")' + '?ids=' + ids,
                    type: "post",
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }
</script>
