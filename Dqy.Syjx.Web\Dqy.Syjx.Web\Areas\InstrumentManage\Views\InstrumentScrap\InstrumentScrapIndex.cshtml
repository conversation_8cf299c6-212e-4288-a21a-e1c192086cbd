﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <div id="storagePlace" col="StoragePlace" style="display:inline-block;"></div>
                    </li>
                    <li>
                        <span id="scrapUserId" col="ScrapUserId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="width:150px" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
            <a id="btnAdd" class="btn btn-success" onclick="addForm(true)"><i class="fa fa-plus"></i> 报废登记</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var BasePageCode = 101014;//报废记录(101014)
    $(function () {
        loadCourse();
        loadStoragePlace();
        loadUser();

        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/InstrumentScrap/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                //{ field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80 },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt3,
                    formatter: function (value, row, index) {
                        var html = '';
                        html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="editForm(this)" value="{0}"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                        html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(this)" value="{0}"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                        return html;
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        if (row.IsSelfMade == 1) {
                            html += Syjx.GetSelfMadeHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                {
                    field: 'FunRoomName', title: '存放地', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.FunRoom,
                    formatter: function (value, row, index) {
                        if (row.CupboardName) {
                            value += '>' + row.CupboardName;
                        }
                        if (row.Floor) {
                            value += '>' + row.Floor;
                        }
                        return value;
                    }
                },
                { field: 'ScrapNum', title: '报废数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'ScrapTime', title: '报废时间', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value.substring(0, 10);
                        }
                    }
                },
                {
                    field: 'ScrapContent', title: '报废原因', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'ScrapUserName', title: '登记人', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UserName },
                {
                    field: 'BaseCreateTime', title: '登记时间', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value.substring(0, 10);
                        }
                    }
                },
               
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function addForm() {
        var url = '@Url.Content("~/InstrumentManage/InstrumentScrap/InstrumentScrapForm")' + '?id=0';
        createMenuItem(url, "报废");
    }

    function editForm(obj) {
        var url = '@Url.Content("~/InstrumentManage/InstrumentScrap/InstrumentScrapForm")' + '?id=' + $(obj).attr('value');
        createMenuItem(url, "报废");
    }

    function deleteForm(obj) {
        ys.confirm('确认要删除该数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentScrap/DeleteFormJson")' + '?id=' + $(obj).attr('value'),
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }


    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });
    }

    function loadStoragePlace() {
        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListAllJson")',
            class: "form-control",
            key: 'id',
            value: 'name',
            defaultName: '存放地'
        });
        $('#storagePlace').ysComboBoxTree('setValue', -1);
    }

    function loadUser() {
        $('#scrapUserId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '登记人'
        });
    }

    function resetGrid() {
        //清空条件
        $('#courseId').ysComboBox('setValue', -1);
        $('#storagePlace').ysComboBoxTree('setValue', -1);
        $('#scrapUserId').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function exportForm() {
        var url = '@Url.Content("~/InstrumentManage/InstrumentScrap/ExportInstrumentScrap")';//报废记录(101014)
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = BasePageCode;//报废记录(101014)
        ys.exportExcel(url, postData);
    }

</script>
