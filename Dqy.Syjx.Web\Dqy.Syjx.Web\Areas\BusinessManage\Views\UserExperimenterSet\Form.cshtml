﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .check-box {
        width: 100px;
    }

    .iradio-box {
        width: 100px;
    }

    .iradio-box {
        position: initial;
        display: inline-block;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

 /*   .tag_msg_color {
        color: #999;
    }*/
</style>
<div class="container-div">
    <div class="row">
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <div class="ibox-title">
                <h5>管理员设置</h5>
            </div>
            <div class="card-body">
                <form id="form" class="form-horizontal m">
                    <input type="hidden" id="Id" col="Id" value="0" />
                    <input type="hidden" id="SysUserId" col="SysUserId" value="0" />
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">姓名<font class="red"> *</font></label>
                        <div class="col-sm-5">
                            <input id="RealName" col="RealName" type="text" class="form-control" readonly />
                        </div>
                        <div class="col-sm-5">
                            <a class="btn btn-success" onclick="showChooseUserForm()"><i class="fa fa-plus"></i> 选择</a>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">是否实验员<font class="red"> *</font></label>
                        <div class="col-sm-5">
                            <label class="iradio-box"><div class="iradio-blue single checked" style="vertical-align:bottom;"><input name="inputType_checkbox" type="radio" value="1" style="position: absolute; opacity: 0;"></div> 是</label>
                        </div>
                        <div class="col-sm-5" style="color:#999;">
                             
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">实验员性质<font class="red"> *</font></label>
                        <div class="col-sm-5">
                            <div id="ExperimenterNature" col="ExperimenterNature"></div>
                        </div>
                        <div class="col-sm-5">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">状态<font class="red"> *</font></label>
                        <div class="col-sm-5">
                            <div id="Statuz" col="Statuz"></div>
                        </div>
                        <div class="col-sm-5" style="color:#999;">
                            市级和区县均需统计，请选择“全部”
                        </div>
                    </div>
                </form>
            </div>
            <div class="btn-group d-flex" role="group" id="toolbar" style="text-align: center;">
                <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#IsExperimenter").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), class:"form-control"});
        $("#ExperimenterNature").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimenterNatureEnum).EnumToDictionaryString())), class: "form-control" });
        $("#Statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class: "form-control"});
        getForm();
    });
    function showChooseUserForm() {
         ys.openDialog({
            title: "选择人员",
             content: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/ChooseUserForm")' + '?id=' + id,
            /* height: "200px",*/
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserExperimenterSet/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = { Id: 0, UserId: 0  };
            $('#form').setWebControls(defaultData);
        }
    }
    function setSelectName(name, userid) {
        $("#RealName").val(name);
        $("#SysUserId").val(userid);
    }
    function saveForm(index) {
        var postData = $('#form').getWebControls({ Id: id });
        //验证信息。
        var msg = '';
        if (isNaN(postData.SysUserId) || postData.SysUserId <= 0) {
            msg += '请选择姓名。<br/>';
        }
        if (isNaN(postData.ExperimenterNature) || postData.ExperimenterNature <= 0) {
            msg += '请选择实验员性质。<br/>';
        }
        if (isNaN(postData.Statuz == 1 || postData.Statuz == 2)) {
            msg += '请选择实验员状态。<br/>';
        }
        if (msg != '') {
            ys.msgError(msg);
            return;
        }
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserExperimenterSet/SaveFormJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    var url = '@Url.Content("~/BusinessManage/UserExperimenterSet/Index")';
                    createMenuAndCloseCurrent(url, "要求统计人员");
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>

