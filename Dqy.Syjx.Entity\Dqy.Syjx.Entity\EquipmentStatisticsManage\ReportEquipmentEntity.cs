﻿using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dynamitey.DynamicObjects;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;

namespace Dqy.Syjx.Entity.EquipmentStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-01-29 10:55
    /// 描 述：学校上报科技信息实体类
    /// </summary>
    [Table("zb_ReportEquipment")]
    public class ReportEquipmentEntity : BaseExtensionEntity
    {
        /// <summary>
        /// 填报主表Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long ReportId { get; set; }
        /// <summary>
        /// 科创设备名称配置Id
        /// </summary>
        /// <returns></returns>
        [JsonConverter(typeof(StringJsonConverter))]
        public long EquipmentNameConfigId { get; set; } = 0;
        /// <summary>
        /// 设备名称
        /// </summary>
        /// <returns></returns>
        [Description("设备名称")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string Name { get; set; }
        /// <summary>
        /// 品牌
        /// </summary>
        /// <returns></returns>
        [Description("品牌")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 10)]
        public string Brand { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        /// <returns></returns>
        [Description("数量")]
        [ExportExcelAttribute(HorizontalAlignment.Right, 10)]
        public decimal Num { get; set; }
        /// <summary>
        /// 型号
        /// </summary>
        /// <returns></returns>
        [Description("型号")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 20)]
        public string Modelz { get; set; }
        /// <summary>
        /// 计量单位
        /// </summary>
        /// <returns></returns>
        [Description("单位")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 8)]
        public string UnitName { get; set; }
        /// <summary>
        /// <summary>
        /// 排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        /// <returns></returns>
        [Description("备注")]
        [ExportExcelAttribute(HorizontalAlignment.Left, 20)]
        public string Remark { get; set; }

        /// <summary>
        /// 学校名称
        /// </summary>
        [Description("单位名称")]
        [NotMapped]
        [ExportExcelAttribute(HorizontalAlignment.Left, 30)]
        public string SchoolName { get; set; }
        /// <summary>
        /// 学校编码
        /// </summary>
        [NotMapped]
        [Description("单位编号")]
        [ExportExcelAttribute(HorizontalAlignment.Center, 20)]
        public string SchoolCode { get; set; }
  
        [NotMapped]
        [JsonConverter(typeof(StringJsonConverter))]
        public long SchoolId { get; set; }
    }
}
