﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input id="channelCode" col="channelCode" type="text" placeholder="通道编码" />
                        &nbsp;
                        <input id="channelName" col="channelName" type="text" placeholder="通道名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>

                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnSync" class="btn btn-info" onclick="SyncDahuaDevice()"><i class="fa fa-random"></i> 同步通道信息</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/CameraManage/DahuaChannel/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { field: 'Id', title: 'Id', visible: false },
                { field: 'channelCode', title: '通道编码', sortable: true, halign: 'center', align: 'center' },
                { field: 'channelName', title: '通道名称', sortable: true, halign: 'center', align: 'center' },
                { field: 'channelSeq', title: '通道序号', sortable: true, halign: 'center', align: 'center' },
                { field: 'deviceCode', title: '所属设备', sortable: true, halign: 'center', align: 'center' },
                {
                    field: 'isOnline', title: '状态', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = "";
                        if (value == 0) {
                            html = "离线";
                        } else if (value == 1) {
                            html = "在线";
                        }
                        return html;
                    }
                },
                { field: 'createTime', title: '创建时间', sortable: true, halign: 'center', align: 'center' }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                console.log('queryString', queryString);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        $('#channelCode').val("");
        $('#channelName').val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function SyncDahuaDevice() { //同步大华设备通道信息
        ys.ajax({
            url: '@Url.Content("~/CameraManage/DahuaCamera/SyncDahuaChannel")',
            type: "post",
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    searchGrid();
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>
