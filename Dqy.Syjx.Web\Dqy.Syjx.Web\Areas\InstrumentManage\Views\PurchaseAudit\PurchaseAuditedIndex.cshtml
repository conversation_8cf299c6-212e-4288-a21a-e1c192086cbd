﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>填报日期： </label><input id="startDate" col="StartDate" type="text" class="time-input" placeholder="开始时间" style="width:100px" />
                        <span>-</span>
                        <input id="endDate" col="EndDate" type="text" class="time-input" placeholder="结束时间" style="width:100px" />
                    </li>
                    @await Html.PartialAsync("/Areas/InstrumentManage/Shared/DeclarationSearchPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    <li>
                        <span id="declareUserId" col="DeclareUserId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="statuz" col="Statuz" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        getStatuz();
        loadUser();
        
        
        initGrid();
    });
    function loadUser() {
        $('#declareUserId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '填报人'
        });
    }

    function getStatuz() {
        var data = [];
        data.push({ id: 11, text: '学校审批不通过' });
        data.push({ id: 100, text: '计划审批结束' });
        $("#statuz").ysComboBox({
            data: data,
            key: "id",
            value: "text",
            defaultName: '计划状态'
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseAudit/GetAuidtedListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            /*showFooter: true,*/
            columns: [
                //{ field: 'PurchaseYear', title: '采购年度', align: 'center', halign: 'center', sortable: true, width: 50 },
                //{ field: 'InstrumentClassName', title: '仪器类别', align: 'left', halign: 'center', sortable: true, width: 140 },
                {
                    field: 'opt', title: '操作', align: 'center', halign: 'center', width: commonWidth.Instrument.Opt2,
                    formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.PurchaseDeclarationId);
                        if (row.IsWithdraw == @IsEnum.Yes.ParseToInt())
                            html += $.Format('<a class="btn btn-warning btn-xs" href="#" onclick="withdraw(this)" value="{0}"><i class="fa fa-reply"></i>撤回</a> ', row.Id);
                        return html;
                    }
                },
                { field: 'Course', title: '适用学科', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Course },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                },
                {
                    field: 'Name', title: '仪器名称', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                { 
                    field: 'Model', title: '规格属性', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'Num', title: '数量', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'Price', title: '单价', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        return ComBox.ToLocaleString(value);
                    }
                },
                {
                    field: 'AmountSum', title: '金额', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        return ComBox.ToLocaleString(value);
                    }
                },
                
                { field: 'UserName', title: '填报人', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.UserName },
                {
                    field: 'BaseCreateTime', title: '填报日期', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
                {
                    field: 'StatuzDesc', title: '计划状态', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Statuz,
                    formatter: function (value, row, index) {
                        if (row.Statuz % 2 == 1)
                            return '<span style="color:red">' + value + '</span>';
                        else
                            return value;
                    }
                }
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function audit(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseAuditForm")' + '?id=' + id;
        createMenuItem(url, "学校审批");
    }

    function look(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseDetail")' + '?id=' + id;
        //createMenuItem(url, "查看计划");
        ys.openDialog({
            title: "查看计划",
            width: "800px",
            content: url,
            btn: ['关闭'],
            yes: function (index) {
                $.modal.close(index);
            },
        });
    }

    function withdraw(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要撤回吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseAudit/Withdraw")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#purchaseYear').ysComboBox('setValue', -1);
        $('#instrumentClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#declareUserId').ysComboBox('setValue', -1);
        $('#statuz').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>
