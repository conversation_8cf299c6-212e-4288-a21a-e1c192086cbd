﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.js"))
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
</style>
<div class="ui-layout-west">
    <div class="main-content">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    所属部门
                </div>
                <div class="box-tools pull-right">
                    <a type="button" class="btn btn-box-tool menuItem" href="#" onclick="showDepartmentForm()" title="管理部门"><i class="fa fa-edit"></i></a>
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div id="departmentTree" class="ztree"></div>
            </div>
        </div>
    </div>
</div>

<div class="container-div ui-layout-center">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <input type="hidden" id="departmentId" col="DepartmentId">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="unitName" col="UnitId" style="display: inline-block;width:180px;"></span>
                    </li>
                    <li>
                        <span id="roleId" col="RoleId" style="display: inline-block;width:150px;"></span>
                    </li>
                    <li>
                        <span id="userStatus" col="UserStatus" style="display: inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="realName" col="RealName" type="text" placeholder="姓名、手机号、登录账号" style="display: inline-block;width:160px;" />
                    </li>
                    <li class="select-time">
                        <label>创建时间： </label>
                        <input id="startTime" col="StartTime" type="text" class="time-input" placeholder="开始时间" />
                        <span>-</span>
                        <input id="endTime" col="EndTime" type="text" class="time-input" placeholder="结束时间" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>


        <div id="toolbar" class="btn-group d-flex" role="group">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            @*<a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>*@
            <a id="btnImport" class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a>
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var BasePageCode = 101021;//用户列表(101021)
    $(function () {
        initGrid();
        initTree();

        $('body').layout({ west__size: 185 });

        laydate.render({ elem: '#startTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        laydate.render({ elem: '#endTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        $("#userStatus").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())),
            defaultName: '用户状态',
        });
         $("#roleId").ysComboBox({
            url: '@Url.Content("~/SystemManage/Role/GetRoleListJson")',
            key: "Id",
             value: "RoleName",
             defaultName: '用户角色',
         });

        $("#unitName").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetListJson?PageSize=10000")',
            key: "Id",
            value: "Name",
            defaultName: '单位名称',
         });


        $('#btnExpand').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });

        $('#btnRefresh').click(function () {
            initTree();
        });

        
        
    });

    function clearGrid() {
        $('#roleId').ysComboBox('setValue', -1);
        $('#userStatus').ysComboBox('setValue', -1);
        $('#unitName').ysComboBox('setValue', -1)
        initTree();
        $("#departmentId").val(null);
        $("#realName").val("");
        $("#startTime").val("");
        $("#endTime").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function initTree() {
        $('#departmentTree').ysTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            async: true,
            expandLevel: 2,
            maxHeight: "700px",
            callback: {
                onClick: function (event, treeId, treeNode) {
                    $("#departmentId").val(treeNode.id);
                    searchGrid();
                }
            }
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/User/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                { checkbox: true, visible: true },
                {
                    title: '操作',
                    halign: 'center', align: 'center', width: 120,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.UserStatus == "@StatusEnum.Yes.ParseToInt()") {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'禁用\')"><i class="fa fa-check"></i>禁用</a>&nbsp;&nbsp;');
                        } else {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'启用\')"><i class="fa fa-check"></i>启用</a>&nbsp;&nbsp;');
                        }
                        //actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="DelUser(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;');
                        //actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="showResetPasswordForm(\'' + row.Id + '\')"><i class="fa fa-key"></i>重置</a>');
                        actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="UnlockUser(\'' + row.Id + '\')"><i class="fa fa-key"></i>解锁</a>');
                        return actions.join('');
                    }
                },
                { field: 'UnitName', title: '单位名称', width: 180, sortable: true,halign: 'center', align: 'left'},
                {
                    field: 'UnitType', title: '单位性质', width: 80, sortable: true, halign: 'center', align: 'center', formatter: function (value, row, index) {
                        switch (row.UnitType) {
                            case @UnitTypeEnum.City.ParseToInt():
                                return '<span>' + "@UnitTypeEnum.City.GetDescription()" + '</span>';
                                break;
                            case @UnitTypeEnum.County.ParseToInt():
                                return '<span>' + "@UnitTypeEnum.County.GetDescription()" + '</span>';
                                break;
                            case @UnitTypeEnum.School.ParseToInt():
                                return '<span>' + "@UnitTypeEnum.School.GetDescription()" + '</span>';
                                break;
                            case @UnitTypeEnum.System.ParseToInt():
                                return '<span>' + "@UnitTypeEnum.System.GetDescription()" + '</span>';
                                break;
                        }
                    }
                },
                { field: 'RealName', title: '姓名', width: 120, halign: 'center', align: 'center', sortable: true, },
                { field: 'Mobile', title: '手机号', width: 100, halign: 'center', align: 'center',sortable: true, },
                { field: 'UserName', title: '登录账号', halign: 'center', align: 'center', width: 120,sortable: true },
                { field: 'RoleNames', title: '用户角色', halign: 'center', align: 'left', width: 200},
                { field: 'DepartmentNames', title: '所属部门', halign: 'center', align: 'left', width: 120},
                {
                    field: 'UserStatus', title: '状态', halign: 'center', align: 'center', width: 80, sortable: true,formatter: function (value, row, index) {
                        if (row.UserStatus == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'BaseModifyTime', title: '创建时间', halign: 'center', align: 'center', width: 120, sortable: true, formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd HH:mm:ss");
                    }
                },
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $("#searchDiv").getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? "编辑用户" : "添加用户",
            height: "600px",
            content: '@Url.Content("~/OrganizationManage/User/UserForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm("确认要删除选中的" + selectedRow.length + "条数据吗？", function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/User/DeleteUserFormBySystemSuper")' + '?ids=' + ids,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function showDepartmentForm() {
        var url = '@Url.Content("~/OrganizationManage/Department/DepartmentIndex")';
        createMenuItem(url, "部门管理");
    }

    function showResetPasswordForm(id) {
        ys.openDialog({
            title: "重置密码",
            content: '@Url.Content("~/OrganizationManage/User/ResetPassword")' + '?id=' + id,
            height: "220px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function exportForm() {
        var url = '@Url.Content("~/OrganizationManage/User/ExportUserJson")';//用户列表(101021)
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = BasePageCode;//用户列表(101021)
        ys.exportExcel(url, postData);
    }

    function importForm() {
      ys.openDialog({
            title: "导入用户数据",
            content: '@Url.Content("~/OrganizationManage/User/UserImport")',
            height: "280px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function ChangeStatuz(Id, strStatuz) {
        ys.confirm("确认要" + strStatuz +"吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/UpdateStatuzBySystemSuper")' + '?id=' + Id,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(strStatuz+" 成功");
                            searchGrid();
                        }
                        else {
                            ys.msgError(strStatuz + " 失败!"+obj.Message);
                        }
                    }
                });
        });
    }

    function DelUser(id) {
         ys.confirm("确认要删除此条数据吗？", function () {
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/User/DeleteUserFormBySystemSuper")' + '?ids=' + id,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
    }

    function UnlockUser(id){
        ys.confirm("确认要解锁此用户吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/UnlockUser")' + '?id=' + id,
                type: "post",
                error: ys.ajaxError,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>
