﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .span-left-tag {
        margin-left: 82px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolYearStart" col="SchoolYearStart" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="CourseId" col="CourseId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="Statuz" col="Statuz" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="PlanName" col="PlanName" placeholder="名称" style="display: inline-block;width:180px;" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 添加</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    $(function () {
        loadCourse();
        ComBox.SchoolTermYear($('#SchoolYearStart'), undefined, '学年', 10);
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            defaultName: '学期'
        });
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        $("#Statuz").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(PublishStatusEnum).EnumToDictionaryString())),
            minimumResultsForSearch: -10000,
            defaultName: '实验状态'
        });
        initGrid();


    });

    function loadCourse() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolUserCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data);
                } else {
                    bindCourseData({});
                }
            }
        });
    }
    function bindCourseData(data) {
        $('#CourseId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '课程'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/GetOutPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                {
                    title: '操作', width: 220, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.Statuz == 0) {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="showSetStatuzForm(\'' + row.Id + '\')"><i class="fa fa-check"></i>提交</a>');
                            actions.push('<a class="btn btn-success btn-xs" href="#" onclick="showSaveForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>');
                            actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        } else if (row.Statuz == 1) {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="showCancelForm(\'' + row.Id + '\')"><i class="fa fa-reply"></i>撤销</a>');
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'SchoolYearStart', title: '学期', width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2);
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== value) {
                            html += '@SchoolTermEnum.NextSemester.GetDescription()';
                        } else {
                            html += '@SchoolTermEnum.LastSemester.GetDescription()';
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'Name', title: '课程名称', sortable: true, width: 180, halign: 'center', valign: 'middle' },
                { field: 'FunRoomName', title: '上课地点', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'ClassTimeStr', title: '上课时间', sortable: true, width: 180, halign: 'center', valign: 'middle', align: 'center'  },
                { field: 'StudentNumLimit', title: '学生限额', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'UseNum', title: '实验清单', width: 70, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="#" onclick="openLookDetailForm(\'' + row.Id + '\')"><i class="fa fa-eye"></i>查看</a>');
                        return actions.join('');
                    }
                },
                { field: 'Num', title: '实验数量', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Statuz', title: '发布状态', sortable: true, width: 60, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.StatuzName;
                    }
                },
                { field: 'StudentNumed', title: '已约人数', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'RealName', title: '编制人', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#Statuz").ysComboBox('setValue', -1);
        $("#CourseId").ysComboBox('setValue', -1);
        $("#SchoolYearStart").ysComboBox('setValue', -1);
        $('#SchoolTerm').ysComboBox('setValue', -1);
        $("#PlanName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(id) {
        // ys.openDialog({
        //     title: id > 0 ? '编辑' : '添加',
        //     content: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddForm")' + '?id=' + id,
        //     width: '768px',
        //     height:'550px;',
        //     callback: function (index, layero) {
        //         var iframeWin = window[layero.find('iframe')[0]['name']];
        //         iframeWin.saveForm(index);
        //     }
        // });
        var title = id > 0 ? '实验课程编辑' : '实验课程添加';
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddForm")' + '?id=' + id;
        createMenuItem(url, title);
    }
 
    function deleteForm(id) {
        ys.confirm('确认要删除这条数据吗？如果删除添加的实验清单将也删除。', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/DeleteOutFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    function showSetStatuzForm(id) {
        ys.confirm('确认要提交当前实验课程吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/SubmitOutFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    function showCancelForm(id) {
        ys.confirm('确认要撤销提交当前实验课程吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/CancelSubmitOutForm")' + '?ids=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function openLookDetailForm(id) {
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddDetailList")' + '?id=' + id;
        createMenuItem(url, "实验清单");
    }
</script>
