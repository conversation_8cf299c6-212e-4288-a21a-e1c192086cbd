﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsrender.js"),true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsviews.js"),true)

<style>


    #searchDiv {
        position: fixed;
        /*  position: sticky;*/
        width: 96%;
        top: 0px;
        z-index: 996;
    }

    .container-div {
        padding: 10px 35px;
        /*  height: 100%;
            overflow-y: auto;*/
    }

    .hide {
        display: none;
    }

    .show {
        display: block;
    }

    .namesearch {
        width: 580px;
        display: block;
        /* position: relative; */
        position: absolute;
        /* bottom: 0px; */
        /* 根据需要调整距离 */
        font-size: 14px;
        border: 1px solid #ccc;
        border-radius: 5px;
        /* width: 300px; */
        max-height: 300px;
        /*max-height: 500px;*/
        background-color: #fff;
        overflow: auto;
        box-shadow: 0px 0px 10px #ceccca;
        margin: 2px 0;
        padding: 10px 5px;
        z-index: 999;
    }

    .form-control {
        display: block;
        width: 100%;
        /*height: 100%;*/
        font-size: 14px;
        color: #555;
        background-color: #fff;
        background-image: none;
        /****控制输入框无边框及颜色***/
        border: none;
        /****控制输入框颜色显示***/
        /* border:solid 1px #1ab394;*/
        border-radius: 4px;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

        .form-control:focus {
            outline: none;
            border: 1px solid #0077cc;
        }

    option:hover {
        background-color: #90bafb;
        color:#ffffff;
    }

    option {
        text-align: left;
        padding:5px;
        white-space: pre-wrap;
    }

    .coursearch {
        display: block;
        position: absolute;
        font-size: 14px;
        border: 1px solid #0077cc;
        border-radius: 5px;
        width: 200px;
        max-height: 150px;
        background-color: #fff;
        overflow-y: auto;
        box-shadow: 0px 0px 2px #0077cc;
        margin: 5px 0;
        padding: 5px 0;
        z-index: 999;
    }

    .form-control[disabled],
    .form-control[readonly],
    fieldset[disabled] .form-control {
        background-color: #fff;
        opacity: 1;
    }

 
    .padTd {
        padding: 0 !important;
        height: 32px;
    }

    .content {
        width: 100%;
        height: 100%;
        line-height: 32px;
        padding: 0 8px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }


    .nameContent {
        width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .modelContent {
        width: 240px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .modelDiv {
        display: block;
        position: absolute;
        font-size: 14px;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 240px;
        height: 120px;
        max-height: 300px;
        background-color: #fff;
        overflow: auto;
        box-shadow: 0px 0px 10px #ceccca;
        margin: 2px 0;
        padding: 10px 5px;
        z-index: 999;
    }

    .modeltextarea {
        width: 100%;
        height: 100%;
        border: none;
        resize: none;
        cursor: pointer;
    }

    .area-control .form-control {
        display: block;
        width: 100%;
        height: 100%;
        font-size: 14px;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        border-radius: 4px;
        -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
        -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
        transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    }



        .area-control .form-control:focus {
            outline: none;
            border: 1px solid #0077cc;
        }

    .searchLeft {
        position: absolute;
        top: 11px;
        left: 2px;
        color: #C0C4CC;
    }
    #gridTable tbody tr td {
        padding: 0;
    }

    .form-control {
        padding: 12px !important;
    }


    #addTable th,
    #addTable td {
        text-align: center;
        width: 140px;
        padding: 5px !important;
        border: none;
    }

    #addTable tr {
        border-top: 1px solid #e3e3e3;
    }

    #addTable thead tr {
        border-top: 1px solid #000000;
    }

    #addTable tbody tr:hover {
        background-color: #ECF5FF;
    }

    #addTable thead {
        background-color: #eeeeee;
    }

    .noform-control {
        display: block;
        width: 100%;
        /*height: 100%;*/
        font-size: 14px;
        color: #555;
        background-color: #fff !important;
        background-image: none;
        border: 1px solid red !important;
        border-radius: 4px;
        -webkit-box-shadow: none !important;
        box-shadow: none;
    }

    /*   #gridTable tbody > tr > td {
            padding: 2px;
        }*/

    #gridTable tbody tr:nth-child(2n+1) td .form-control {
        background-color: #F9F9F9;
    }

    #gridTable tbody tr:nth-child(2n) td .form-control {
        background-color: #FFFFFF;
    }

    #gridTable tbody tr:hover td .form-control {
        background-color: #F5F5F5;
    }

    #gridTable tbody tr td {
        position: relative;
        overflow: visible;
    }

    .iconDown{  
        position:absolute;
        top:8px;
        right:5px;
    }
    .iconUp {  
        position: absolute;
        top: 11px;
        right: 5px;
    }



    @@media (max-width: 768px) {
        #divCourseInfo {
            margin-top: 10px !important;
        }
    }

</style>

@*<select id="selData" style="display:none;">
    <option value="1">aaa</option>
    <option value="2">bbb</option>

    <option value="3">ccc</option>
</select>*@

<div class="container-div">
    <div id="searchDiv" class="col-sm-12 search-collapse">
        <div class="select-list">
            <ul>

                <li>
                    <label>采购年度： </label>
                    <span id="purchaseYear" col="PurchaseYear" style="display:inline-block;width:100px;"></span>
                </li>
                @* <li>
                <label>适用学段： </label>
                <span id="stageId" col="StageId" style="width:80px;"></span>
                </li>
                <li>
                <label>适用学科： </label>
                <span id="courseId" col="CourseId" style="width:80px;"></span>
                </li>*@
                <li>
                    <a id="btnAdd" class="btn btn-success btn-sm" onclick="saveInstrumentStandard()"><i class="fa fa-search"></i> 从仪器目录选择</a>
                </li>
                <li>
                    <a id="btnAdd" class="btn btn-success btn-sm" onclick="saveGap()"><i class="fa fa-search"></i> 从达标缺口选择</a>
                </li>

                <li>
                    <a id="btnAdd" class="btn btn-success btn-sm" onclick="tempSave()"><i class="fa fa-save"></i> 暂存</a>
                </li>
                <li>
                    <a id="btnDelete" class="btn btn-warning btn-sm" onclick="save(10)"><i class="fa fa-check"></i>提交计划</a>
                </li>
                <li style="padding-left:50px;display:none;" id="lblMsg">
                    @* <label id="lblMsg" style="display:none;color:red; font-size:15px; padding-left:20px;padding-top:10px;">【当前区县已设置不允许学校自定义规格属性;若有自定义规格属性需要提交,请联系区县负责人员设置!】</label>*@
                    <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                          data-content="当前区县已设置不允许学校自定义规格属性;若有自定义规格属性需要提交,请联系区县负责人员设置!"></span>
                </li>
                
                <!--帮助文档需要内容,id值必须为“helpBtn”-->
                <li>
                    <i class="fa fa-question-circle" id="helpBtn" ></i>
                </li>
                
            </ul>
        </div>
    </div>
   
    <div id="divCourseInfo" style="margin-top:60px;">
    </div>

    <!--帮助文档需要内容,id值必须为“tipMsg”-->
    <div id="tipMsg"></div>


</div>

<script id="dateList" type="text/x-jsrender">
    <div class="fixed-table-container">
        @*<div class="fixed-table-toolbar" style="></div>*@
        <div class="bootstrap-table"  style="margin-top:10px;">
    @* <div class="fixed-table-toolbar">
                <div class="bs-bars pull-left" style="margin-bottom:10px;">
                    <div id="toolbar" class="btn-group d-flex" role="group">


                    </div>
                </div>
            </div>*@

            <div class="fcontainer" style="padding-bottom: 0px;">
    @*<div class="fixed-table-header" style="display: none;">
                    <table></table>
                </div>*@
                <div class="fixed-table-body" >
                    <form id="commentForm">
                        <table id="gridTable" data-mobile-responsive="true" class="table table-hover table-striped table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">序号</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">操作</div><div class="fht-cell"></div>
                                </th>

                                <th style="text-align: center; vertical-align: middle;" data-field="OriginalCode">
                                    <div class="th-inner sortable both">分类代码</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;" data-field="Name">
                                    <div class="th-inner sortable both">仪器名称</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">规格属性</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">数量</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">单位</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">单价</div><div class="fht-cell"></div>
                                </th>
    @* <th style="text-align:center; vertical-align: middle;">
                                    <div class="th-inner sortable both">金额</div><div class="fht-cell"></div>
                                </th>*@
                                 <th style="text-align: center; vertical-align: middle;" data-field="Stage">
                                    <div class="th-inner sortable both">适用学段</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;" data-field="Course">
                                    <div class="th-inner sortable both">适用学科</div><div class="fht-cell"></div>
                                </th>
                                 <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">采购理由</div><div class="fht-cell"></div>
                                </th>

                            </tr>
                        </thead>
                        <tbody id="list">
                             {^{for data}}
                                 <tr>
                                    <td style="text-align: center;width:50px; ">{^{:#index + 1}}</td>
                                    <td style="text-align: center;width:120px; white-space:nowrap;">
                                        <input type="hidden" data-link="Id" value="{{>Id}}" />
                                        <input type="hidden" data-link="ModelStandardId" value="{{>ModelStandardId}}" />
                                        <input type="hidden" data-link="InstrumentStandardId" value="{{>InstrumentStandardId}}" />


    @*  &nbsp;
                                        <a class="btn btn-success btn-xs" href="#" data-link="{on ~root.addRow #index}" value=""><i class="fa fa-plus"></i></a>&nbsp;&nbsp;
                                        <a class="btn btn-info btn-xs" href="#" data-link="{on ~root.copyRow #index}" value=""><i class="fa fa-clone"></i></a>&nbsp;&nbsp;
                                        <a class="btn btn-danger btn-xs" href="#" data-link="{on ~root.deleteRow #index}" value=""><i class="fa fa-remove"></i></a>
                                        &nbsp;*@
                                        <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.addRow #index}" value=""><i class="fa fa-plus"></i>新增</a>
                                        <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.deleteRow #index}" value=""><i class="fa fa-remove">删除</i></a>
                                        <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.copyRow #index}" value=""><i class="fa fa-clone"></i>复制</a>
                                        <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.selectModel #index}" value=""><i class="fa fa-search"></i>选型</a>
                                    </td>

                                    <td style="text-align: center;width:100px;">
                                            <input  index="{{:#index}}" data-link="OriginalCode"  value="{{>OriginalCode}}" title="{{>OriginalCode}}" onkeyup = "value=value.replace(/[^\d]/g,'')" onpaste="value=value.replace(/[^\d]/g,'')"  onclick="textClick(this);" autocomplete="off"   oninput="codeInput(this)" class="form-control form-control-lg" style="cursor: pointer;" type="text"/>
                                            <div class="hide"></div>
                                            @*<i id="searchLeft" class="fa fa-search searchLeft"></i>*@
                                    </td>
                                    <td style="text-align: center;width:140px;">
                                            <input index="{{:#index}}"  data-link="Name" value="{{>Name}}" title="{{>Name}}" onclick="textClick(this);" autocomplete="off"  oninput="nameInput(this);" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                                            <div class="hide"></div>
                                            @*<i id="searchLeft" class="fa fa-search searchLeft"></i>*@
                                    </td>
                                    <td style="text-align: left;width:200px;cursor: pointer;" class="padTd">
                                            <div class="content model"  data-content="{{>Model}}" onclick="modelClick(this)"  id="modelHover"  >{{>Model}}</div>
                                            <div  class="area-control hide"><textarea data-link="Model"  onchange="modelTextarea(this)" class="form-control form-control-lg modeltextarea" type="text" maxlength="150">{{>Model}}</textarea></div>
                                        </td>

                                     <td style="text-align: center;width:80px;">
                                             <input index="{{:#index}}" name="inputName" data-link="Num"  value="{{>Num}}" autocomplete="off"  maxlength="200" onkeyup="num(this,'Num')" onpaste="num(this,'Num')"  style="text-align:center;" onclick="textClick(this);select();" class="form-control form-control-lg" type="text" placeholder=""/>
                                     </td>

                                    <td style="text-align: center;width:50px;">

                                        <input id="nameinput{{:#index}}" data-link="UnitName" value="{{>UnitName}}" autocomplete="off"  onclick="textClick(this);select()" class="form-control form-control-lg" style="text-align:center;" type="text" placeholder=""/>
                                    </td>
                                    <td style="text-align: right;width:80px;">
                                        <input id="nameinput{{:#index}}" data-link="Price" value="{{>Price}}" autocomplete="off" onkeyup="num(this,'Price')" onpaste="num(this,'Price')" onclick="textClick(this);select();" class="form-control form-control-lg" style="text-align:right;" type="text" placeholder=""/>
                                    </td>
    @* <td style="text-align: right;width:100px;">

                                    </td>*@
                                       <td style="text-align: center;width:80px;">
                                            <input index="{{:#index}}" name="inputName" value="{{>Stage}}" autocomplete="off" readOnly  onclick="textClick(this);stageClick(this)" style="text-align:center;cursor: pointer;padding-right:15px !important;" class="form-control form-control-lg" type="text" placeholder=""/>
                                            <div class="hide widthDiv"></div>
                                            <i class="fa fa-sort-down iconDown"></i>
                                            <i class="fa fa-sort-up iconUp hide"></i>
                                        </td>

                                    <td style="text-align: center;width:80px;">
                                         <input index="{{:#index}}" name="inputName" value="{{>Course}}" autocomplete="off" readOnly onclick="textClick(this);courseClick(this)" style="text-align:center;cursor: pointer;padding-right:15px !important;" class="form-control form-control-lg" type="text" placeholder=""/>
                                            <div class="hide widthDiv"></div>
                                           <i class="fa fa-sort-down iconDown"></i>
                                            <i class="fa fa-sort-up iconUp hide"></i>
                                    </td>
                                    <td style="text-align: left;width:200px;cursor: pointer;" class="padTd">
                                            <div class="content reason"  title="{{>Reason}}" data-content="{{>Reason}}" onclick="modelClick1(this)" id="ReasonHover">{{>Reason}}</div>
                                            <div  class="area-control hide"><textarea data-link="Reason"  onchange="reasonTextarea(this)" class="form-control form-control-lg modeltextarea" type="text" maxlength="150">{{>Reason}}</textarea></div>
                                    </td>
                                </tr>
                             {{/for}}
                        </tbody>
                    </table>
                    </form>
                </div>
            </div>
        </div><div class="clearfix"></div>
    </div>
</script>

<script type="text/javascript">

    var showReadOnly = false;
    var course_info = null;
    var codeInputVal = '';
    var StageData = [];
    var CourseData = [];
    var defaultStageId = 0;
    var defaultStage = "";
    var defaultCourseId = 0;
    var defaultCourse = "";
    var IsValidateReason = 0;


    $(function () {
        
        
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });


        getYear();
        getSchoolStage();
        getCourse();

        $("#commentForm").validate();

        course_info = {
            data: [],
            addRow: function (index) {
                if (course_info.data.length >= 200) {
                    layer.alert("每次最多只能添加200条数据，为了方便您查阅您可以分多次提交计划！", {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                    return;
                }
                $.observable(this.data).insert(index + 1, { Id: 0, EntryType: 1, ModelCode: '', OriginalCode: '', InstrumentStandardId: 0, ModelStandardId: 0, RPurchaseYear: '', Name: '', UnitName: '', Num: '', Price: '', StageId: defaultStageId, CourseId: defaultCourseId, Stage: defaultStage, Course: defaultCourse, Reason: '' });
            },
            deleteRow: function (index) {
                $.observable(this.data).remove(index);
                if (this.data.length == 0) {
                    $.observable(this.data).insert(index + 1, { Id: 0, EntryType: 1, ModelCode: '', OriginalCode: '', InstrumentStandardId: 0, ModelStandardId: 0, RPurchaseYear: '', Name: '', UnitName: '', Num: '', Price: '', StageId: defaultStageId, CourseId: defaultCourseId, Stage: defaultStage, Course: defaultCourse, Reason: '' });
                }
            },
            copyRow: function (index) {
                if (course_info.data.length >= 200) {
                    layer.alert("每次最多只能添加200条数据，为了方便您查阅您可以分多次提交计划！", {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                    return;
                }
                var obj = this.data[index];
                //obj.Id = 0;
                //$.observable(this.data).insert(index + 1, obj);
                $.observable(this.data).insert(index + 1, { Id: 0, EntryType: obj.EntryType, ModelCode: obj.ModelCode, OriginalCode: obj.OriginalCode, Model: obj.Model, InstrumentStandardId: obj.InstrumentStandardId, ModelStandardId: obj.ModelStandardId, RPurchaseYear: obj.RPurchaseYear, Name: obj.Name, UnitName: obj.UnitName, Num: obj.Num, Price: obj.Price, StageId: obj.StageId, CourseId: obj.CourseId, Stage: obj.Stage, Course: obj.Course, Reason: obj.Reason });
            },
            selectModel: function (index) {
                var obj = this.data[index];
                if (obj.OriginalCode == "" || obj.OriginalCode.length != 11) {
                    //layer.alert("请输入11位编码", {
                    //    icon: 2,
                    //    skin: 'layer-ext-moon'
                    //});
                    ys.msgError('请输入11位分类代码');
                    return;
                }
                //console.log("obj:" + JSON.stringify(obj));
                //console.log("showReadOnly:" + showReadOnly);
                var title = "选型";
                var IsAllowEditModel = 1;
                if (showReadOnly) {
                    IsAllowEditModel = 0;
                    title = '选型 <font style ="color:red;">(当前区县不允许学校自定义规格属性，所以选型列表只能参考不能选择)</font>';
                }
                //console.log("IsAllowEditModel:" + IsAllowEditModel);
                var standardCode = obj.OriginalCode;

                ys.openDialog({
                    title: title,
                    content: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/MallProductChoose")' + '?instrumentStandardId=0&isAllowEditModel=' + IsAllowEditModel + '&instrumentStandardCode=' + standardCode + '&rowIndex=' + index,
                    width: '980px',
                    callback: function (index, layero) {
                        var iframeWin = window[layero.find('iframe')[0]['name']];
                        iframeWin.saveForm(index);
                    }
                });
            }
        };

        $.templates("#dateList").link("#divCourseInfo", course_info);
        loadIsValidate();

        $(".select2-container").width("100%");
        $(".select-list");

    });

    $(".white-bg").scroll(function () {

        let top = $(".white-bg").scrollTop()
        if (top > 0) {
            $("#searchDiv").css({
                "top": "-10px"
            })
        } else {
            $("#searchDiv").css({
                "top": "0px"
            })
        }

    });

    function textChange(obj) {
        if (!$(obj).val()) {
            $(obj).addClass('noform-control')
        } else {
            $(obj).removeClass('noform-control')
        }
    }
    function textClick(obj) {
        //$(obj).next().next().removeClass("show").addClass("hide")
        $(obj).blur(() => {
            //$(obj).next().next().removeClass("hide").addClass("show")
            if (!$(obj).val()) {
                $(obj).addClass('noform-control')
            } else {
                $(obj).removeClass('noform-control')
            }
        })
    }

    function textAreaClick(obj) {
        $(obj).blur(() => {
            if (!$(obj).text()) {
                $(obj).addClass('noform-control')
            } else {
                $(obj).removeClass('noform-control')
            }
        })
    }

    /**
    *
    * 加载必填项
    */
    function loadIsValidate() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetNeedValidateJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    //console.log("obj:"+JSON.stringify(obj));
                    IsValidateReason = obj.Data;
                }

                getForm();
            }
        });
    }

    /**
     *
     * 限制文本框只能输入数字且小数点后两位
     */
    function num(obj, name) {
        var index = $($(obj).parent().siblings()[0]).text() - 1;
        obj.value = obj.value.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
        obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字
        obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个, 清除多余的
        obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
        obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
        course_info.data[index][name] = obj.value;
    }


    /***
     *
     * 绑定年度
     */
    function getYear() {
        var d = new Date();
        var nowYear = d.getFullYear();
        var nextYear = nowYear + 1;
        $("#purchaseYear").ysComboBox({
            data: [{
                "id": nowYear,
                "text": nowYear
            }, {
                "id": nextYear,
                "text": nextYear,
            }],
            key: "id",
            value: "text",
        });

        $("#purchaseYear").ysComboBox('setValue', nowYear);
    }

    /**
     *
     * 绑定学段数组
     */
    function getSchoolStage(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSchoolStageByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    StageData = obj.Data;
                    if (obj.Data.length == 1) {
                        defaultStageId = obj.Data[0].DictionaryId;
                        defaultStage = obj.Data[0].DicName;
                    }
                    //$('#stageId').ysComboBox({
                    //    data: obj.Data,
                    //    key: 'DictionaryId',
                    //    value: 'DicName',
                    //    class: "form-control"
                    //});
                    //if (obj.Data.length == 0) {
                    //    ComBox.LoadPageMessage('适用学段', '/BusinessManage/UserSchoolStageSubject/Index', '管理员授权', @RoleEnum.BusinessManager.ParseToInt());
                    //}
                    //else {
                    //    if (defaultValue > 0) {
                    //        $('#stageId').ysComboBox('setValue', defaultValue);
                    //    }
                    //    else if (obj.Data.length == 1) {
                    //        $('#stageId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                    //    }
                    //}
                }
            }
        });
    }

    /***
     *
     * 绑定学科数组
     */
    function getCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSubjectByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    CourseData = obj.Data;
                    if (obj.Data.length == 1) {
                        defaultCourseId = obj.Data[0].DictionaryId;
                        defaultCourse = obj.Data[0].DicName;
                    } else if (obj.Data.length == 0) {
                        ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.BusinessManager.ParseToInt());
                    }
                    //$('#courseId').ysComboBox({
                    //    data: obj.Data,
                    //    key: 'DictionaryId',
                    //    value: 'DicName',
                    //    class: "form-control"
                    //});
                    //if (defaultValue > 0) {
                    //    $('#courseId').ysComboBox('setValue', defaultValue);
                    //}
                    //else if (obj.Data.length == 1) {
                    //    $('#courseId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                    //}
                }
            }
        });
    }



    /**
     *
     * 绑定Table Json数据
     */
    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetSubmitJson")',
            type: 'get',
            success: function (obj) {

                if (obj.Tag == 1) {
                    $.observable(course_info).setProperty("data", obj.Data);
                    if (obj.Data.length == 0) {
                        $.observable(course_info.data).insert(1, { Id: 0, OriginalCode: '', InstrumentStandardId: 0, ModelStandardId: 0, RPurchaseYear: '', Name: '', UnitName: '', Num: '', Price: '', StageId: defaultStageId, CourseId: defaultCourseId, Stage: defaultStage, Course: defaultCourse, Reason: '' });
                    } else {
                        var year = obj.Data[0].PurchaseYear;
                        if (year != null && year != undefined) {
                            $("#purchaseYear").ysComboBox('setValue', year);
                        }
                    }

                }
                if (obj.Total == -1) {
                    showReadOnly = true;
                    $("#lblMsg").show();
                }
                //console.log("showReadOnly:" + showReadOnly);
            }
        });
    }

    /**
     *
     * 采购理由弹出框修改
     */
    function reasonTextarea(obj) {
        //赋值给span标签
        $($(obj).parent().prev()).text($(obj).val());
        $(obj).parent().parent().children(".reason").removeClass('noform-control');


    }
    $(document).on('mouseup', function (e) {
        //只有在mouseup是展示的效果是正确的，down不行
        let e_class = e.target.className;
        if (e_class != 'form-control form-control-lg modeltextarea' && e_class != 'area-control modelDiv') {
            $('.area-control').removeClass("modelDiv")
            $('.area-control').addClass("hide")
        }
    })
    function reasonClick(obj) {
        $(obj).next().removeClass("hide")
        $(obj).next().addClass("modelDiv")
        $(obj.nextElementSibling.firstChild).focus()
        let outerWdh = $(obj).parent().outerWidth()
        $('.modelDiv').css({
            "right": "5px",
            "width": outerWdh + "px",
        })
        let popupH = $(".modelDiv").height() + 30

    }
    /**
     *
     * 备注信息弹出框修改
     */
    function modelTextarea(obj) {
        //赋值给span标签
        $($(obj).parent().prev()).text($(obj).val());
        $(obj).parent().parent().children(".model").removeClass('noform-control');
        //console.log(course_info.data, $(obj).parent().parent().children(".model").text())
    }
    $(document).on('mouseup', function (e) {
        //只有在mouseup是展示的效果是正确的，down不行
        let e_class = e.target.className;
        if (e_class != 'form-control form-control-lg modeltextarea' && e_class != 'area-control modelDiv') {
            $('.area-control').removeClass("modelDiv")
            $('.area-control').addClass("hide")
        }
    })
    function modelClick(obj) {
        //如果区县配置不可修改规格属性
        if (showReadOnly) {
            return;
        }
        $(obj).next().removeClass("hide")
        $(obj).next().addClass("modelDiv")
        $(obj.nextElementSibling.firstChild).focus()
        let outerWdh = $(obj).parent().outerWidth()
        $('.modelDiv').css({
            "width": outerWdh + "px",
        })

        let popupH = $(".modelDiv").height() + 30
        popupHeigth(obj, popupH, '.modelDiv')
    }
        function modelClick1(obj) { 
        $(obj).next().removeClass("hide")
        $(obj).next().addClass("modelDiv")
        $(obj.nextElementSibling.firstChild).focus()
        let outerWdh = $(obj).parent().outerWidth()
        $('.modelDiv').css({
            "width": outerWdh + "px",
        })

        let popupH = $(".modelDiv").height() + 30
        popupHeigth(obj, popupH, '.modelDiv')
    }
    function popupHeigth(obj, popupH, cssDiv) {
        // 获取父元素高度进行定位
        let h = $(obj).height();//元素高度
        let wh = $(window).height();//浏览器窗口高度
        let xh = wh - (h + $(obj).offset().top - $(document).scrollTop());//元素到浏览器底部的高度
        // 获取表格元素高度进行定位
        let tabh = $(".fcontainer").height();//表格元素高度
        let tabxh = -(wh - (tabh + $(".fcontainer").offset().top - $(document).scrollTop()) - 20);//表格元素到浏览器底部的高度
        let th = $(obj).offset().top//元素距离顶部高度
        let tabth = $(".fcontainer").offset().top//表格元素距离顶部高度
        let boxh = th - tabth//获取当前元素与表格的像素差进行判断
        let ph = $(obj).parent().outerHeight();// 获取父元素高度进行定位

        if (xh < popupH) {
            if (boxh < popupH || th < popupH) {
                $(cssDiv).css({
                    "bottom": tabxh + "px"
                });
                $("#gridTable tbody tr td").css({
                    "position": ""
                })
            } else {
                $(cssDiv).css({
                    "bottom": ph + "px"
                });
                $("#gridTable tbody tr td").css({
                    "position": "relative"
                })

            }
        }
    }

    /**
     *
     * 学段点击事件
     */
    function stageClick(obj) {
        var index = $($(obj).parent().siblings()[0]).text() - 1;
        console.log("index",index)
        $(obj).next().removeClass("hide").addClass("coursearch")
        $(obj).next().next().removeClass("show").addClass("hide")
        $(obj).next().next().next().removeClass("hide").addClass("show")
        let searchSuggestions = $(obj).next();
        let searchTerm = $(obj).val();
        // 获取列表所有规格列表   绑定搜索条件通过条件筛选展示
        let data = StageData;
        searchSuggestions.html('');
        $.each(data, function (index, item) {
            let option = $('<option>');
            option.val(item.DictionaryId);
            option.text(item.DicName);
            searchSuggestions.append(option);
        });
        // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
        //   新添加的option节点添加点击事件
        $(obj).next().on('click', 'option', function () {
            let selvaText = $(this).text();//选中的下拉选项
            let selvaValue = $(this).val();
            //$(obj).val(selvaText)//下拉选中的值赋值给span

            //此处修改data内对象的值
            course_info.data[index].Stage = selvaText;
            course_info.data[index].StageId = selvaValue;
            var tempRow = course_info.data[index];
            $.observable(course_info.data).remove(index).insert(index, tempRow);

            // 选择后隐藏弹窗
            if (selvaText) {
                $(obj).next().removeClass("coursearch").addClass("hide")
                $(obj).next().next().removeClass("hide").addClass("show")
                $(obj).next().next().next().removeClass("show").addClass("hide")
            }
        });
        
        $('.widthDiv').width($(obj).innerWidth() + "px")
        $(obj).blur(() => {
            // 点击弹出框内部不隐藏
            $(obj).next().click(function (e) {
                e.stopPropagation()
            });
            // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
            $(document).one("click", function (e) {
                $(obj).next().removeClass("coursearch").addClass("hide")
                $(obj).next().next().removeClass("hide").addClass("show")
                $(obj).next().next().next().removeClass("show").addClass("hide")
            });
        })

        let popupH = $(".coursearch").height() + 30
        popupHeigth(obj, popupH, '.coursearch')
    }

    /**
    *
    * 学科点击事件
    */
    function courseClick(obj) {
        var index = $($(obj).parent().siblings()[0]).text() - 1;
        
        $(obj).next().removeClass("hide").addClass("coursearch")
        $(obj).next().next().removeClass("show").addClass("hide")
        $(obj).next().next().next().removeClass("hide").addClass("show")
        let searchSuggestions = $(obj).next();
        let searchTerm = $(obj).val();
        // 获取列表所有规格列表   绑定搜索条件通过条件筛选展示
        let data = CourseData;
        searchSuggestions.html('');
        $.each(data, function (index, item) {
            let option = $('<option>');
            option.val(item.DictionaryId);
            option.text(item.DicName);
            searchSuggestions.append(option);
        });
        // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
        //   新添加的option节点添加点击事件
        $(obj).next().on('click', 'option', function () {
            let selvaText = $(this).text();//选中的下拉选项
            let selvaValue = $(this).val();
            //$(obj).val(selvaText)//下拉选中的值赋值给span

            //此处修改data内对象的值
            course_info.data[index].Course = selvaText;
            course_info.data[index].CourseId = selvaValue;
            var tempRow = course_info.data[index];
            $.observable(course_info.data).remove(index).insert(index, tempRow);

            // 选择后隐藏弹窗
            if (selvaText) {
                $(obj).next().removeClass("coursearch").addClass("hide")
                $(obj).next().next().removeClass("hide").addClass("show")
                $(obj).next().next().next().removeClass("show").addClass("hide")
            }
        });
        $('.widthDiv').width($(obj).innerWidth() + "px")
        $(obj).blur(() => {
            // 点击弹出框内部不隐藏
            $(obj).next().click(function (e) {
                e.stopPropagation()
            });
            // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
            $(document).one("click", function (e) {
                $(obj).next().removeClass("coursearch").addClass("hide")
                $(obj).next().next().removeClass("hide").addClass("show")
                $(obj).next().next().next().removeClass("show").addClass("hide")
            });
        })
        let popupH = $(".coursearch").height() + 30
        popupHeigth(obj, popupH, '.coursearch')
    }


    /**
    *
    * 编号下拉框联想表格方法
    */
    function codeInput(codeObj) {
        let searchTerm = $(codeObj).val();//输入框内容，用来显示列表
        if (isNaN(searchTerm)) {
            return;
        }
        var index = $($(codeObj).parent().siblings()[0]).text() - 1;
       
        // 弹出下一个兄弟节点
        $(codeObj.nextElementSibling).removeClass("hide")
        $(codeObj.nextElementSibling).addClass("namesearch")
        let searchSuggestions = $(codeObj.nextElementSibling);
        if (searchTerm.length >= 3 && searchTerm.length <= 11) {
            // 此处根据输入框内容调用接口渲染列表
            searchSuggestions.html('');
            // 调用列表接口  列表只展示一部分需要的列  这里是弹出的列表
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetShowListJson")' + '?Code=' + searchTerm,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {

                        if (obj.Data.length > 0) {
                            $("#addTable tr").unbind("click");

                            var comData = obj.Data;
                            var inn = '<table border id="addTable" >'
                            inn += "<thead>";
                            inn += "<tr>";
                            inn += "<th>" + "仪器编码" + "</th>";
                            inn += "<th>" + "仪器名称" + "</th>";
                            inn += "<th>" + "规格属性" + "</th>";
                            inn += "<th>" + "单位" + "</th>";
                            inn += "</tr>";
                            inn += "</thead>";
                            inn += "<tbody>";

                            for (var i = 0; i < comData.length; i++) {
                                inn += "<tr id='trIndex'>";
                                inn += "<td style='width:80px'>" + comData[i].Code + "</td>";
                                inn += "<td style='width:120px'>" + "<div  class='nameContent'>" + comData[i].Name + "</div>" + "</td>";
                                inn += "<td style='width:240px'>" + "<div  class='modelContent'>" + comData[i].Model + "</div>" + "</td>";
                                inn += "<td style='width:50px'>" + comData[i].UnitName + "</td>";
                                inn += "</tr>";
                            }
                            inn += "</tbody>";
                            inn += '</table>';
                            searchSuggestions.html(inn);

                            $(".namesearch tr").bind('click', "tr", function (e) {
                                e.stopPropagation()
                                let idx = $(this).index()
                                let idxObj = comData[idx]
                                // 此处将这条选中的数据替换到页面上

                                course_info.data[index].OriginalCode = idxObj.Code;
                                course_info.data[index].Name = idxObj.Name;
                                course_info.data[index].Model = idxObj.Model;
                                course_info.data[index].UnitName = idxObj.UnitName;
                                course_info.data[index].InstrumentStandardId = idxObj.InstrumentStandardId;
                                course_info.data[index].ModelStandardId = idxObj.ModelStandardId;

                                var tempRow = course_info.data[index];
                                //$.observable(course_info.data).remove(index);
                                //$.observable(course_info.data).insert(index, tempRow);
                                $.observable(course_info.data).remove(index).insert(index, tempRow);

                                $(".namesearch").hide();
                            });
                            $(".namesearch").show();
                            $(".namesearch").css({ "width": "580px" });
                            //console.log($(".namesearch").height())
                            let popupH = $(".namesearch").height() + 30
                            popupHeigth(codeObj, popupH, '.namesearch')

                        } else {
                            //$(codeObj.nextElementSibling).removeClass("namesearch")
                            //$(codeObj.nextElementSibling).addClass("hide")
                            //$(".namesearch").hide();
                            $(".namesearch").html("无数据");
                            $(".namesearch").css({ "width": "120px" });
                        }
                    }
                }
            });
        } else {
            $(codeObj.nextElementSibling).removeClass("namesearch")
            $(codeObj.nextElementSibling).addClass("hide")
        }
        // 失焦隐藏弹窗
        $(codeObj).blur(() => {
            // 点击弹出框内部不隐藏
            $(codeObj.nextElementSibling).click(function (e) {
                e.stopPropagation()
            });
            // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
            $(document).one("click", function (e) {
                $(codeObj.nextElementSibling).removeClass("namesearch")
                $(codeObj.nextElementSibling).addClass("hide")
            });
        });
    }


    /**
    *
    * 仪器名称下拉框联想表格方法
    */
    function nameInput(nameObj) {
        let searchTerm = $(nameObj).val();//输入框内容，用来显示列表
        if (!searchTerm.match(/^[\u4E00-\u9FA5]{1,}$/)) {
            return;
        }

        var index = $($(nameObj).parent().siblings()[0]).text() - 1;
        // 弹出下一个兄弟节点
        $(nameObj.nextElementSibling).removeClass("hide")
        $(nameObj.nextElementSibling).addClass("namesearch")


        let searchSuggestions = $(nameObj.nextElementSibling);
        if (searchTerm.length >= 1 && searchTerm.length <= 30) {
            // 此处根据输入框内容调用接口渲染列表
            searchSuggestions.html('');
            // 调用列表接口  列表只展示一部分需要的列  这里是弹出的列表
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetShowListJson")' + '?Name=' + searchTerm,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {

                        if (obj.Data.length > 0) {
                            $("#addTable tr").unbind("click");

                            var comData = obj.Data;
                            var inn = '<table border id="addTable" >'
                            inn += "<thead>";
                            inn += "<tr>";
                            inn += "<th>" + "仪器编码" + "</th>";
                            inn += "<th>" + "仪器名称" + "</th>";
                            inn += "<th>" + "规格属性" + "</th>";
                            inn += "<th>" + "单位" + "</th>";
                            inn += "</tr>";
                            inn += "</thead>";
                            inn += "<tbody>";

                            for (var i = 0; i < comData.length; i++) {
                                inn += "<tr id='trIndex'>";
                                inn += "<td style='width:80px'>" + comData[i].Code + "</td>";
                                inn += "<td style='width:120px'>" + "<div  class='nameContent'>" + comData[i].Name + "</div>" + "</td>";
                                inn += "<td style='width:240px'>" + "<div  class='modelContent'>" + comData[i].Model + "</div>" + "</td>";
                                inn += "<td style='width:50px'>" + comData[i].UnitName + "</td>";
                                inn += "</tr>";
                            }
                            inn += "</tbody>";
                            inn += '</table>';
                            searchSuggestions.html(inn);

                            $(".namesearch tr").bind('click', "tr", function (e) {
                                e.stopPropagation()
                                let idx = $(this).index()
                                let idxObj = comData[idx]
                                // 此处将这条选中的数据替换到页面上

                                course_info.data[index].OriginalCode = idxObj.Code;
                                course_info.data[index].Name = idxObj.Name;
                                course_info.data[index].Model = idxObj.Model;
                                course_info.data[index].UnitName = idxObj.UnitName;
                                course_info.data[index].InstrumentStandardId = idxObj.InstrumentStandardId;
                                course_info.data[index].ModelStandardId = idxObj.ModelStandardId;

                                var tempRow = course_info.data[index];
                                $.observable(course_info.data).remove(index);
                                $.observable(course_info.data).insert(index, tempRow);

                                $(".namesearch").hide();
                            });
                            $(".namesearch").show();
                            $(".namesearch").css({ "width": "580px" });
                            console.log($(".namesearch").height())
                            let popupH = $(".namesearch").height() + 30
                            popupHeigth(nameObj, popupH, '.namesearch');

                        } else {
                            //$(nameObj.nextElementSibling).removeClass("namesearch")
                            //$(nameObj.nextElementSibling).addClass("hide")
                            //$(".namesearch").hide();
                            $(".namesearch").html("无数据");
                            $(".namesearch").css({ "width": "120px" });
                        }
                    }
                }
            });
        } else {
            $(nameObj.nextElementSibling).removeClass("namesearch")
            $(nameObj.nextElementSibling).addClass("hide")

        }
        // 失焦隐藏弹窗
        $(nameObj).blur(() => {
            // 点击弹出框内部不隐藏
            $(nameObj.nextElementSibling).click(function (e) {
                e.stopPropagation()
            });
            // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
            $(document).one("click", function (e) {
                $(nameObj.nextElementSibling).removeClass("namesearch")
                $(nameObj.nextElementSibling).addClass("hide")
            });
        });

    }

    /**
     *
     * 从仪器库选择
     */
    function saveInstrumentStandard() {
        ys.openDialog({
            title: "选择物品",
            width: "800px",
            content: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/SelectStandard")',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    /**
     * 从达标缺口选择
     */
    function saveGap() {
        ys.openDialog({
            title: "选择物品(<font style='color:red;'>如教育局未设置仪器配备标准的学科，下表就不能显示该学科的仪器清单。</font>)",
            width: "850px",
            content: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/SelectMallProduct")',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    /***
     *
     * 提交计划
     */
    function save(opt) {

        var errorMsg = "";
        var isShowRedError = false;
        var year = $('#purchaseYear').ysComboBox('getValue');
        if (year == "") {
            errorMsg += "请选择采购年度!<br/>";
        }

        var table = $('#gridTable');//找到这个table
        var trs = table.find('tbody td');

        //var trEachLine = table.find('tbody tr');
        ////console.log(IsValidateReason,"获取tr", trEachLine)
        //var trIdx = []
        ////获取行有内容但是未填写完整的行
        //trEachLine.each(function (idx) {
        //    //console.log("tr下td", $(this).children())
        //    let trtds = $(this).children()
        //    let tdArr = []
        //    trtds.each(function (i, v) {
        //        //console.log("tr下每一项td", $(this))
        //        if (i > 1 && $(this).children("input").length) {
        //            tdArr.push($(this).children("input").val())
        //        }
        //        if (i > 1 && $(this).children(".reason").length) {
        //            tdArr.push($(this).children(".reason").html())
        //        }
        //    })
        //    let arr1 = tdArr.every((currentValue, index) => {
        //        return currentValue == '' ;
        //    })
        //    if (!arr1) {
        //        trIdx.push(idx)
        //    }
        //})
        //console.log("trIdx", trIdx)
        ////通过行进行校验
        //trEachLine.each(function (ix) {
        //    //console.log("tr下td", $(this).children())
        //    let trtds = $(this).children()
        //    for (let index = 0; index < trIdx.length; index++) {
        //        if (trIdx[index] == ix) {
        //            trtds.each(function (i, v) {
        //                //console.log("tr下每一项td", $(this))
        //                if ($(this).children("input").val() == "") {
        //                    $(this).children("input").addClass('noform-control');
        //                    isShowRedError = true;
        //                }
        //                if (IsValidateReason == "1") {
        //                    if ($(this).children(".reason")) {
        //                        if ($(this).children(".reason").html() == "") {
        //                            $(this).children(".reason").addClass('noform-control');
        //                            isShowRedError = true;
        //                        }
        //                    }
        //                }

        //            })
        //        }

        //    }
        //})

        trs.each(function () {
            if ($(this).children("input").val() == "") {
                $(this).children("input").addClass('noform-control');
                isShowRedError = true;
            }

            if (IsValidateReason == "1"){
                if ($(this).children(".reason")) {
                    if ($(this).children(".reason").html() == "") {
                        $(this).children(".reason").addClass('noform-control');
                        isShowRedError = true;
                    }
                }
            }
        });

        if (isShowRedError) {
             layer.msg('所有显示红框的地方，必须填写，请检查。', {
                icon: 2,
                skin: 'layer-ext-moon'
            });
            return;
        }
        //
        $(course_info.data).each(function (i, v) {
            var rowMsg = "";
            //if (v.Code == ""){
            //    rowMsg += "分类代码不能为空！";
            //}
            //if (v.Name == ""){
            //    rowMsg += "仪器名称不能为空！";
            //}else{
            //    if (v.Name.length > 200) {
            //        rowMsg += "仪器名称不能大于200个字符！";
            //    }
            //}

            if (v.Name.length > 200) {
                rowMsg += "仪器名称不能大于200个字符！";
            }

            //if(v.Num == ""){
            //    inputMsg += "数量不能为空！";
            //}
            //else{
            //    if (isNaN(v.Num)) {
            //        alertMsg += "数量输入有误！";
            //    } else {
            //        if (v.Num <= 0) {
            //            alertMsg += "数量不能小于等于0！";
            //        } else if (v.Num > 99999999) {
            //            alertMsg += "数量不能大于99999999！";
            //        }
            //    }
            //}

            if (isNaN(v.Num)) {
                rowMsg += "数量输入有误！";
            } else {
                if (v.Num <= 0) {
                    rowMsg += "数量不能小于等于0！";
                } else if (v.Num > 99999999) {
                    rowMsg += "数量不能大于99999999！";
                }
            }

            //if (v.UnitName == "") {
            //    rowMsg += "单位不能为空！";
            //}else{
            //    if (v.UnitName.length > 10){
            //        rowMsg += "单位不能大于10个字符！";
            //    }
            //}
            if (v.UnitName.length > 10) {
                rowMsg += "单位不能大于10个字符！";
            }

            //if (v.Price == "") {
            //    rowMsg += "单价不能为空！";
            //}else{
            //    if (isNaN(v.Price)) {
            //        rowMsg += "价格输入有误！";
            //    } else {
            //        if (v.Price <= 0) {
            //            rowMsg += "价格不能小于等于0！";
            //        } else if (v.Price > 999999999){
            //            rowMsg += "价格不能大于999999999！";
            //        }
            //    }
            //}

            if (isNaN(v.Price)) {
                rowMsg += "价格输入有误！";
            } else {
                if (v.Price <= 0) {
                    rowMsg += "价格不能小于等于0！";
                } else if (v.Price > 999999999) {
                    rowMsg += "价格不能大于999999999！";
                }
            }

            //if (v.Stage == "") {
            //    rowMsg += "适用学段不能为空！";
            //}
            //if (v.Course == "") {
            //    rowMsg += "适用学科不能为空！";
            //}

            if (v.Model != undefined && v.Model != "" && v.Model.length > 255) {
                rowMsg += "规格属性不能超出255个字符！";
            }
            if (v.Reason != "" && v.Reason.length > 500) {
                rowMsg += "采购理由不能超出500个字符！";
            }

            if (rowMsg != "") {
                errorMsg += $.Format("第{0}行,{1}<br/>", (i + 1), rowMsg);
            }

            course_info.data[i].PurchaseYear = year;
            course_info.data[i].RowIndex = i + 1;
        });
        //console.log("postData:" + JSON.stringify(course_info.data));

        if (errorMsg != "") {
            layer.alert(errorMsg, {
                icon: 2,
                skin: 'layer-ext-moon'
            });
        }
        var postData = { list: course_info.data, operateType: opt };
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/SaveBatchFormJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    getForm();
                }
                else {
                    layer.alert(obj.Message, {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                }
            }
        });
    }

    /**
     *
     * 暂存
     */
    function tempSave() {

        //var errorMsg = "";
        var year = $('#purchaseYear').ysComboBox('getValue');
        $(course_info.data).each(function (i, v) {
            //    var rowMsg = "";

            //    if (v.Name == "") {
            //        rowMsg += "仪器名称不能为空！";
            //    } else {
            //        if (v.Name.length > 200) {
            //            rowMsg += "仪器名称不能大于200个字符！";
            //        }
            //    }
            //    if (rowMsg != "") {
            //        errorMsg += $.Format("第{0}行,{1}<br/>", (i + 1), rowMsg);
            //    }
            course_info.data[i].PurchaseYear = year;
            course_info.data[i].RowIndex = i + 1;
        });
        //if (errorMsg != "") {
        //    layer.alert(errorMsg, {
        //        icon: 2,
        //        skin: 'layer-ext-moon'
        //    });
        //    return;


        var postData = { list: course_info.data };
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/SaveTempFormJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    getForm();
                }
                else {
                    layer.alert(obj.Message, {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                }
            }
        });

    }

</script>


