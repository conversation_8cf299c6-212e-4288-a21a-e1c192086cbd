﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
</style>
@{
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
}
<div class="container-div">
 @*   <div class="row" style="height:auto;">
        <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-down"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">排序号是用于各类统计报表中的单位排列前后次序，值越小越靠前。</div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="Id" col="Id" style="display: inline-block; width: 180px;"></div>
                        @*<input Id="Name" col="Name" type="text" placeholder="区县名称" style="display: inline-block;width:200px;" />*@
                    </li>
                    <li>
                        @await Html.PartialAsync("/Areas/OrganizationManage/Shared/EnableDisableIndexPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" role="group">
            @if (operatorInfo.IsThirdLogin == 0)
            {
                <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,'0')"><i class="fa fa-plus"></i> 新增</a>
            }
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadCounty();
        initGrid();
        
        
    });
    function loadCounty() {
        $("#Id").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList")'+"?Pid="+"@Operator.Instance.Current().Result.UnitId",
            key: "Id",
            value: "Name",
            defaultName: '区县名称'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Sort',
            sortOrder: 'ASC',
            columns: [
                {
                    field: 'opt1', title: '操作', width: 200, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var actions = [];
                        var html = '';
                        if (row.Id != undefined) {
                            actions.push($.Format('<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'{0}\');"><i class="fa fa-edit"></i>修改</a>&nbsp;&nbsp;', row.Id));
                            actions.push($.Format('<a class="btn btn-primary btn-xs" href="#" onclick="showSortForm(\'{0}\');"><i class="fa fa-edit"></i>排序</a>&nbsp;&nbsp;', row.Id));
                            if (row.Statuz == 1) {
                                actions.push($.Format('<a class="btn btn-warning btn-xs" href="#" onclick="ShowIsEnableForm(2,\'{0}\');"><i class="fa fa-check"></i>禁用</a>&nbsp;&nbsp;', row.Id));
                            } else {
                                actions.push($.Format('<a class="btn btn-primary btn-xs" href="#" onclick="ShowIsEnableForm(1,\'{0}\');"><i class="fa fa-check"></i>启用</a>&nbsp;&nbsp;', row.Id));
                            }
                            //actions.push($.Format('<a class="btn btn-primary btn-xs" href="#" onclick="deleteForm(\'{0}\');"><i class="fa fa-edit"></i>删除</a>&nbsp;&nbsp;', row.Id));
                        }
                        return actions.join('');
                    }
                },
                { field: 'Name', title: '区县名称', sortable: true, width: 300, halign: 'center', valign: 'middle' },
                { field: 'Sort', title: '排序号', sortable: true, width: 70, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Statuz', title: '单位状态', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html = '--';
                            if (value > 0) {
                                html = top.getDataDictValue('EnableStatuz', value);
                            }
                        }
                        return html;
                    }
                },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#Statuz").ysComboBox('setValue', -1);
        $("#Id").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,editid) {
        var id = 0;
        if (editid == '0' && !bAdd) {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        } else {
            id = editid;
        }
        ys.openDialog({
            title: id > 0 ? "修改单位信息" : "添加单位信息",
            content: '@Url.Content("~/OrganizationManage/Unit/CountyForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSortForm(editid) {
        var id = 0;
        if (editid == '0') {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        } else {
            id = editid;
        }
        ys.openDialog({
            title: "设置单位排序信息",
            content: '@Url.Content("~/OrganizationManage/Unit/UnitSortForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(editid) {
        var ids = editid;
        var message = '确认要删除该数据吗？';
        if (editid == '0') {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (ys.checkRowDelete(selectedRow)) {
                message = ("确认要删除选中的" + selectedRow.length + "条数据吗？");
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/DeleteFormJson")' + '?ids=' + ids,
                type: "post",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function ShowIsEnableForm(IsEnable, editid) {
        var ids = editid + '';
        var message = '确定禁用';
        if (IsEnable==1) {
            message = '确定启用';
        }
        if (editid != '0') {
            message += "该单位信息？";
        } else {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (ys.checkRowDelete(selectedRow)) {
                message += ("选中的" + selectedRow.length + "条数据吗？");
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgError("请选择要启用禁用的数据？");
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/SaveStatuzFormJson")' + '?ids=' + ids + '&isenable=' + IsEnable,
                type: "post",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>
