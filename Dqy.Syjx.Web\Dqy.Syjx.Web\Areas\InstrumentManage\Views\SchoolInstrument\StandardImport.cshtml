﻿
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))

<div class="wrapper animated fadeInRight">
    <div class="btn-group d-flex" role="group" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="divform divStockImport">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">下载模板</label>
                        <div class="col-sm-6">
                            <table id="gridTable" data-mobile-responsive="true"></table>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">选择文件</label>
                        <div class="col-sm-6">
                            <input id="importFile2" type="file">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label "></label>
                        <div class="col-sm-10 text-danger">
                            提示：请先下载模板，必须严格按照模板格式导入数据！
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-save"></i> 保存</a>
        </div>
    </div>
</div>

<script type="text/javascript">
    var filePath2 = undefined;//存量导入文件
    var BasePageCode = 101020;//仪器存量导入模板(101020)
    $(function () {

        $("#importFile2").fileinput({
            language: 'zh',
            'uploadUrl': '@Url.Content("~/File/UploadExcel")' + '?fileModule=@UploadFileType.Import.ParseToInt()',
            showPreview: false,
            allowedFileExtensions: ['xls', 'xlsx']
        }).on("fileuploaded", function (event, data) {
            var obj = data.response;
            if (obj.Tag == 1) {
                filePath2 = obj.Data;
            }
            else {
                filePath2 = '';
            }
        });

        initGrid();

    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/SchoolInstrument/GetInstrumentEvProjectDownloadList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showToolbar: false,
            pagination: false,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                { field: 'EvaluateName', title: '评估项目名称', sortable: true, halign: 'center', valign: 'middle', },
                {
                    field: 'SchoolStage', title: '适用学段', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SchoolStageName;
                    }
                },
                {
                    field: 'DictionaryId1005', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SubjectName;
                    }
                },
                {
                    title: '操作',
                    halign: 'center',
                    align: 'center',
                    width: 50,
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-success btn-xs" href="#" onclick="downloadExcel(\'{0}\',\'{1}\')"><i class="fa fa-download"></i> 下载</a>', row.Id, row.EvaluateName);
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function downloadExcel(id, name) {
        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/ExportInstrumentEvStandardTemplate")';//仪器存量导入模板(101020)
        var postData = { id: id, name: name, basepagecode: 100000000 };
        postData.basepagecode = BasePageCode;//仪器存量导入模板(101020)
        ys.exportExcel(url, postData);
    }

    function saveForm() {
        if (!filePath2) {
            ys.alertError('文件未上传或者上传失败');
            return;
        }
        var postData = $("#form").getWebControls();
        postData.FilePath = filePath2;
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/SchoolInstrument/ImportStockInstrumentJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentIndex")';
                    createMenuAndCloseCurrent(url, "仪器审核");
                }
                else {
                    ys.alertError(obj.Message)

                }
            }
        });
    }
</script>