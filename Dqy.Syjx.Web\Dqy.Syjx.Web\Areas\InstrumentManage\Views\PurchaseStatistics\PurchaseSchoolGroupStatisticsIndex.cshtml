﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="purchaseYear" col="PurchaseYear" style="display:inline-block;width:90px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="StageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                   
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="purchaseType" col="PurchaseType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExportSchoolHz" class="btn btn-warning" onclick="exporthzForm()"><i class="fa fa-download"></i> 导出</a>
            <a id="btnLookByDetail" class="btn btn-success btn-sm" onclick="lookByStage()"><i class="fa fa-navicon"></i>&nbsp;按学科查看</a>
            <a id="btnLookBySchool" style="display:none;" class="btn btn-success btn-sm" onclick="lookByDetail()"><i class="fa fa-navicon"></i>&nbsp;按仪器查看</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var BasePageCode = 101019;//学校年度采购计划汇总表(101019)
    $(function () {

        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            loadSchool(0);
            $('#liSchoolId').show();
            $("#btnLookBySchool").show();
        }
        else if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            loadCounty();
            $('#liCountyId').show();
        }
        
        

        getYear();
        loadSchoolStage();
        loadSubject();

        initGrid();

        $("#purchaseType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(PurchaseTypeEnum).EnumToDictionaryString())), defaultName: '采购方式' });
    });

    function initGrid() {
        $('#gridTable').ysTable({
            url:  '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetPurchaseSchoolGroupListJson")',
            sortName: 'CountySort asc ,CountyId asc ,SchoolSort asc ,SchoolId asc',
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                {
                    field: 'AreaName', title: '区县名称', sortable: true, halign: 'center', align: 'center', visible: (@UnitType == @UnitTypeEnum.City.ParseToInt()), width: 100,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value;
                        }
                        else {
                            if (@UnitType == @UnitTypeEnum.City.ParseToInt() && row.SchoolId == null) {
                                return '<b>总计（元）：</b>';
                            }
                            else {
                                return '';
                            }
                        }
                    }
                },
                {
                    field: 'SchoolName', title: '单位名称', sortable: true, halign: 'center', align: 'left', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt()), width: 150,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value;
                        }
                        else {
                            if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                                return '<b>总计（元）：</b>';
                            }
                            else {
                                return '';
                            }
                        }
                    }
                },
                {
                    field: 'PurchaseYear', title: '采购年度', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.PlanYear,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.SchoolId > 0) {
                            html = $("#purchaseYear").ysComboBox('getValue');
                        }
                        return html;
                    }
                },
                {
                    field: 'AmountSum', title: '采购金额', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        return row.SchoolId ? ComBox.ToLocaleString(value) : '<b>' + ComBox.ToLocaleString(value); + '</b>';
                    }
                },
                {
                    field: 'opt', title: '仪器明细', halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        return row.PurchaseYear ? $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(this)" purchaseyear="{0}" schoolid="{1}" countyid="{2}"><i class="fa fa-eye"></i> 查看</a> '
                            , row.PurchaseYear, row.SchoolId, row.CountyId) : '';
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        //清空条件
        if (@UnitType != @UnitTypeEnum.School.ParseToInt()) {
            $('#schoolId').ysComboBox('setValue', -1);
            loadSchool(0);
        }
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', -1);
        }
        $("#purchaseYear").ysComboBox('setValue', new Date().getFullYear());
        $('#schoolStageId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $("#purchaseType").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function loadSchool(schoolStageId) {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=" + schoolStageId,
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });
                }
            }
        });
    }

    function loadCounty() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#countyId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '区县名称',
                        onChange: function () {
                            if (listLookType == 1)
                                loadSchoolByCountyId($('#countyId').ysComboBox('getValue'));
                        }
                    });
                }
            }
        });
    }

    function loadSchoolByCountyId(countyId) {
        if (countyId > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                data: null,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        $('#schoolId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Name',
                            defaultName: '单位名称'
                        });
                    }
                }
            });
        }
        else {
            $('#schoolId').ysComboBox({ defaultName: '单位名称' });
        }
    }

    function getYear() {
        var currDate = new Date();
        var currYear = currDate.getFullYear();
        var yearData = [];
        for (i = 0; i < 10; i++) {
            if (currYear + 1 - i < 2021)
                continue;
            yearData.push({ "id": currYear + 1 - i, "text": currYear + 1 - i });
        }
        $("#purchaseYear").ysComboBox({
            data: yearData,
            key: "id",
            value: "text",
            defaultName: '采购年度'
        }).ysComboBox('setValue', currYear);
    }

    //查看达标明细
    function lookForm(obj){
        var schoolId = $(obj).attr('schoolid');
        var countyId = $(obj).attr('countyid');
        var purchaseYear = $(obj).attr('purchaseyear');

        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseStatisticsIndex")' + '?purchaseYear='+ purchaseYear +'&schoolId=' + schoolId + '&countyId=' + countyId;
        createMenuItem(url, "查看仪器明细");
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                var schoolStageId = $('#schoolStageId').ysComboBox("getValue");
                if (schoolStageId > 0) {
                    loadSchool(schoolStageId);
                }
            }
        });
    }

    function loadSubject() {
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function exporthzForm() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/ExportPurchaseSchoolStatistics")';//学校年度采购计划汇总表(101019)
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = BasePageCode;//学校年度采购计划汇总表(101019)
        ys.exportExcel(url, postData);
    }

    function lookByDetail() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseStatisticsIndex")' + '?purchaseYear=' + $('#purchaseYear').ysComboBox('getValue');
        createMenuItem(url, "按仪器查看");
    }


    function lookByStage() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseGroupStaticIndex")' + '?purchaseYear=' + $('#purchaseYear').ysComboBox('getValue');
        createMenuItem(url, "按学科查看");
    }
</script>
