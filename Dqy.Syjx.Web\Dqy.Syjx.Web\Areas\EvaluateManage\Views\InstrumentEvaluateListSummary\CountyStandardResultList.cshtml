﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="instrumentEvaluateProjectId" col="InstrumentEvaluateProjectId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="width:150px" />
                    </li>
                    <li>
                        <div id="isOnlyShowNoStandard" col="IsOnlyShowNoStandard" style="display:inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        $('#isOnlyShowNoStandard').ysCheckBox({
            data: [{ Key: 1, Value: '只显示不达标' }]
        });

        loadInstrumentEvaluateProjectVersionId();
        loadSchoolStage();
        loadSubject();
        
        
    });

    function initGrid() {
        if (!$('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0 || !$('#schoolId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        var queryUrl = '@Url.Content("~/EvaluateManage/InstrumentEvaluateListSummary/GetStandardPageList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'SchoolStageName', title: '学段', halign: 'center', align: 'center', sortable: true },
                { field: 'CourseName', title: '学科', halign: 'center', align: 'center', sortable: true },
                { field: 'Code', title: '仪器代码', halign: 'center', align: 'center', sortable: true },
                { field: 'InstrumentName', title: '仪器名称', halign: 'center', align: 'left', sortable: true },
                { field: 'ParamDemand', title: '规格型号', halign: 'center', align: 'left', sortable: true },
                { field: 'StandardValue', title: '指标量', halign: 'center', align: 'center', sortable: true },
                { field: 'StockNum', title: '存量', halign: 'center', align: 'center', sortable: true },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true },
                {
                    field: 'AllocateType', title: '配备要求', halign: 'center', align: 'center', sortable: true,
                    formatter: function (value, row, index) {
                        return value == 1 ? '必配' : '选配';
                    }
                },
                {
                    field: 'Difference', title: '差额', halign: 'center', align: 'center', sortable: true ,
                    formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function loadInstrumentEvaluateProjectVersionId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectBySchool")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#instrumentEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        $('#instrumentEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    loadSchool();
                }
            }
        });
    }

    function loadSchool() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000",
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '学校名称'
                    });
                    $('#schoolId').ysComboBox('setValue', obj.Data[0].Id);

                    initGrid();
                }
            }
        });
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }

    function loadSubject() {
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function searchGrid() {
        if ($('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0 &&
            $('#schoolId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }

    function resetGrid() {
        //清空条件
        $('#schoolStageId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>
