﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .fixed-table-toolbar {
        display: none;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li id="liSchoolYear">
                        <span id="SchoolYearStart" col="SchoolYearStart" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li id="liSchoolTerm">
                        <span id="SchoolTerm" col="SchoolTerm" style="display:inline-block;width:70px;"></span>
                    </li>
                    <li id="liExperimentVersionId">
                        <div id="ExperimentVersionId" col="ExperimentVersionId" style="display: inline-block;width:200px;"></div>
                    </li>
                    <li>
                        <span id="experimentType" col="ExperimentType" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="IsNeedDo" col="IsNeedDo" style="display: inline-block; width: 80px;"></span>
                    </li>
                    <li>
                        <input id="experimentName" col="ExperimentName" style="display:inline-block;width:100px;" placeholder="实验名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar" style="display:none;">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var SourceType = ys.request("s"); //1:计划  2：目录
    var Com_SchoolTermYear = 0;
    var Com_SchoolTerm = 1;
    $(function () {
        loadSearchCombo();
        if (parseInt(SourceType) == 1 || parseInt(SourceType) == 2) {
            $("#liSchoolYear").hide();
            $("#liSchoolTerm").hide();
        }
        if (parseInt(SourceType) == 1) {
            $("#liExperimentVersionId").hide();
        }
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetAddBookingListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="selectPlan(this,\'{0}\',\'{1}\',\'{2}\')"><i class="fa fa-edit"></i>选择</a> ', row.Id, row.SourcePath, row.TextbookVersionCurrentId);
                        }
                        return html;
                    }
                },
                //{
                //    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center', width: 100,
                //    formatter: function (value, row, index) {
                //        var html = "@SchoolTermEnum.LastSemester.GetDescription()";
                //        if (value == @SchoolTermEnum.NextSemester.ParseToInt()) {
                //            html = "@SchoolTermEnum.NextSemester.GetDescription()";
                //        }
                //        return html;
                //    }
                //},
                {
                    field: 'ExperimentName', title: '实验名称', sortable: false, halign: 'center', align: 'left', width: 360,
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.Id) {
                            switch (row.ExperimentType) {
                                case @ExperimentTypeEnum.Demo.ParseToInt():
                                    html = Syjx.GetCircleDemoHtml();
                                    break;
                                case @ExperimentTypeEnum.Group.ParseToInt():
                                    html = Syjx.GetCircleGroupHtml();
                                    break;
                                default:
                                    html += '';
                                    break;
                            }
                            if (row.SourcePath == @SourcePathEnum.School.ParseToInt()) {
                                html += '<span class="font-circle font-circleViolet">校</span>'
                            }
                            // if (row.IsExam == 1) {
                            //     html += Syjx.GetCircleShenRoomHtml();
                            // }
                            if (value && value.length > 0) {
                                html += value;
                            }
                        }
                        return html;
                    }
                },
                { field: 'VersionName', title: '实验教材', sortable: false, halign: 'center', align: 'left', width: 160 },
                {
                    field: 'IsNeedDo', title: '实验要求', sortable: false, halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '@IsNeedEnum.MustDo.GetDescription()';
                        if (@IsNeedEnum.SelectToDo.ParseToInt()== value) {
                            html = '@IsNeedEnum.SelectToDo.GetDescription()';
                        }
                        return html;
                    }
                },
                {
                    field: 'WeekNum', title: '周次', sortable: false, halign: 'center', align: 'center', width: 80, visible: (SourceType == 1),
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.Id) {
                            html = value;
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.Id = id;
                if (parseInt(SourceType) == 2) {
                    queryString.SchoolYearStart = 0;
                    queryString.SchoolTerm = 0;
                } else {
                    queryString.ExperimentVersionId = 0;
                }
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        //resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $('#ExperimentVersionId').ysComboBox('setValue', -1);
        $('#experimentType').ysComboBox('setValue', -1);
        $('#IsNeedDo').ysComboBox('setValue', -1);
        $('#experimentName').val('');
        if (SourceType == 1) {
            //等于1，计划的时候才重置这个
            $('#SchoolYearStart').ysComboBox('setValue', Com_SchoolTermYear);
            $('#SchoolTerm').ysComboBox('setValue', Com_SchoolTerm);
        }
        $('#gridTable').ysTable('search');
        //resetToolbarStatus();
    }
    /**加载搜索条件*/
    function loadSearchCombo() {
        ComBox.SchoolTermYear($('#SchoolYearStart'), undefined, '学年');
        $("#SchoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
        $("#IsNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });
        loadVersionBase();
    }
    function loadVersionBase() {
        var schoolterm = 0;
        if (parseInt($('#SchoolTerm').ysComboBox("getValue")) > 0) {
            schoolterm = parseInt($('#SchoolTerm').ysComboBox("getValue"));
        }
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetCatalogVersionListJson")' + '?Id=' + id + '&SchoolTerm=' + schoolterm,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var arrData = [{ VersionName: "校本实验", Id: 99 }];
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        arrData = arrData.concat(obj.Data);
                    }
                    $('#ExperimentVersionId').ysComboBox({
                        data: arrData,
                        key: 'Id',
                        value: 'VersionName',
                        defaultName: '实验教材版本'
                    });
                }
            }
        });
    }

    function selectPlan(obj, experimentid, sourcepath, textbookversioncurrentid) {
        if (!(textbookversioncurrentid != undefined && parseFloat(textbookversioncurrentid)>0)){
            textbookversioncurrentid=0;
        }
        //确认要添加该实验
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/SaveBookingAddExperimentJson")' + "?Id=" + id + '&textbookversioncurrentid=' + textbookversioncurrentid + "&ExperimentId=" + experimentid + '&SourcePath=' + sourcepath,
            type: 'POST',
            success: function (obj) {
                if (obj.Tag == 1) {
                    parent.addExperiment(obj.Data);
                    parent.layer.closeAll();
                } else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>
