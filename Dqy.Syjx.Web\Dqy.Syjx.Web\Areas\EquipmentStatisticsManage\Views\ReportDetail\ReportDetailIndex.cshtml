﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        ReportId：<input id="reportId" col="ReportId" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'ReportId', title: 'ReportId' },
                { field: 'TotalAmount', title: 'TotalAmount' },
                { field: 'CurrentYearAmount', title: 'CurrentYearAmount' },
                { field: 'ClassNum', title: 'ClassNum' },
                { field: 'StudentNum', title: 'StudentNum' },
                { field: 'TeacherNum', title: 'TeacherNum' },
                { field: 'MediaDeviceNum', title: 'MediaDeviceNum' },
                { field: 'MediaFunroomNum', title: 'MediaFunroomNum' },
                { field: 'MediaInteractiveNum', title: 'MediaInteractiveNum' },
                { field: 'MedialaboratoryNum', title: 'MedialaboratoryNum' },
                { field: 'MediaOtherNum', title: 'MediaOtherNum' },
                { field: 'ComputerStudentNum', title: 'ComputerStudentNum' },
                { field: 'ComputerTeacherNum', title: 'ComputerTeacherNum' },
                { field: 'ComputerPadNum', title: 'ComputerPadNum' },
                { field: 'ComputerPortableNum', title: 'ComputerPortableNum' },
                { field: 'NetworkAdminNum', title: 'NetworkAdminNum' },
                { field: 'NetworkFulltimeNum', title: 'NetworkFulltimeNum' },
                { field: 'BookTotalNum', title: 'BookTotalNum' },
                { field: 'BookNewYearNum', title: 'BookNewYearNum' },
                { field: 'BookCabinetNum', title: 'BookCabinetNum' },
                { field: 'BookPavilionNum', title: 'BookPavilionNum' },
                { field: 'BookLibraryArea', title: 'BookLibraryArea' },
                { field: 'BookdRoomSeatNum', title: 'BookdRoomSeatNum' },
                { field: 'SportRunwayLength', title: 'SportRunwayLength' },
                { field: 'SportRoomNum', title: 'SportRoomNum' },
                { field: 'SportPingPongNum', title: 'SportPingPongNum' },
                { field: 'SportBasketballNum', title: 'SportBasketballNum' },
                { field: 'SportVolleyballNum', title: 'SportVolleyballNum' },
                { field: 'SportFootballNum', title: 'SportFootballNum' },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/ReportDetailForm")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }
</script>
