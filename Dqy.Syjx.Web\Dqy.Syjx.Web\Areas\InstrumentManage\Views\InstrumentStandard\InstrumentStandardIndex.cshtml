﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.js"))
}

<div class="container-div">
@*    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、组织结构是指单位内的构成部门；<br />
                            2、建立部门是用于人员的归属、设备仪器的归属及按部门查询统计。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input id="code" col="Code" type="text" placeholder="分类代码" />
                    </li>
                    <li>
                        <input id="name" col="Name" type="text" placeholder="品种名类" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchTreeGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" role="group">
            @*<a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
            <a id="btnExpandAll" class="btn btn-info"><i class="fa fa-exchange"></i> 展开/折叠</a>*@
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var id = 0;
    $(function () {
        initTreeGrid();
        
        
    });

    function initTreeGrid() {
        var options = {
            code: "Id",
            parentCode: "Pid",
            uniqueId: "Id",
            expandAll: false,
            expandFirst: false,
            toolbar: '#toolbar',
            expandColumn: '1',
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetList")',
            modalName: "部门",
            columns: [
                { field: 'selectItem', radio: true },
                { field: 'Code', title: '分类代码' },
                {
                    field: 'Name', title: '品种名称',
                    formatter: function (value, row, index) {
                        if (row.ClassType == 2) return row.Model;
                        else return value;
                    }
                },
                { field: 'UnitName', title: '计量单位' },
                {
                    field: 'Statuz', title: '状态', width: '10%', align: "left",
                    formatter: function (value, row, index) {
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'BaseModifyTime', title: '创建时间', align: "left",
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
                {
                    field: 'opt', title: '操作', align: "left",
                    formatter: function (value, row, index) {
                        let html = '';
                        if (row.IsLast == 1 && row.ClassType == 1) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="showSaveForm(true,this)" value="{0}"><i class="fa fa-plus"></i> 添加规格</a> ', row.Id);
                        }
                        if (row.ClassType == 2) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="showSaveForm(false,this)" value="{0}"><i class="fa fa-edit"></i> 修改</a> ', row.Id);
                            if (row.Statuz == @StatusEnum.Yes.ParseToInt()) {
                                html += $.Format('<a class="btn btn-warning btn-xs" href="#" onclick="setStatus(this)" value="{0}" status="@StatusEnum.No.ParseToInt()"><i class="fa fa-close"></i> 禁用</a> ', row.Id);
                            }
                            else {
                                html += $.Format('<a class="btn btn-primary btn-xs" href="#" onclick="setStatus(this)" value="{0}" status="@StatusEnum.Yes.ParseToInt()"><i class="fa fa-check"></i> 启用</a> ', row.Id);
                            }
                        }
                        return html;
                    }
                }
            ],
            onLoadSuccess: function () {
                if (id != 0) {
                    $('#gridTable').ysTreeTable('expandRowById', id);
                }
            }
        };
        $('#gridTable').ysTreeTable(options);
    }

    function searchTreeGrid(callBackId) {
        var param = $("#searchDiv").getWebControls();
        $('#gridTable').ysTreeTable('search', param);
         if (callBackId) {
            id = callBackId;
        }
    }
    function resetGrid() {
        //清空条件
        $('#code').val('');
        $('#name').val('');
        var param = $("#searchDiv").getWebControls();
        $('#gridTable').ysTreeTable('search', param);
        if (callBackId) {
            id = callBackId;
        }
    }

    function showSaveForm(bAdd ,obj) {
        var id = 0;
        var pid = 0;
        if (!bAdd) {
            id = $(obj).attr('value');
        }
        else {
            pid = $(obj).attr('value');
        }
        ys.openDialog({
            title: !bAdd ? "编辑规格属性" : "添加规格属性",
            content: '@Url.Content("~/InstrumentManage/InstrumentStandard/InstrumentStandardForm")' + '?pid=' + pid + '&id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function setStatus(obj) {
        let id = $(obj).attr('value');
        let status = $(obj).attr('status');
        ys.confirm($.Format("您确认{0}此条数据吗？", status == "@StatusEnum.Yes.ParseToInt()" ? "启用" : "禁用"), function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/SetStatusJson")' + '?id=' + id + '&status=' + status,
                type: "post",
                error: ys.ajaxError,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchTreeGrid(id);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>