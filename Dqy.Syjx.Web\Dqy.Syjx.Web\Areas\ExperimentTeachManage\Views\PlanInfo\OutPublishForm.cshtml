﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .span-left-tag {
        margin-left: 82px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="GradeId" col="GradeId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="ExperimentType" col="ExperimentType" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" placeholder="名称" style="display: inline-block;width:180px;" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnPublish" class="btn btn-success" onclick="showSaveForm()"><i class="fa fa-rss"></i> 发布计划</a>
            <div>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var id = ys.request("id");//计划id
    $(function () {
        loadGrade();
        loadExperimentType();
        initGrid();


    });
    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolGradeListByPidJson")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '适用年级',
        });
    }
    function loadExperimentType() {
        $('#ExperimentType').ysComboBox({
            url: '@Url.Content("~/SystemManage/DataDictDetail/GetSortListJson")' + '?TypeCode=OET101001',
            key: 'DictKey',
            value: 'DictValue',
            defaultName: '实验类型',
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetOutExperimentPageListJson")' + '?Statuzge=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } }, 
                {
                    field: 'SchoolYearStart', title: '学期',  width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2);
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== value) {
                            html += '@SchoolTermEnum.NextSemester.GetDescription()';
                        } else {
                            html += '@SchoolTermEnum.LastSemester.GetDescription()';
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '适用学科', width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'GradeName', title: '适用年级', halign: 'center', valign: 'middle', align: 'center', width: 100 },
                { field: 'ExperimentTypeName', title: '实验类型', sortable: true, width: 80, halign: 'center', align: 'center' },
                { field: 'ExperimentName', title: '实验名称', sortable: true, halign: 'center', valign: 'middle', align: 'left' },
                {
                    field: 'opt', title: '操作', halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<span class="btn btn-look btn-xs" onclick="showSaveForm(\'' + row.Id + '\')"><i class="fa fa-look"></i>查看</span>'); 
                        return actions.join('');
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.Id = id;
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#GradeId").ysComboBox('setValue', -1);
        $("#ExperimentType").ysComboBox('setValue', -1);
        $("#Name").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    } 

    //发布
    function showSaveForm() {
        ys.confirm('确定要发布当前计划吗？发布成功后将跳转到发布计划列表。', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/PublishOutFormJson")' + '?ids=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        //跳转发布表
                        var url = '/ExperimentTeachManage/PlanInfo/OutPublishList';
                        createMenuAndCloseCurrent(url, "发布计划");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    //查看详情页面
    function showDetailForm(){

    }
</script>
