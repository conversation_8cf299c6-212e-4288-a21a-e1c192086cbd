﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";

}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="BackupYear" col="BackupYear" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolId" col="SchoolId" style="display: inline-block; width: 180px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var columnArr = [];
    var Current_QueryYear = new Date().getFullYear();
    $(function () {
        $('#SchoolId').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenHasKindergartenPageList")' + "?PageSize=10000", key: 'Id', value: 'Name',
            defaultName: '单位名称',
            minimumResultsForSearch: 0
        });
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        initGrid();

    });

    function initGrid() {

        var columns = [
            {
                field: 'PhysicsFrNum', title: '数量<br />（间）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    return '';
                }
            },
            {
                field: 'PhysicsFrArea', title: '总面积<br />（㎡）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value ;
                    return '';
                }
            },
            {
                field: 'ChemistryFrNum', title: '数量<br />（间）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    return '';
                }
            },
            {
                field: 'ChemistryFrArea', title: '总面积<br />（㎡）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    return '';
                }
            },
            {
                field: 'BiologyFrNum', title: '数量<br />（间）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    return '';
                }
            },
            {
                field: 'BiologyFrArea', title: '总面积<br />（㎡）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    return '';
                }
            },
            {
                field: 'ScienceFrNum', title: '数量<br />（间）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    return '';
                }
            },
            {
                field: 'ScienceFrArea', title: '总面积<br />（㎡）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    return '';
                }
            },
            { 
                field: 'BookTotalNum', title: '藏书量<br />（册）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookTotalNum > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'StudentBookAvg', title: '生均图书<br />（册）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.StudentBookAvg > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return '';
                }
            },
            {
                field: 'BookNewYearNum', title: '当年新增图<br />书（册）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookNewYearNum > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'StudentBookNewAvg', title: '当年生均新增<br />图书（册）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.StudentBookAvg > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return '';
                }
            },
            {
                field: 'BookLibraryArea', title: '图书馆总面<br />积（平米）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookLibraryArea > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                }
            },
            {
                field: 'BookRoomSeatNum', title: '阅览室座位<br />（个）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookRoomSeatNum > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'BookReadersTerminal', title: '供读者使用的<br />终端（台）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookReadersTerminal > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'BookCabinetNum', title: '移动图书柜<br />（组）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookCabinetNum > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'BookPavilionNum', title: '朗读亭<br />（组）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookPavilionNum > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'BookAdminNum', title: '图书管理员<br />（人）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookAdminNum > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'BookFulltimeNum', title: '其中专职<br />（人）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.BookFulltimeNum > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + (!(value >0 ))? "0":value.toLocaleString() + '</b>';
                }
            },
        ];
        var columnsGroup = [
            {
                field: 'index', title: '序号', colspan: 1, rowspan: 2, width: 60, halign: 'center', valign: 'middle', align: 'center',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return index + 1;
                    else return '';
                }
            },
            {
                field: 'SchoolCode', title: '<div style="width:80px;">单位编号</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    else return '';
                }

            },
            {
                field: 'SchoolName', title: '<div style="width:120px;">单位名称</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
                formatter: function (value, row, index) {
                    var html = value;
                    if (html == "<b>总计：<b>") {
                        html = "<div style='text-align:center;'><b>总计：<b></div>";
                    }
                    return html;
                }
            },
            { title: '物理实验室', align: 'center', colspan: 2 },
            { title: '化学实验室', align: 'center', colspan: 2 },
            { title: '生物实验室', align: 'center', colspan: 2 },
            { title: '科学实验室', align: 'center', colspan: 2 },
            { title: '图书馆', align: 'center', colspan: 11 }
        ];
        columnArr = [
            columnsGroup,
            columns
        ];
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            var postData = {
                ModuleId: @BackupsModuleIdEnum.ZbStatistics.ParseToInt(),
                PageCode: "@BackupsPageCodeEnum.Book.ParseToInt().ToString()",
                BackupType: @BackupTypeEnum.DataTitleJson.ParseToInt(),
                BackupYear: thisYear
            };
            $.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetBackupsTitleJson")',
                async: false,
                type: 'get',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var colArr = obj.Data[0].Content;
                        if (typeof (colArr) == "string") {
                            colArr = JSON.parse(colArr);
                        }
                        if (colArr && colArr.length > 0) {
                            columnArr = [];
                            for (var i = 0; i < colArr.length; i++) {
                                columnArr.push(Syjx.GetGridTableColumns(colArr[i]));
                            }
                        }
                    } else {

                    }
                }
            });
        }
        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetBookPageListJson")';

        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Sort ASC ,SchoolId DESC ',
            columns: columnArr,
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        if (Current_QueryYear != thisYear && parseInt(thisYear) > 0) {
            Current_QueryYear = thisYear;
            $('#gridTable').bootstrapTable('destroy');
            initGrid();//重新表头。
        } else {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
    }

    function resetGrid() {
        $("#SchoolId").ysComboBox('setValue', -1);
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function exportForm() {
        var url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/BookExport")';
        var postData = $("#searchDiv").getWebControls();
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/BackupExport")';
            postData.PageCode = '@BackupsPageCodeEnum.Book.ParseToInt().ToString()';
        }
        ys.exportExcel(url, postData);
    }
</script>
