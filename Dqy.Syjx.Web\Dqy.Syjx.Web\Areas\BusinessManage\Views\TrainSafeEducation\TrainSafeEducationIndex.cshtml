﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>实施时间： </label>
                        <input id="startTime" col="StartTime" type="text" class="time-input" placeholder="开始时间" />
                        <span>至</span>
                        <input id="endTime" col="EndTime" type="text" class="time-input" placeholder="结束时间" />
                    </li>
                    <li>
                        <div id="Trainees" col="Trainees" style="display: inline-block;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>


<script type="text/javascript">
    $(function () {

        $("#Trainees").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(TrainingObject).EnumToDictionaryString())),
            defaultName: '培训对象'
        });

        laydate.render({ elem: '#startTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });


        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/BusinessManage/TrainSafeEducation/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'Name', title: '培训/教育项目名称', width: 120, sortable: true, halign: 'center', align: 'left', },
                { field: 'PersonCharge', title: '实施负责人', width: 120, sortable: true, halign: 'center', align: 'center', },
                { field: 'Address', title: '实施地点', width: 120, sortable: true, halign: 'center', align: 'left', },
                {
                    field: 'EffectiveDate', title: '实施时间', width: 100, sortable: true,halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
                {
                    field: 'Trainees', title: '培训/教育对象', width: 120, sortable: true,halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = "学生";
                        if (value == 2) {
                            html = "老师";
                        } else if (value == 3) {
                            html = "学生,老师";
                        }
                        return html;
                    }
                },
                {
                    field: 'BaseCreateTime', title: '填报时间', width: 100, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function clearGrid() {
        $('#Trainees').ysComboBox('setValue', -1)
        $("#startTime").val("");
        $("#endTime").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/BusinessManage/TrainSafeEducation/TrainSafeEducationForm")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/BusinessManage/TrainSafeEducation/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

     
</script>
