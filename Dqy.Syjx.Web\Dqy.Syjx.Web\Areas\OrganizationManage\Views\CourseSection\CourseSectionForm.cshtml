﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment

@*@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery-timepicker/css/timePicker.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery-timepicker/js/timepicker.js"),true)*@
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.1/laydate.js"),true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsrender.js"),true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsviews.js"),true)

<style>
    /* 设置只展示时分，隐藏秒那一列 */
    .laydate-time-list {
        padding-bottom: 0;
        overflow: hidden;
    }

        .laydate-time-list > li {
            width: 50% !important;
        }

            .laydate-time-list > li:last-child {
                display: none;
            }

        .laydate-time-list ol li {
            width: 100% !important;
            padding-left: 0 !important;
            text-align: center !important;
        }
</style>

<div class="wrapper animated fadeInRight">

    <div id="divCourseInfo" class="wrapper animated fadeInRight">
    </div>

</div>

<script id="dateList" type="text/x-jsrender">
    <div class="col-sm-12 select-table table-striped">
        <div class="bootstrap-table">
            <div class="fixed-table-toolbar">
                <div class="bs-bars pull-left">
                    <div id="toolbar" class="btn-group d-flex" role="group">
                        <a id="btnAdd" class="btn btn-success" data-link="{on addRow}"><i class="fa fa-plus"></i> 添加</a>
                        <a id="btnDelete" class="btn btn-danger" data-link="{on deleteRow}"><i class="fa fa-remove"></i> 删除</a>
                        <a id="btnDelete" class="btn btn-info" data-link="{on clearRow}"><i class="fa fa-recycle"></i>清空</a>
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt divClear" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right" data-content="课间操、用餐和午休时间段无需设置。"></span>
                    </div>
                </div>
            </div><div class="fixed-table-container" style="padding-bottom: 0px;">
                <div class="fixed-table-header" style="display: none;">
                    <table></table>
                </div>
                <div class="fixed-table-body">
                    <table id="gridTable" data-mobile-responsive="true" class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th style="text-align: center; vertical-align: middle;" colspan="4">
                                    <div class="th-inner sortable both">夏季时间</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;" colspan="4">
                                    <div class="th-inner sortable both">冬季时间
                                        &nbsp;&nbsp;<a id="btnCopy" class="btn btn-info"><i class="fa fa-copy"></i>同夏季时间</a>
                                    </div><div class="fht-cell"></div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="list">
                             {^{for data}}
                                 <tr>
                                    <td width="60px">第{^{:#index + 1}}节</td>
                                    <td><input name="sectionDate" id="xj_beginTime_{{>RIndex}}" col="xjBeginTime" type="text" value="{{>BeginTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>
                                    <td>至</td>
                                    <td><input name="sectionDate" id="xj_endTime_{{>RIndex}}" col="xjEndTime" type="text" value="{{>EndTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>

                                    <td>&nbsp;&nbsp;</td>
                                    <td><input name="sectionDate" id="dg_beginTime_{{>RIndex}}" col="djBeginTime" type="text" value="{{>DjBeginTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>
                                    <td>至</td>
                                    <td><input name="sectionDate" id="dg_endTime_{{>RIndex}}" col="djEndTime" type="text" value="{{>DjEndTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>
                                </tr>
                             {{/for}}
                        </tbody>
                    </table>
                </div>
            </div>
        </div><div class="clearfix"></div>
    </div>
</script>

<script type="text/javascript">
    var id = ys.request("gradeId");
    var weekId = ys.request("weekId");
    var course_info = null;


    $(function () {

        course_info = {
            data: [],
            addRow: function () {
                var rowIndex = parseInt(this.data[this.data.length - 1].RIndex) + 1;
                $.observable(this.data).insert({ RIndex: rowIndex, BeginTime: '', EndTime: '', DjBeginTime: '', DjEndTime: '', });
                // 时间插件
                laydate.render({
                    elem: '#xj_beginTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
                laydate.render({
                    elem: '#xj_endTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
                laydate.render({
                    elem: '#dg_beginTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
                laydate.render({
                    elem: '#dg_endTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
            },
            deleteRow: function () {
                $.observable(this.data).remove(this.data.length - 1);
                if (this.data.length == 0) {

                    $.observable(this.data).insert({ RIndex: 1, BeginTime: '', EndTime: '', DjBeginTime: '', DjEndTime: '', });

                    // 时间插件
                    laydate.render({
                        elem: '#xj_beginTime_1',
                        trigger: 'click',
                        type: 'time',
                        format: 'HH:mm',
                        min: '06:00:00',
                        max: '23:00:00',
                    });
                    laydate.render({
                        elem: '#xj_endTime_1',
                        trigger: 'click',
                        type: 'time',
                        format: 'HH:mm',
                        min: '06:00:00',
                        max: '23:00:00',
                    });
                    laydate.render({
                        elem: '#dg_beginTime_1',
                        trigger: 'click',
                        type: 'time',
                        format: 'HH:mm',
                        min: '06:00:00',
                        max: '23:00:00',
                    });
                    laydate.render({
                        elem: '#dg_endTime_1',
                        trigger: 'click',
                        type: 'time',
                        format: 'HH:mm',
                        min: '06:00:00',
                        max: '23:00:00',
                    });
                }
            },
            bindDate: function () {
                // 时间插件
                laydate.render({
                    elem: '.time-picker',
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                    //ready: function (date) {
                    //    //var col=$(this).val();
                    //    //console.log("col:"+col);
                    //    console.log("this:"+JSON.stringify($(this)));
                    //    console.log("data:"+JSON.stringify(date));
                    //},
                    //change: function (value, date, endDate) {
                    //    console.log("value:" + value);
                    //    console.log("date:" + JSON.stringify(date));
                    //    console.log("endDate:" + JSON.stringify(endDate));
                    //},
                    //done: function (value, date) {
                    //    console.log("value:"+value);
                    //    console.log("date:" +JSON.stringify(date));
                    //}
                    //$("input[name='sectionDate']").each(function (index, obj) {
                    //    var rowIndex = $(this).attr("rowIndex");
                    //    laydate.render({
                    //        elem: '#xj_beginTime_' + rowIndex,
                    //        trigger: 'click',
                    //        type: 'time',
                    //        format: 'HH:mm',
                    //        min: '06:00:00',
                    //        max: '23:00:00',
                    //        ready: function (date) {
                    //            console.log("date:" + JSON.stringify(date));
                    //            if (rowIndex == 1){
                    //                //设置max
                    //                var endTime = $("#xj_endTime_" + rowIndex).val();
                    //                if (endTime != undefined && endTime!= ""){
                    //                    var strs = endTime.split(':');
                    //                    console.log("strs[0]:" + strs[0]);
                    //                    $(this).config.max = strs[0] + ":00:00";
                    //                }

                    //            }else{

                    //            }
                    //        }
                    //    });

                    //    laydate.render({
                    //        elem: '#xj_endTime_' + rowIndex,
                    //        trigger: 'click',
                    //        type: 'time',
                    //        format: 'HH:mm',
                    //        min: '06:00:00',
                    //        max: '23:00:00',
                    //    });

                    //    laydate.render({
                    //        elem: '#dg_beginTime_' + rowIndex,
                    //        trigger: 'click',
                    //        type: 'time',
                    //        format: 'HH:mm',
                    //        min: '06:00:00',
                    //        max: '23:00:00',
                    //    });

                    //    laydate.render({
                    //        elem: '#dg_endTime_' + rowIndex,
                    //        trigger: 'click',
                    //        type: 'time',
                    //        format: 'HH:mm',
                    //        min: '06:00:00',
                    //        max: '23:00:00',
                    //    });
                    //});
                });
            },
            clearRow:function(){
                $(".time-picker").val("");
            }
        };
        $.templates("#dateList").link("#divCourseInfo", course_info);
        getForm();


        //同夏季时间点击按钮
        $("#btnCopy").click(function(){
            var arrLength = course_info.data.length;
            if (arrLength == 0) {
                ys.msgError("无夏季时间可复制");
                return;
            }
            for (var i = 0; i < arrLength; i++) {
                var BeginTime = $("#xj_beginTime_" + (i + 1)).val();
                var EndTime = $("#xj_endTime_" + (i + 1)).val();
                $("#dg_beginTime_" + (i + 1)).val(BeginTime);
                $("#dg_endTime_" + (i + 1)).val(EndTime);
            }
        });

     });



    function getForm() {
        if (id > 0) {

            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/CourseSection/GetCourseSectionList")' + '?gradeId=' + id + "&weekId=" + weekId,
                type: 'get',
                success: function (obj) {

                    if (obj.Tag == 1) {
                        //console.log("obj.Data:" + JSON.stringify(obj.Data));
                        if (obj.Data.length > 0){
                            var unitId = obj.Data[0].UnitId;
                        }
                        $.observable(course_info).setProperty("data", obj.Data);
                        course_info.bindDate();
                    }

                }
            });
        }
        $(".divClear").show();
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });
    }

    function saveForm(index) {

        //遍历
        var arrLength = course_info.data.length;
        if (arrLength == 0) {
            ys.msgError("无数据可供保存");
            return;
        }

        var isReal = false;
        //判断是否都为空
        for (var i = 0; i < arrLength; i++) {
            var RowIndex = $("#xj_beginTime_" + (i + 1)).attr("rowIndex");
            var BeginTime = $("#xj_beginTime_" + (i + 1)).val();
            var EndTime = $("#xj_endTime_" + (i + 1)).val();
            var DjBeginTime = $("#dg_beginTime_" + (i + 1)).val();
            var DjEndTime = $("#dg_endTime_" + (i + 1)).val();
            if (BeginTime != "" || EndTime != "" || DjBeginTime != "" || DjEndTime != ""){
                isReal = true;
                break;
            }
        }

        if (isReal){
            var listArray = [];
            for (var i = 0; i < arrLength; i++) {

                var RowIndex = $("#xj_beginTime_" + (i + 1)).attr("rowIndex");
                var BeginTime = $("#xj_beginTime_" + (i + 1)).val();
                var EndTime = $("#xj_endTime_" + (i + 1)).val();
                var DjBeginTime = $("#dg_beginTime_" + (i + 1)).val();
                var DjEndTime = $("#dg_endTime_" + (i + 1)).val();

                if (BeginTime == "" || EndTime == "" || DjBeginTime == "" || DjEndTime == "") {
                    ys.msgError("请完成所有课程节次设置不能留空，不需要的节次可以点击删除!");
                    return;
                }

                if (EndTime <= BeginTime) {
                    ys.msgError("夏季时间：第" + RowIndex + "节，结束时间不能小于等于开始时间!");
                    return;
                }

                if (DjEndTime <= DjBeginTime) {
                    ys.msgError("冬季时间：第" + RowIndex + "节，结束时间不能小于等于开始时间!");
                    return;
                }

                //var obj = { RowIndex: RowIndex, BeginTime: BeginTime, EndTime: EndTime, DjBeginTime: DjBeginTime, DjEndTime: DjEndTime };
                course_info.data[i].RowIndex = RowIndex;
                course_info.data[i].BeginTime = BeginTime;
                course_info.data[i].EndTime = EndTime;
                course_info.data[i].DjBeginTime = DjBeginTime;
                course_info.data[i].DjEndTime = DjEndTime;
            }

            for (var i = 0; i < arrLength; i++) {
                if (i > 0) {
                    if (course_info.data[i].BeginTime < course_info.data[i - 1].EndTime) {
                        ys.msgError("夏季时间：第" + course_info.data[i].RowIndex + "节，开始时间不能小于上一节课结束时间!");
                        return;
                    }
                    if (course_info.data[i].DjBeginTime < course_info.data[i - 1].DjEndTime) {
                        ys.msgError("冬季时间：第" + course_info.data[i].RowIndex + "节，开始时间不能小于上一节课结束时间!");
                        return;
                    }
                }
            }

            var postData = {
                list: course_info.data, gradeId: id, weekId: weekId
            };

            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/CourseSection/SaveBatchForm")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }else{
            //清空所有数据
            //console.log("清空所有数据");
            var postData = {
                gradeId: id, weekId: weekId
            };
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/CourseSection/SaveBatchForm")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
       
    }

</script>

