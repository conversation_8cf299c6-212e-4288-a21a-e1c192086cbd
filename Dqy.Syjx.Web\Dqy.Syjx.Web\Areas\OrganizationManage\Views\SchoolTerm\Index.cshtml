﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container { width: 100% !important; }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolYearStart" col="SchoolYearStart" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        /*loadSchoolYearComboBox();*/
        ComBox.SchoolTermYear($('#SchoolYearStart'), '', '学年');
        $("#SchoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())) });
        initGrid();
        
        
    });

    function decimal(amount, v) {
        var vv = Math.pow(10, v);
        return Math.round(amount * vv) / vv;
    }
    function loadSchoolYearComboBox() {
        /**/
        var arr = [];
        var startYear = new Date().getFullYear();
        for (var i = startYear; i > (startYear - 10); i--) {
            var name = (i + '').substr(2);
            name = parseInt(name);
            arr.push({ id: i + ',' + i, name: name + '~' + name })
            arr.push({ id: (i - 1) + ',' + i, name: (name - 1) + '~' + name })
        }
        arr.unshift({ id: startYear + ',' + (startYear + 1), name: (startYear + '').substr(2) + '~' + (parseInt((startYear + '').substr(1)) + 1) });
        arr.unshift({ id: (startYear + 1) + ',' + (startYear + 1), name: (parseInt((startYear + '').substr(1)) + 1) + '~' + (parseInt((startYear + '').substr(1)) + 1) });
        $('#SchoolYear').ysComboBox({
            data: arr,
            key: 'id',
            value: 'name'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/SchoolTerm/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'opt1', title: '操作', halign: 'center', align: 'center', width: 150, formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="showSaveForm(false,\'{0}\');"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'{0}\');"><i class="fa fa-remove"></i>删除</a> ', row.Id);

                        }
                        return html;
                    }
                },
                {
                    field: 'SchoolYearStart', title: '学年', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.SchoolYearStart != undefined && ('' + row.SchoolYearStart).length == 4) {
                            html += ('' + row.SchoolYearStart).substr(2);
                        }
                        if (row.SchoolYearEnd != undefined && ('' + row.SchoolYearEnd).length == 4) {
                            html += '~';
                            html += ('' + row.SchoolYearEnd).substr(2);
                        }
                        return html;
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.SchoolTerm == "@SchoolTermEnum.LastSemester.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@SchoolTermEnum.LastSemester.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-primary">' + "@SchoolTermEnum.NextSemester.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'TermStart', title: '学期起始日', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                },
                {
                    field: 'TermEnd', title: '学期终止日', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                },
                {
                    field: 'FirstWeekDate', title: '第一周开始日', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                },
                {
                    field: 'optlook', title: '校历表', width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return '&nbsp;<a onclick="showDetailForm(\'' + row.Id + '\');">查看</a>&nbsp;';
                    }
                },
                { field: 'Remark', title: '备注', halign: 'center', align: 'left',width: 300,  },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $('#SchoolTerm').ysComboBox('setValue', -1);
        $("#SchoolYearStart").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError("请选择需要修改的数据。");
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/OrganizationManage/SchoolTerm/Form")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
     function showDetailForm(id) {
        ys.openDialog({
            title: '校历表',
            content: '@Url.Content("~/OrganizationManage/SchoolTerm/Detail")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm(id) {
        var ids = '';
        var msg = '';
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                ids = ys.getIds(selectedRow);
                msg = $.Format('确认要删除选中的{0}条数据吗？', selectedRow.length);
            } else {
                ys.msgSuccess('请选择需要删除的数据！');
                return;
            }
        } else {
            ids = id;
            msg = '确认要删除选中当前这条数据吗？';
        }
        ys.confirm(msg, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolTerm/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    function removeConfirm() {
        $(".layui-layer-btn0").hide();
    }
</script>
