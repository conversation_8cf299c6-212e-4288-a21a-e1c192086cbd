﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int SchoolYearStart = (int)ViewBag.SchoolYearStart;
    int SchoolTerm = (int)ViewBag.SchoolTerm;
    int UnitType = (int)ViewBag.UnitType;
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="schoolYearStart" col="SchoolYearStart" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li id="liSchoolStage">
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block; width:100px;"></span>
                    </li>
                    <li>
                        <span id="gradeId" col="GradeId" style="display:inline-block;width:110px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li style="display:none;" id="liCompulsoryType">
                        <span id="CompulsoryType" col="CompulsoryType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <li id="liExport" style="display:none;">
                        <a id="btnExport" class="btn btn-warning btn-sm" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var IsGenderData = 0; //搜索时是否需要生成数据
    var LastSchoolYearStart = 0; //前一次搜索时的学年
    var LastSchoolTerm = 0; //前一次搜索时的学期
    var MenuType = ys.request("t");//1:义务教育开出率    2:高中教育开出率
    $(function () {
        if (!(MenuType==2)){
            MenuType = 1;
        }
        ComBox.SchoolTermLast3Year($('#schoolYearStart'), undefined, '学年');
        $('#schoolYearStart').ysComboBox('setValue', @SchoolYearStart);

        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        $("#schoolTerm").ysComboBox('setValue', @SchoolTerm);

        if (@UnitType == @UnitTypeEnum.School.ParseToInt()) { //学校显示导出按钮
            $('#liExport').show();
        }

        loadSchoolStage();
        loadCourse();
        if (MenuType == 2) {
            $("#liSchoolStage").hide();
            $("#liCompulsoryType").show();
            loadCourseType();
        }
        initGrid();
        
        
    });

    function initGrid() {
        var schoolYearStart = $('#schoolYearStart').ysComboBox('getValue');
        var schoolTerm = $('#schoolTerm').ysComboBox('getValue');
        if (schoolYearStart == -1) {
            ys.msgError('请先选择学年！');
            return false;
        }
        if (schoolTerm == -1) {
            ys.msgError('请先选择学期！');
            return false;
        }

        var columns = [
            {
                field: 'SchoolYearStart', title: '学期', sortable: true, halign: 'center', align: 'center', width: 100,
                formatter: function (value, row, index) {
                    return (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2)
                        + (row.SchoolTerm == @SchoolTermEnum.LastSemester.ParseToInt()
                                ? "@SchoolTermEnum.LastSemester.GetDescription()"
                            : "@SchoolTermEnum.NextSemester.GetDescription()");
                }
            },
            { field: 'SchoolStageName', title: '学段', sortable: true, halign: 'center', align: 'center', width: 60 },
            { field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center', width: 60 },
            { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', width: 60 }
        ];
        var columnMergeNum = 5;//列合并数
        if (MenuType == 2) {
            columnMergeNum = 6;
            columns.push({
                field: 'CompulsoryType', title: '学科类型', sortable: true, halign: 'center', align: 'center', width: 60,
                formatter: function (value, row, index) {
                    switch (value) {
                        case 4:
                            return '选修科目班级';
                        case 3:
                            return '非选修科目班级';
                    }
                }
            });
        }

        columns.push(
            { field: 'NeedShowNum', title: '应开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            { field: 'NeedShowNumed', title: '实开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            {
                field: 'NeedShowNumRatio', title: '开出率', sortable: true, halign: 'center', align: 'center', width: 40,
                formatter: function (value, row, index) {
                    return value.toFixed(2) + '%';
                }
            },

            { field: 'NeedGroupNum', title: '应开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            { field: 'NeedGroupNumed', title: '实开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            {
                field: 'NeedGroupNumRatio', title: '开出率', sortable: true, halign: 'center', align: 'center', width: 40,
                formatter: function (value, row, index) {
                    return value.toFixed(2) + '%';
                }
            },

            { field: 'OptionalShowNum', title: '应开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            { field: 'OptionalShowNumed', title: '实开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            {
                field: 'OptionalShowNumRatio', title: '开出率', sortable: true, halign: 'center', align: 'center', width: 40,
                formatter: function (value, row, index) {
                    return value.toFixed(2) + '%';
                }
            },

            { field: 'OptionalGroupNum', title: '应开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            { field: 'OptionalGroupNumed', title: '实开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            {
                field: 'OptionalGroupNumRatio', title: '开出率', sortable: true, halign: 'center', align: 'center', width: 40,
                formatter: function (value, row, index) {
                    return value.toFixed(2) + '%';
                }
            },

            { field: 'TotalNum', title: '应开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            { field: 'TotalNumed', title: '实开数', sortable: true, halign: 'center', align: 'center', width: 40 },
            {
                field: 'TotalRatio', title: '开出率', sortable: true, halign: 'center', align: 'center', width: 40,
                formatter: function (value, row, index) {
                    return value.toFixed(2) + '%';
                }
            }
        );
            columns.unshift(
                {
                    field: '', title: @UnitType == @UnitTypeEnum.School.ParseToInt() ? '班级明细' : @UnitType == @UnitTypeEnum.County.ParseToInt() ? '学校明细' : '区县明细', halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                    var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(\'' + row.CountyId + '\',\'' + row.SchoolId + '\',\'' + row.SchoolStageId + '\',\'' + row.SchoolYearStart + '\',\'' + row.SchoolTerm + '\',\'' + row.CourseId + '\',\'' + row.GradeId + '\',\'' + row.SourceType + '\',\'' + row.CompulsoryType + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        return html;
                    }
                }
            );

        var queryUrl = '';
        if (@UnitType == @UnitTypeEnum.School.ParseToInt()) {
            queryUrl = '@Url.Content("~/EvaluateManage/ExperimentAttendStatic/GetPageListJson")';
        } else if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            queryUrl = '@Url.Content("~/EvaluateManage/ExperimentAttendStatic/GetCountyPageListJson")';
        } else if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            queryUrl = '@Url.Content("~/EvaluateManage/ExperimentAttendStatic/GetCityPageListJson")';
        }

        $('#gridTable').ysTable({
            url: queryUrl + '?IsGenderData=' + IsGenderData,
            sortName: 'SchoolYearStart ASC,SchoolTerm ASC,GradeId ASC,CourseId ASC',
            columns: [
                [
                    { title: '', align: 'center', colspan: columnMergeNum },
                    { title: '必做演示实验', align: 'center', colspan: 3 },
                    { title: '必做分组实验', align: 'center', colspan: 3 },
                    { title: '选做演示实验', align: 'center', colspan: 3 },
                    { title: '选做分组实验', align: 'center', colspan: 3 },
                    { title: '小计', align: 'center', colspan: 3 }
                ],
                columns
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                if (queryString) {
                    queryString.Menutype = MenuType;
                    if (MenuType == 1) {
                        queryString.SourceType = 2;//默认登记过，实验存在都统计在达标数据中（统一口径去除：不区分计划、目录）。
                    }
                }
                return queryString;
            }
        });
    }

    function searchGrid() {
        var txtSchoolYearStart = $('#schoolYearStart').ysComboBox('getValue'); //搜索的学年
        var txtSchoolTerm = $('#schoolTerm').ysComboBox('getValue'); //搜索的学期
        if ((txtSchoolYearStart > @SchoolYearStart || (txtSchoolYearStart == @SchoolYearStart && txtSchoolTerm > @SchoolTerm))
            && (txtSchoolYearStart != LastSchoolYearStart || txtSchoolTerm != LastSchoolTerm)) {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/ExperimentAttendStatic/GenderExperimentAttendStaticData")' + "?schoolYearStart=" + txtSchoolYearStart + '&schoolTerm=' + txtSchoolTerm,
                data: null,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#gridTable').ysTable('search');
                        resetToolbarStatus();
                    }
                }
            });
        }
        else {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        LastSchoolYearStart = txtSchoolYearStart;
        LastSchoolTerm = txtSchoolTerm;
    }
    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                var schoolStageId = $("#schoolStageId").ysComboBox("getValue");
                loadGrade(schoolStageId);
            }
        });
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data && obj.Data.length > 0) {
                        var newData = [];
                        obj.Data.forEach(function callback(currentValue, index, array) {
                            if (!(MenuType == 1 && currentValue.DictionaryId == @SchoolStageEnum.GaoZhong.ParseToInt())) {
                                newData.push(array[index]);
                            }
                        });
                        obj.Data = newData;
                    }
                    bindSchoolStage(obj.Data);
                } else {
                    bindSchoolStage({});
                }
            }
        });
    }
    function bindSchoolStage(data) {
        $('#schoolStageId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段', 
        });
        var defaultStageId = $("#schoolStageId").ysComboBox("getValue");
        if (defaultStageId > 0){
            loadGrade(defaultStageId);
        }else{
            loadGrade(0);
        }
    }

    function loadGrade(schoolStage) {
        var ids = '1003001,1003002,1003003,1003004,1003005,1003006,1003007,1003008,1003009,1003010,1003011,1003012';
        if (MenuType == 1) {
            ids = '1003001,1003002,1003003,1003004,1003005,1003006,1003007,1003008,1003009';
        } else if (MenuType == 2) {
            ids = '1003010,1003011,1003012';
        }
        if (schoolStage == @SchoolStageEnum.XiaoXue.ParseToInt()) { //小学
            ids = '1003001,1003002,1003003,1003004,1003005,1003006';
        } else if (schoolStage == @SchoolStageEnum.ChuZhong.ParseToInt()) { //初中
            ids = '1003007,1003008,1003009';
        } else if (schoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) { //高中
            ids = '1003010,1003011,1003012';
        }

        if (@UnitType == @UnitTypeEnum.School.ParseToInt() && schoolStage == 0) {
            $('#gradeId').ysComboBox({
                url: '@Url.Content("~/EvaluateManage/ExperimentAttendStatic/GetGradeBySchoolProp")',
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '年级'
            });
        }else if (@UnitType == @UnitTypeEnum.School.ParseToInt() && schoolStage > 0) {
            $('#gradeId').ysComboBox({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Ids=' + ids,
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '年级'
            });
        }else {
            $('#gradeId').ysComboBox({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Ids=' + ids,
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '年级'
            });
        }
    }

    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId?nature=1")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }
   
    function loadCourseType() {
        var data = [{ Key: 1, Value: '选修科目班级' }, { Key: 2, Value: '非选修科目班级' }];
        $("#CompulsoryType").ysComboBox({ data: data, defaultName: '学科类型' });
        $("#CompulsoryType").ysComboBox('setValue', -1);
    }

    function loadSchool() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000",
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });
                }
            }
        });
    }
    function resetGrid() {
        //清空条件
        $('#schoolYearStart').ysComboBox('setValue', @SchoolYearStart);
        $("#schoolTerm").ysComboBox('setValue', @SchoolTerm);
        $('#courseId').ysComboBox('setValue', -1);
        if (MenuType == 2) {
            $('#CompulsoryType').ysComboBox('setValue', -1);
        }
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            $('#schoolId').ysComboBox('setValue', -1);
        }
        $('#schoolStageId').ysComboBox('setValue', -1);
        $('#gradeId').ysComboBox('setValue', -1);
        loadGrade(0);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function look(countyId, schoolId, schoolStageId, schoolYearStart, schoolTerm, courseId, gradeId, sourceType, compulsorytype) {
        if (@UnitType == @UnitTypeEnum.School.ParseToInt()) {
            var url = '/EvaluateManage/ExperimentAttendStatic/ExperimentAttendStaticDetail?' + 'CountyId=' + countyId + '&SchoolId=' + schoolId + '&SchoolStageId=' + schoolStageId + '&SchoolYearStart=' + schoolYearStart + '&SchoolTerm=' + schoolTerm + '&CourseId=' + courseId + "&GradeId=" + gradeId + "&t=" + MenuType + "&CompulsoryType=" + compulsorytype;
            createMenuItem(url, "班级明细");
        } else if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            var url2 = '/EvaluateManage/ExperimentAttendStatic/ExperimentAttendStaticSchoolDetail?' + 'CountyId=' + countyId + '&SchoolStageId=' + schoolStageId + '&SchoolYearStart=' + schoolYearStart + '&SchoolTerm=' + schoolTerm + '&CourseId=' + courseId + "&GradeId=" + gradeId + "&t=" + MenuType + "&CompulsoryType=" + compulsorytype;
            createMenuItem(url2, "学校明细");
        } else if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            var url3 = '/EvaluateManage/ExperimentAttendStatic/ExperimentAttendStaticCountyDetail?' + 'SchoolStageId=' + schoolStageId + '&SchoolYearStart=' + schoolYearStart + '&SchoolTerm=' + schoolTerm + '&CourseId=' + courseId + "&GradeId=" + gradeId + "&t=" + MenuType + "&CompulsoryType=" + compulsorytype;
            createMenuItem(url3, "区县明细");
        }
    }

    function exportForm() { //导出
        var url = '@Url.Content("~/EvaluateManage/ExperimentAttendStatic/Export")';
        var postData = $("#searchDiv").getWebControls();
        ys.exportExcel(url, postData);
    }
</script>
