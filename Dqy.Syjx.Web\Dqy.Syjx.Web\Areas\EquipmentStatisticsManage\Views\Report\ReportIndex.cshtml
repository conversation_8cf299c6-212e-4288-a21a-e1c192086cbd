﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsrender.js"), true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsviews.js"), true)

<style type="text/css">
    .list-left {
        display: inline-block;
    }

    .list-right {
        display: inline-block;
        vertical-align: top;
        margin-top: 3px;
        margin-left: 5px;
        width: 390px;
        float: left;
    }

    #Catalog_box {
        position: fixed;
        bottom: 100px;
    }

        #Catalog_box .cate-item1 {
            margin-bottom: 20px;
        }

            #Catalog_box .cate-item1 span {
                color: #999;
                font-weight: bold;
                font-family: Arial;
                font-size: 14px;
                padding-right: 5px;
            }

        #Catalog_box .cate-item2 {
            padding-left: 20px;
        }

            #Catalog_box .cate-item2 span {
                font-weight: 500;
            }

        #Catalog_box dd.active, #Catalog_box dd.active a {
            color: #136ec2 !important;
        }



    .card-heading {
        color: #333;
        background-color: #f5f5f5;
        border-color: #ddd;
        padding: 10px 15px;
        border-bottom: 1px solid transparent;
        border-top-left-radius: 3px;
        border-top-right-radius: 3px;
    }

    .col-sm-4{
        display:flex;
    }
    .form-control-static>span{
        line-height:32px;
    }
</style>
<div class="container-div">
    <div class="d-flex justify-content-between">
        <div class="col-sm-11 page_left" style="height:auto;padding-right:5px;height:30px;" id="divContent">
            <div class="form-group row" style="padding:5px;">
                <label id="labReportDate"></label> <i class="fa fa-question-circle" id="helpBtn"></i> <!--帮助文档需要内容,id值必须为“helpBtn”-->
            </div>
            <form id="form" class="form-horizontal m">
                <div class="card " style="margin-left:-15px;">
                    <div class="card-heading">
                        <h4 style="display:inline-block;width:150px;"><i class="fa fa-user"></i><span style="padding-left:15px;">装备投入经费</span></h4>
                        <a class="btn btn-primary" onclick="saveForm('1')"><i class="fa fa-save btn-xs"></i>保存</a>
                        <span style="padding:5px;color:#999;"></span>
                    </div>
                    <div class="card-body">

                        <div class="form-group row">
                            <label class="col-sm-2 control-label">装备累计投入经费（元）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="TotalAmount" col="TotalAmount" type="text" class="form-control input_change" opttype="1" />
                            </div>
                            <label class="col-sm-2 control-label ">班均额（元）：</label>
                            <div class="col-sm-2 form-control-static">
                                <span id="spanTotalClassAverage">0.00%</span>
                            </div>
                            <label class="col-sm-2 control-label ">生均额（元）：</label>
                            <div class="col-sm-2 form-control-static">
                                <span id="spanTotalStudentAverage" class=" ">0.00%</span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">装备当年投入经费（元）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="CurrentYearAmount" col="CurrentYearAmount" type="text" class=" form-control input_change" opttype="1" />
                            </div>
                            <label class="col-sm-2 control-label ">班均额（元）：</label>
                            <div class="col-sm-2 form-control-static">
                                <span id="spanClassAverage">0.00%</span>
                            </div>
                            <label class="col-sm-2 control-label ">生均额（元）：</label>
                            <div class="col-sm-2 form-control-static">
                                <span id="spanStudentAverage" class="">0.00%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card " style="margin-left:-15px;">
                    <div class="card-heading">
                        <h4 style="display:inline-block;"><i class="fa fa-user"></i><span style="padding-left:15px;">信息技术</span></h4>
                    </div>
                    <div class="card-body">
                        <div class="form-group row">
                            <label class="col-sm-12 control-label " style="text-align: left !important;">
                                <span style="padding-left:15px;"><h5 style="display:inline-block;width:135px;">1.多媒体设备</h5></span>
                                <a class="btn btn-primary" onclick="saveForm('2')"><i class="fa fa-save btn-xs"></i>保存</a>
                                <span style="padding:5px;color:#999;"></span>
                            </label>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">普通教室多媒体设备（套）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="MediaFunroomNum" col="MediaFunroomNum" type="text" class="form-control" />

                            </div>
                            <label class="col-sm-2 control-label ">其中交互式多媒体（套）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="MediaInteractiveNum" col="MediaInteractiveNum" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">专用教室多媒体设备（套）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                @*<input id="MediaDeviceNum" col="MediaDeviceNum" type="text" class="form-control" /> *@
                                <input id="MedialaboratoryNum" col="MedialaboratoryNum" type="text" class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">其中交互式多媒体（套）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="MediaInteractiveLabNum" col="MediaInteractiveLabNum" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">其它场所多媒体设备（套）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="MediaOtherNum" col="MediaOtherNum" type="text" class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">其中交互式多媒体（套）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="MediaInteractiveOtherNum" col="MediaInteractiveOtherNum" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-12 control-label " style="text-align: left !important;">
                                <span style="padding-left:15px;"><h5 style="display:inline-block;width:135px;">2.计算机设备</h5></span>
                                <a class="btn btn-primary" onclick="saveForm('3')"><i class="fa fa-save btn-xs"></i>保存</a>
                                <span style="padding:5px;color:#999;"></span>
                            </label>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-4">
                                <label class="col-sm-6 control-label ">学生计算机（台）：<font class="red"> *</font></label>
                                <div class="col-sm-6">
                                    <input id="ComputerStudentNum" col="ComputerStudentNum" type="text" class="form-control input_change" opttype="3" />
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <label class="col-sm-6 control-label ">其中平板计算机（台）：<font class="red"> *</font></label>
                                <div class="col-sm-6">
                                    <input id="ComputerPadNum" col="ComputerPadNum" type="text" class="form-control" />
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <label class="col-sm-6 control-label ">生机比值：</label>
                                <div class="col-sm-6 form-control-static">
                                    <span id="spanStudentComputerAverage" class="">0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-4">
                                <label class="col-sm-6 control-label ">教师计算机（台）：<font class="red"> *</font></label>
                                <div class="col-sm-6">
                                    <input id="ComputerTeacherNum" col="ComputerTeacherNum" type="text" class="form-control input_change" opttype="3" />
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <label class="col-sm-6 control-label ">其中便携式计算机（台）：<font class="red"> *</font></label>
                                <div class="col-sm-6">
                                    <input id="ComputerPortableNum" col="ComputerPortableNum" type="text" class="form-control" />
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <label class="col-sm-6 control-label ">师机比值：</label>
                                <div class="col-sm-6 form-control-static">
                                    <span id="spanTeacherComputerAverage" class="">0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-12 control-label " style="text-align: left !important;">
                                <span style="padding-left:15px;"><h5 style="display:inline-block;width:135px;">3.信息化系统</h5></span>
                                <a class="btn btn-primary" onclick="saveForm('7')"><i class="fa fa-save btn-xs"></i>保存</a>
                                <span style="padding:5px;color:#999;"></span>
                            </label>
                        </div>
                        <div>
                            <div class="form-group row">
                                <div class="col-sm-4" id="divSysNetworkWired">
                                </div>
                                <div class="col-sm-4" id="divSysNetworkWireless">
                                </div>
                                <div class="col-sm-4">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-4" id="divSysCampusBroadcasting">
                                </div>
                                <div class="col-sm-4" id="divSysSecurityMonitor">
                                </div>
                                <div class="col-sm-4">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-4" id="divSysElectronicClassCard">
                                </div>
                                <div class="col-sm-4" id="divSysOutdoorsLEDBig">
                                </div>
                                <div class="col-sm-4">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-4" id="divSysCampusTV">
                                </div>
                                <div class="col-sm-4" id="divSysNormalizedBroadcast">
                                </div>
                                <div class="col-sm-4">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-4" id="divSysCampusVisitor">
                                </div>
                                <div class="col-sm-4" id="divSysOneCard">
                                </div>
                                <div class="col-sm-4">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-sm-4" id="divSysLoT">
                                </div>
                                <div class="col-sm-4" id="divSysStandardizedRoom">
                                </div>
                                <div class="col-sm-4">
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-12 control-label " style="text-align: left !important;">
                                <span style="padding-left:15px;"><h5 style="display:inline-block;width:135px;">4.网络管理员</h5></span>
                                <a class="btn btn-primary" onclick="saveForm('4')"><i class="fa fa-save btn-xs"></i>保存</a>
                            </label>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">网络管理员（人）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="NetworkAdminNum" col="NetworkAdminNum" type="text" class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">其中专职人员（人）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="NetworkFulltimeNum" col="NetworkFulltimeNum" type="text" class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label "></label>
                            <div class="col-sm-2">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card " style="margin-left:-15px;">
                    <div class="card-heading">
                        <h4 style="display:inline-block;width:150px;"><i class="fa fa-user"></i><span style="padding-left:15px;">图书</span></h4>
                        <a class="btn btn-primary" onclick="saveForm('5')"><i class="fa fa-save btn-xs"></i>保存</a>
                        <span style="padding:5px;color:#999;"></span>
                    </div>
                    <div class="card-body">
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">藏书量(册）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="BookTotalNum" col="BookTotalNum" type="text" class="form-control input_change" opttype="5" />
                            </div>
                            <label class="col-sm-2 control-label ">生均图书(册）：</label>
                            <div class="col-sm-2 form-control-static">
                                <span id="spanStudentBookAverage" class=" ">0.00%</span>
                            </div>
                            <label class="col-sm-2 control-label ">图书管理员（人）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="BookAdminNum" col="BookAdminNum" type="text" class=" form-control" />
                            </div> 
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">当年新增图书（册）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="BookNewYearNum" col="BookNewYearNum" type="text" class="form-control input_change" opttype="5" />
                            </div>
                            <label class="col-sm-2 control-label ">当年生均新增图书（册）：</label>
                            <div class="col-sm-2 form-control-static">
                                <span id="spanStudentBookNewAverage" class=" ">--</span>
                            </div>
                            <label class="col-sm-2 control-label ">其中专职人员（人）：<font class="red"> *</font></label>
                            <div class="col-sm-2 form-control-static">
                                <input id="BookFulltimeNum" col="BookFulltimeNum" type="text" class=" form-control" />
                            </div>
                        
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">图书馆总面积（平米）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="BookLibraryArea" col="BookLibraryArea" type="text" class=" form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">阅览室座位（个）：<font class="red"> *</font></label>
                            <div class="col-sm-2 form-control-static">
                                <input id="BookRoomSeatNum" col="BookRoomSeatNum" type="text" class=" form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">供读者使用的终端（台）：<font class="red"> *</font></label>
                            <div class="col-sm-2 form-control-static">
                                <input id="BookReadersTerminal" col="BookReadersTerminal" type="text" class=" form-control" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">移动图书柜（个）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="BookCabinetNum" col="BookCabinetNum" type="text" class=" form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">朗读亭（个）：<font class="red"> *</font></label>
                            <div class="col-sm-2 form-control-static">
                                <input id="BookPavilionNum" col="BookPavilionNum" type="text" class=" form-control" />
                            </div>
                            <label class="col-sm-2 control-label "></label>
                            <div class="col-sm-2 form-control-static">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card " style="margin-left:-15px;">
                    <div class="card-heading">
                        <h4 style="display:inline-block;width:150px;"><i class="fa fa-user"></i><span style="padding-left:15px;">体育场所</span></h4>
                        <a class="btn btn-primary" onclick="saveForm('6')"><i class="fa fa-save btn-xs"></i>保存</a>
                        <span style="padding:5px;color:#999;"></span>
                    </div>
                    <div class="card-body">
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">跑道长度（米）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="SportRunwayLength" col="SportRunwayLength" type="text" class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">室内运动场（个）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="SportRoomNum" col="SportRoomNum" type="text" class="form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">乒乓球桌（张）：<font class="red"> *</font></label>
                            <div class="col-sm-2 form-control-static">
                                <input id="SportPingPongNum" col="SportPingPongNum" type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-2 control-label ">篮球场（个）：<font class="red"> *</font></label>
                            <div class="col-sm-2">
                                <input id="SportBasketballNum" col="SportBasketballNum" type="text" class=" form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">排球场（个）：<font class="red"> *</font></label>
                            <div class="col-sm-2 form-control-static">
                                <input id="SportVolleyballNum" col="SportVolleyballNum" type="text" class=" form-control" />
                            </div>
                            <label class="col-sm-2 control-label ">足球场（个）：<font class="red"> *</font></label>
                            <div class="col-sm-2 form-control-static">
                                <input id="SportFootballNum" col="SportFootballNum" type="text" class=" form-control" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card " style="margin-left:-15px;">
                    <div class="card-heading">
                        <h4 style="display:inline-block;width:150px;"><i class="fa fa-user"></i><span style="padding-left:15px;">科创设备</span></h4>
                        <a class="btn btn-primary" onclick="saveEquipmentForm('8')"><i class="fa fa-save btn-xs"></i>保存</a>
                        <span style="padding:5px;color:#999;"></span>
                    </div>
                    <div class="card-body">
                        <div class="form-group row" id="divEquipment"></div>
                    </div>
                </div>
                <div class="card " style="margin-left:-15px;" id="divKindSchool">
                    <div class="card-heading">
                        <h4 style="display:inline-block;width:150px;"><i class="fa fa-user"></i><span style="padding-left:15px;">幼儿园专用室</span></h4>
                        <a class="btn btn-primary" onclick="saveSpecialRoomForm('9')"><i class="fa fa-save btn-xs"></i>保存</a>
                        <span style="padding:5px;color:#999;"></span>
                    </div>
                    <div class="card-body">
                        <div class="form-group row" id="divSpecialRoom"></div>
                    </div>
                </div>
            </form>
        </div>
        <div class="col-sm-1 page_right" style="padding-left:0px;">
            <div id="Catalog_box">
            </div>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<div id="sapnSelect2" class="select2-container--bootstrap select2-container--open select2-dropdown select2-dropdown--below" dir="ltr" style="width: 500px;position: absolute;">
    <span class="select2-search select2-search--dropdown">
        <i id="searchLeft" class="fa fa-search"></i>
        <input id="searchInputSelect2" class="select2-search__field" type="text" autocomplete="off">
    </span>
    <div class="select2-results">
        <ul class="select2-results__options" role="listbox" id="searchUlSelect2" aria-expanded="true" aria-hidden="false">
        </ul>
    </div>
</div>
<script id="scriptEquipment" type="text/x-jsrender">
     <table id="gridEquipmentTable" data-mobile-responsive="true" class="table table-hover table-striped" style="margin-top: 0px;">
         <thead>
             <tr>
                 <th style="text-align: center; vertical-align: middle; width: 40px; ">
                     <div class="th-inner sortable both">序号</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 180px; ">
                     <div class="th-inner ">操作</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 160px; ">
                     <div class="th-inner sortable both">设备名称</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 100px; ">
                     <div class="th-inner sortable both">品牌</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 200px; ">
                     <div class="th-inner sortable both">型号</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 80px; ">
                     <div class="th-inner sortable both">数量</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 60px; ">
                     <div class="th-inner sortable both">单位</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 200px; ">
                     <div class="th-inner sortable both">备注</div>
                     <div class="fht-cell"></div>
                 </th>
             </tr>
         </thead>
         <tbody id="equipmentBody">
             {^{for data}}
                 <tr data-index="0" data-uniqueid="1">
                     <td style="text-align: center; vertical-align: middle;width:40px; ">{{:#index + 1}}</td>
                     <td style="text-align: center; vertical-align: middle;width:180px;">
                         <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;padding:1px 5px;" data-link="{on ~root.addRow #index}"><i class="fa fa-plus"></i>新增</a>&nbsp;&nbsp;
                         <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;padding:1px 5px;" data-link="{on ~root.deleteRow #index}"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;
                         <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;padding:1px 5px;" data-link="{on ~root.copyRow #index}"><i class="fa fa-clone"></i>复制</a>&nbsp;&nbsp;
                     </td>
                     <td style="text-align: left; vertical-align: middle;">
                          <input index="{{:#index}}"  data-link="Name" value="{{>Name}}" title="{{>Name}}" onclick="clickSelectModel(this,{{:#index}},1)" autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <input index="{{:#index}}" data-link="EquipmentNameConfigId" value="{{>EquipmentNameConfigId}}" type="hidden" "/>

                          <div class="hide"></div>
                     </td>
                     <td style="text-align: center; vertical-align: middle; ">
                            <input index="{{:#index}}"  data-link="Brand" value="{{>Brand}}" title="{{>Brand}}"  autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: left; vertical-align: middle; ">
                            <input index="{{:#index}}"  data-link="Modelz" value="{{>Modelz}}" title="{{>Modelz}}" autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: right; vertical-align: middle; ">
                            <input index="{{:#index}}" data-link="Num" value="{{>Num}}" title="{{>Num}}" autocomplete="off"  class="form-control form-control-lg jsrender-x-num" style="cursor: pointer;text-align: right;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: center; vertical-align: middle;">
                            <input index="{{:#index}}"  data-link="UnitName" value="{{>UnitName}}" title="{{>UnitName}}" autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: left; vertical-align: middle; ">
                            <input index="{{:#index}}"  data-link="Remark" value="{{>Remark}}" title="{{>Remark}}" autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                 </tr>
            {{/for}}
        </tbody>
    </table>
</script>
<script id="scriptSpecialRoom" type="text/x-jsrender">
     <table id="gridSpecialRoomTable" data-mobile-responsive="true" class="table table-hover table-striped" style="margin-top: 0px;">
         <thead>
             <tr>
                 <th style="text-align: center; vertical-align: middle; width: 40px; ">
                     <div class="th-inner sortable both">序号</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 180px; ">
                     <div class="th-inner ">操作</div>
                     <div class="fht-cell"></div>
                 </th>
                  <th style="text-align: center; vertical-align: middle; width: 100px; ">
                     <div class="th-inner sortable both">分类名称</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 160px; ">
                     <div class="th-inner sortable both">专用室名称</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 100px; ">
                         <div class="th-inner sortable both">使用面积（平米）</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 120px; ">
                     <div class="th-inner sortable both">起初建设时间</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 120px; ">
                     <div class="th-inner sortable both">最新改造时间</div>
                     <div class="fht-cell"></div>
                 </th>
                 <th style="text-align: center; vertical-align: middle; width: 200px; ">
                     <div class="th-inner sortable both">备注</div>
                     <div class="fht-cell"></div>
                 </th>
             </tr>
         </thead>
         <tbody id="specialRoomBody">
             {^{for data}}
                 <tr data-index="0" data-uniqueid="1">
                     <td style="text-align: center; vertical-align: middle;width:40px; ">{{:#index + 1}}</td>
                     <td style="text-align: center; vertical-align: middle;width:180px;">
                         <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.addRow #index}"><i class="fa fa-plus"></i>新增</a>&nbsp;&nbsp;
                         <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.deleteRow #index}"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;
                         <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.copyRow #index}"><i class="fa fa-clone"></i>复制</a>&nbsp;&nbsp;
                     </td>
                     <td style="text-align: left; vertical-align: middle;">
                          <input index="{{:#index}}" data-link="CategoryName"value="{{>CategoryName}}" title="{{>CategoryName}}" onclick="clickSelectModel(this,{{:#index}},0)" autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <input index="{{:#index}}" data-link="CategoryId" value="{{>CategoryId}}" type="hidden" id="CategoryId_{{:#index}}"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: left; vertical-align: middle;">
                          <input index="{{:#index}}"  data-link="Name" value="{{>Name}}" title="{{>Name}}"  autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: right; vertical-align: middle; ">
                          <input index="{{:#index}}" data-link="UseArea" value="{{>UseArea}}" title="{{>UseArea}}" autocomplete="off"  class="form-control form-control-lg jsrender-x-num" style="cursor: pointer;text-align: right;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: center; vertical-align: middle;">
                          <input index="{{:#index}}"  data-link="BuildTime" value="{{>BuildTime}}" title="{{>BuildTime}}" autocomplete="off" class="form-control form-control-lg selectbuilddate" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                      <td style="text-align: left; vertical-align: middle; ">
                          <input index="{{:#index}}"  data-link="ReformTime" value="{{>ReformTime}}" title="{{>ReformTime}}" autocomplete="off" class="form-control form-control-lg selectreformdate" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                     <td style="text-align: left; vertical-align: middle; ">
                          <input index="{{:#index}}"  data-link="Remark" value="{{>Remark}}" title="{{>Remark}}" autocomplete="off" class="form-control form-control-lg" style="cursor: pointer;"  type="text"/>
                          <div class="hide"></div>
                     </td>
                 </tr>
            {{/for}}
        </tbody>
    </table>
</script>

<script type="text/javascript">
    var IsKindSchool = 1;
    var Com_CategoryArr = [];
    var Com_EquipmentDataArr = [];
    var EquipmentTableObj;
    var SpecialRoomTableObj;
    $(function () {
        loadReportDateMsg();
        setCategoryArrd();
        loadCategorySelectModel();
        loadEquipmentTable();
        loadSpecialRoomTable();
        getForm();

        loadFormDataFormat();
        loadEquipmentData();
    });

    function loadReportDateMsg() {
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/Report/GetReportDate")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $("#labReportDate").html($.Format("填报开启时间段 {0} ，请您尽快填报！", obj.ExtendData));
                }
                else {
                    $("#labReportDate").html("当前不在填报时间内，您修改的数据无法保存");
                }
            }
        });
    }

    function setCategoryArrd() {
        Com_CategoryArr.push({ id: "1", name: "音乐类" });
        Com_CategoryArr.push({ id: "2", name: "美工类" });
        Com_CategoryArr.push({ id: "3", name: "科学类" });
        Com_CategoryArr.push({ id: "4", name: "建构类" });
        Com_CategoryArr.push({ id: "5", name: "体育类" });
        Com_CategoryArr.push({ id: "6", name: "其他类" });
    }

    function loadEquipmentData(){
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/EquipmentNameConfig/GetListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data != undefined && obj.Data.length > 0){
                        Com_EquipmentDataArr = obj.Data;
                    }                              
                }
            }
        });
    }

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/Report/GetInfoFormJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#form').setWebControls(obj.Data);


                    $("#divSysNetworkWired").html(getCheckboxHtml("SysNetworkWired", "有线网络系统", obj.Data.SysNetworkWired));
                    $("#divSysNetworkWireless").html(getCheckboxHtml("SysNetworkWireless", "无线网络系统", obj.Data.SysNetworkWireless));
                    $("#divSysCampusBroadcasting").html(getCheckboxHtml("SysCampusBroadcasting", "校园广播系统", obj.Data.SysCampusBroadcasting));
                    $("#divSysSecurityMonitor").html(getCheckboxHtml("SysSecurityMonitor", "安防监控系统", obj.Data.SysSecurityMonitor));
                    // $("#divSysMediaPublish").html(getCheckboxHtml("SysMediaPublish", "媒体发布系统", obj.Data.SysMediaPublish));
                    $("#divSysElectronicClassCard").html(getCheckboxHtml("SysElectronicClassCard", "电子班牌系统", obj.Data.SysElectronicClassCard));
                    $("#divSysOutdoorsLEDBig").html(getCheckboxHtml("SysOutdoorsLEDBig", "户外LED大屏系统", obj.Data.SysOutdoorsLEDBig));

                    $("#divSysCampusTV").html(getCheckboxHtml("SysCampusTV", "校园电视台", obj.Data.SysCampusTV));
                    $("#divSysNormalizedBroadcast").html(getCheckboxHtml("SysNormalizedBroadcast", "录播系统", obj.Data.SysNormalizedBroadcast));

                    $("#divSysCampusVisitor").html(getCheckboxHtml("SysCampusVisitor", "校园访客系统", obj.Data.SysCampusVisitor));
                    $("#divSysOneCard").html(getCheckboxHtml("SysOneCard", "一卡通系统", obj.Data.SysOneCard));

                    $("#divSysLoT").html(getCheckboxHtml("SysLoT", "物联网系统", obj.Data.SysLoT));
                    $("#divSysStandardizedRoom").html(getCheckboxHtml("SysStandardizedRoom", "标准化考场", obj.Data.SysStandardizedRoom));
                   
                    $("#spanTotalClassAverage").text(getFloatFormat(obj.Data.ClassAmountRate));
                    $("#spanTotalStudentAverage").text(getFloatFormat(obj.Data.StudentAmountRate));
                    $("#spanClassAverage").text(getFloatFormat(obj.Data.ClassAmountCurrentRate));
                    $("#spanStudentAverage").text(getFloatFormat(obj.Data.StudentAmountCurrentRate));

                    if (obj.Data.StudentComputRate != undefined ) {
                        $("#spanStudentComputerAverage").text(obj.Data.StudentComputRate);
                    }
                    if (obj.Data.TeacherComputRate != undefined ) {
                        $("#spanTeacherComputerAverage").text(obj.Data.TeacherComputRate);
                    }


                    $("#spanStudentBookAverage").text(getFloatFormat(obj.Data.StudentBookRate));
                    $("#spanStudentBookNewAverage").text(getFloatFormat(obj.Data.StudentBookNewRate));

                    if (obj.Data.IsKindSchool != 1) {
                        $("#divKindSchool").hide();
                        IsKindSchool = 0;
                    }
                    setFormDataFormat();
                    //列表
                    $.observable(EquipmentTableObj).setProperty("data", obj.Data.EquipmentList);
                    //
                    if (obj.Data.SpecialRoomList != undefined && obj.Data.SpecialRoomList.length > 0) {
                        obj.Data.SpecialRoomList.forEach(function (item, i, data) {
                            if (item.BuildTime != undefined && item.BuildTime.length > 0) {
                                item.BuildTime = ys.formatDate(item.BuildTime, "yyyy-MM-dd");
                            }
                            if (item.ReformTime != undefined && item.ReformTime.length > 0) {
                                item.ReformTime = ys.formatDate(item.ReformTime, "yyyy-MM-dd");
                            }
                        });
                        $.observable(SpecialRoomTableObj).setProperty("data", obj.Data.SpecialRoomList);
                    }

                    setJsrenderXFormat();
                    loadDormatJsrenderX();

                    loadBindDateEvent();

                    CateNav('#divContent', '#Catalog_box');
                }
                loadBindInputChange()
            }
        });
    }

    function loadBindDateEvent() {
        $(".selectbuilddate").each(function (index) {
            laydate.render({
                elem: this, format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed', done: function (value, date) {
                    var index = $(this.elem).attr('index');
                    SpecialRoomTableObj.data[index].BuildTime = value;
                }
            });
        });
        $(".selectreformdate").each(function (index) {
            laydate.render({
                elem: this, format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed', done: function (value, date) {
                    var index = $(this.elem).attr('index');
                    SpecialRoomTableObj.data[index].ReformTime = value;
                }
            });
        });
    }

    function loadEquipmentTable() {
        // var html = $("#scriptEquipment").render();
        // $("#divEquipment").append(html);
        EquipmentTableObj = {
            // data: [],
            addRow: function (index) {
                if (EquipmentTableObj.data.length >= 200) {
                    layer.alert("每次最多只能添加200条数据，为了方便您查阅您可以分多次提交计划！", {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                    return;
                }
                $.observable(this.data).insert(index + 1, { Id: 0, EquipmentNameConfigId: 0, Name: '', Brand: '', Num: 0, Modelz: '', UnitName: '', Remark: '', Sort: 0 });
                loadDormatJsrenderX();
            },
            deleteRow: function (index) {
                $.observable(this.data).remove(index);
                if (this.data.length == 0) {
                    $.observable(this.data).insert(index + 1, { Id: 0, EquipmentNameConfigId: 0, Name: '', Brand: '', Num: 0, Modelz: '', UnitName: '', Remark: '', Sort: 0 });
                }
            },
            copyRow: function (index) {
                if (EquipmentTableObj.data.length >= 200) {
                    layer.alert("每次最多只能添加200条数据，为了方便您查阅您可以分多次提交计划！", {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                    return;
                }
                var obj = this.data[index];
                //obj.Id = 0;
                //$.observable(this.data).insert(index + 1, obj);
                $.observable(this.data).insert(index + 1, { Id: 0, EquipmentNameConfigId: obj.EquipmentNameConfigId, Name: obj.Name, Brand: obj.Brand, Num: obj.Num, Modelz: obj.Modelz, UnitName: obj.UnitName, Remark: obj.Remark, Sort: 0 });
                loadDormatJsrenderX();
            }
        };

        $.templates("#scriptEquipment").link("#divEquipment", EquipmentTableObj);
    }

    //#region

    function loadSpecialRoomTable() {
        SpecialRoomTableObj = {
            // data: [],
            addRow: function (index) {
                if (SpecialRoomTableObj.data.length >= 200) {
                    layer.alert("每次最多只能添加200条数据，为了方便您查阅您可以分多次提交计划！", {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                    return;
                }
                $.observable(this.data).insert(index + 1, { Id: 0, CategoryName: '', CategoryId: '', Name: '', UseArea: 0, BuildTime: '', ReformTime: '', Remark: '', Sort: 0 });
                loadBindDateEvent();
                loadDormatJsrenderX();
            },
            deleteRow: function (index) {
                $.observable(this.data).remove(index);
                if (this.data.length == 0) {
                    $.observable(this.data).insert(index + 1, { Id: 0, CategoryName: '', CategoryId: '', Name: '', UseArea: 0, BuildTime: '', ReformTime: '', Remark: '', Sort: 0 });
                }
                loadBindDateEvent();
            },
            copyRow: function (index) {
                if (SpecialRoomTableObj.data.length >= 200) {
                    layer.alert("每次最多只能添加200条数据，为了方便您查阅您可以分多次提交计划！", {
                        icon: 2,
                        skin: 'layer-ext-moon'
                    });
                    return;
                }
                var obj = this.data[index];
                $.observable(this.data).insert(index + 1, { Id: 0, CategoryName: obj.CategoryName, Name: obj.Name, UseArea: obj.UseArea, BuildTime: obj.BuildTime, ReformTime: obj.ReformTime, Remark: obj.Remark, Sort: 0 });
                loadBindDateEvent();
                loadDormatJsrenderX();
            }
        }

        $.templates("#scriptSpecialRoom").link("#divSpecialRoom", SpecialRoomTableObj);
    }

    /**
    *
    * 分类点击事件
    */
    function loadCategorySelectModel() {
        //搜索条件绑定事件
        var inputSearch = document.getElementById("searchInputSelect2");
        inputSearch.addEventListener('input', function (event) {
            if ($("#searchInputSelect2").val()) {
                $("#searchLeft").hide()
            } else {
                $("#searchLeft").show()
            }
            if (COM_Current_SelectObjType == 1) {
                loadEquipmentNameData();
            } else {
                loadCategoryData();
            }
       
        });
        inputSearch.addEventListener('change', function (event) {
            if (COM_Current_SelectObjType == 1) {
                loadEquipmentNameData();
            } else {
                loadCategoryData();
            }
        });
        $("#searchInputSelect2").blur(function (e) {
            // 点击弹出框内部不隐藏
            $("#sapnSelect2 > .select2-dropdown,.select-userteach").click(function (e) {
                e.stopPropagation()
            });

            // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
            $(document).one("click", function (e) {
                $('#sapnSelect2').hide();
                $(".iconDown").removeClass("hide").addClass("show")
                $(".iconUp").removeClass("show").addClass("hide")
            });
        });
        if (COM_Current_SelectObjType == 1) {
            loadEquipmentNameData();
        } else {
            loadCategoryData();
        }
    }

    var COM_Current_SelectObj;
    var COM_Current_SelectObjType=1;//1:科创设备  其他：幼儿园设备分类
    function clickSelectModel(obj, index,objtype) {
        $("#searchLeft").show()
        COM_Current_SelectObj = $(obj);
        COM_Current_SelectObjType = objtype;
        $(".iconDown").removeClass("hide").addClass("show")
        $(".iconUp").removeClass("show").addClass("hide")
        $(obj).next().removeClass("show").addClass("hide")
        $(obj).next().next().removeClass("hide").addClass("show")
        $("#searchInputSelect2").val('')
        var top = $(obj).offset().top;
        var left = $(obj).offset().left;
        var tdwidth = $(obj).css("width");
        $("#sapnSelect2").css("width", tdwidth);
        var tbheight = $(".container-div").height();//元素高度
        var minDiv = 240;
        var thisbottom = tbheight - top;
        var offsetBottom = tbheight - top;
        if (COM_Current_SelectObjType==1){
            loadEquipmentNameData();
        }else{
            loadCategoryData();
        }
  
        $("#sapnSelect2").show();
        if (offsetBottom > (minDiv + 30)) {
            top = top + 30;//加上文本框高度
            $("#sapnSelect2").css({ top: top + 'px', bottom: 'auto', left: left + 'px', position: 'absolute' });
        } else {
            offsetBottom = (offsetBottom + 18);
            $("#sapnSelect2").css({ top: 'auto', bottom: offsetBottom + 'px', left: left + 'px', position: 'absolute' });
        }

        $("#searchInputSelect2").focus();

    }

    function loadCategoryData() {
        $(".select2-results__options").html("");
        var thisValu = $("#searchInputSelect2").val();
        if (Com_CategoryArr != undefined && Com_CategoryArr.length > 0) {
            var userNum = 0;
            for (let i = 0; i < Com_CategoryArr.length; i++) {
                const element = Com_CategoryArr[i];
                if (thisValu != undefined && thisValu.length > 0) {
                    if (element.name.indexOf(thisValu) == -1) {
                        continue;
                    }
                }
                userNum += 1;
                var strOption = $.Format('<li class="select2-results__option select2-results__option--selectable" aria-selected="false" role="option" onclick="updateSelectVal(this,\'{0}\',\'{1}\')">{1}</li>', element.id, element.name);
                $(".select2-results__options").append(strOption);
            }
            if (userNum == 0) {
                $(".select2-results__options").append('<div style="text-align: center;">无匹配数据</div>');
            }
        }
    }
     
    function updateSelectVal(obj, id, name) {
        var index = COM_Current_SelectObj.attr("index");
        COM_Current_SelectObj.val(name);
        // $("#CategoryId_" + index).val(id);
        // $("#CategoryName_" + index).val(name);
        SpecialRoomTableObj.data[index].CategoryName = name;
        SpecialRoomTableObj.data[index].CategoryId = id;
        $("#sapnSelect2").hide();
    }

    function loadEquipmentNameData() {
        $(".select2-results__options").html("");
        var thisValu = $("#searchInputSelect2").val();
        if (Com_EquipmentDataArr != undefined && Com_EquipmentDataArr.length > 0) {
            var userNum = 0;
            for (let i = 0; i < Com_EquipmentDataArr.length; i++) {
                const element = Com_EquipmentDataArr[i];
                if (thisValu != undefined && thisValu.length > 0) {
                    if (element.Name.indexOf(thisValu) == -1) {
                        continue;
                    }
                }
                userNum += 1;
                var strOption = $.Format('<li class="select2-results__option select2-results__option--selectable" aria-selected="false" role="option" onclick="updateSelectEquipmentVal(this,\'{0}\',\'{1}\',\'{2}\')">{1}</li>', element.Id, element.Name, element.UnitName);
                $(".select2-results__options").append(strOption);
            }
            if (userNum == 0) {
                $(".select2-results__options").append('<div style="text-align: center;">无匹配数据</div>');
            }
        }
    }

    function updateSelectEquipmentVal(obj, id, name,unitname) {
      //  var index = COM_Current_SelectObj.attr("index");
        var index = COM_Current_SelectObj.parent().parent().index();
       COM_Current_SelectObj.val(name);
        //$("#UnitName_" + index).val(unitname);
        EquipmentTableObj.data[index].Name = name;
        EquipmentTableObj.data[index].EquipmentNameConfigId = id;
        EquipmentTableObj.data[index].UnitName = unitname;
        var tempRow = EquipmentTableObj.data[index];
        $.observable(EquipmentTableObj.data).remove(index).insert(index, tempRow);
        $("#sapnSelect2").hide();
    }
    //#endregion

    //#region 保存

    function verifyNoLessZeroMsg(value, msg) {
        var errormsg = '';
        if (!(value != undefined && parseInt(value) >= 0)) {
            errormsg = ('请填写' + msg + ',' + msg + '必须不小于0。<br/>');
        }
        return errormsg;
    }

    function saveForm(opttype) {
        if ($('#form').validate().form()) {
            var errorMsg = "";
            var postData = $('#form').getWebControls();

            for (var i in postData) {
                if (postData[i]) {
                    postData[i] = postData[i].replace(/[^0-9.]/g, '');
                }
            }

            if (opttype == 1) {
                if (!(postData.TotalAmount != undefined && parseInt(postData.TotalAmount) >= 0)) {
                    errorMsg += "请填写装备累计投入经费,装备累计投入经费必须不小于0。<br/>";
                }
                if (!(postData.CurrentYearAmount != undefined && parseInt(postData.CurrentYearAmount) >= 0)) {
                    errorMsg += "请填写装备当年投入经费,装备当年投入经费必须不小于0。<br/>";
                }
                if (errorMsg == '') {
                    if (parseFloat(postData.CurrentYearAmount) > parseFloat(postData.TotalAmount)) {
                        errorMsg += "装备累计投入经费必须不小于装备当年投入经费。<br/>";
                    }
                }
            } else if (opttype == 2) {
                // if (!(postData.MediaDeviceNum != undefined && parseInt(postData.MediaDeviceNum) >= 0)) {
                //     errorMsg += "请填写多媒体设备总数,多媒体设备总数必须不小于0。<br/>";
                // }
                if (!(postData.MediaFunroomNum != undefined && parseInt(postData.MediaFunroomNum) >= 0)) {
                    errorMsg += "请填写普通教室多媒体设备,普通教室多媒体设备必须不小于0。<br/>";
                }
                if (!(postData.MediaInteractiveNum != undefined && parseInt(postData.MediaInteractiveNum) >= 0)) {
                    errorMsg += "请填写普通教室多媒体设备的交互式多媒体,普通教室多媒体设备的交互式多媒体必须不小于0。<br/>";
                }
                if (Number(postData.MediaFunroomNum) < Number(postData.MediaInteractiveNum)) {
                    errorMsg += "普通教室多媒体设备必须不小于其中交互式多媒体的数量。<br/>";
                }
                if (!(postData.MedialaboratoryNum != undefined && parseInt(postData.MedialaboratoryNum) >= 0)) {
                    errorMsg += "请填写专用教室多媒体设备,专用教室多媒体设备必须不小于0。<br/>";
                }
                if (!(postData.MediaInteractiveLabNum != undefined && parseInt(postData.MediaInteractiveLabNum) >= 0)) {
                    errorMsg += "请填写专用教室多媒体设备的交互式多媒体,专用教室多媒体设备的交互式多媒体数必须不小于0。<br/>";
                }
                if (Number(postData.MedialaboratoryNum) < Number(postData.MediaInteractiveLabNum)) {
                    errorMsg += "专用教室多媒体设备必须不小于其中交互式多媒体的数量。<br/>";
                }
                if (!(postData.MediaOtherNum != undefined && parseInt(postData.MediaOtherNum) >= 0)) {
                    errorMsg += "请填写其它多媒体设备,其它多媒体设备必须不小于0。<br/>";
                }
                if (!(postData.MediaInteractiveOtherNum != undefined && parseInt(postData.MediaInteractiveOtherNum) >= 0)) {
                    errorMsg += "请填写其它多媒体设备的交互式多媒体,其它多媒体设备的交互式多媒体数必须不小于0。<br/>";
                }
                if (Number(postData.MediaOtherNum) < Number(postData.MediaInteractiveOtherNum)) {
                    errorMsg += "其它多媒体设备必须不小于其中交互式多媒体的数量。<br/>";
                }
            } else if (opttype == 3) {
                if (!(postData.ComputerStudentNum != undefined && parseInt(postData.ComputerStudentNum) >= 0)) {
                    errorMsg += "请填写学生计算机,学生计算机必须不小于0。<br/>";
                }
                if (!(postData.ComputerPadNum != undefined && parseInt(postData.ComputerPadNum) >= 0)) {
                    errorMsg += "请填写其中平板计算机,其中平板计算机必须不小于0。<br/>";
                }
                if (Number(postData.ComputerStudentNum) < Number(postData.ComputerPadNum)) {
                    errorMsg += "学生计算机必须不小于其中平板计算机的数量。<br/>";
                }
                if (!(postData.ComputerTeacherNum != undefined && parseInt(postData.ComputerTeacherNum) >= 0)) {
                    errorMsg += "请填写教师计算机,教师计算机必须不小于0。<br/>";
                }
                if (!(postData.ComputerPortableNum != undefined && parseInt(postData.ComputerPortableNum) >= 0)) {
                    errorMsg += "请填写其中便携式计算机,其中便携式计算机必须不小于0。<br/>";
                }
                if (Number(postData.ComputerTeacherNum) < Number(postData.ComputerPortableNum)) {
                    errorMsg += "教师计算机必须不小于其中平板计算机的数量。<br/>";
                }
            } else if (opttype == 7) {
                if (!(postData.SysNetworkWired != undefined && parseInt(postData.SysNetworkWired) >= 0)) {
                    errorMsg += "请填写有线网络系统数量,有线网络系统数量必须大于0。<br/>";
                }
                if (!(postData.SysNetworkWireless != undefined && parseInt(postData.SysNetworkWireless) >= 0)) {
                    errorMsg += "请填写无线网络系统数量,无线网络系统数量必须大于0。<br/>";
                }
                if (!(postData.SysCampusBroadcasting != undefined && parseInt(postData.SysCampusBroadcasting) >= 0)) {
                    errorMsg += "请填写校园广播系统数量,常态校园广播系统必须大于0。<br/>";
                }
                if (!(postData.SysSecurityMonitor != undefined && parseInt(postData.SysSecurityMonitor) >= 0)) {
                    errorMsg += "请填写安防监控系统数量,安防监控系统数量必须大于0。<br/>";
                }
                if (!(postData.SysElectronicClassCard != undefined && parseInt(postData.SysElectronicClassCard) >= 0)) {
                    errorMsg += "请填写电子班牌系统数量,电子班牌系统数量必须大于0。<br/>";
                }
                if (!(postData.SysOutdoorsLEDBig != undefined && parseInt(postData.SysOutdoorsLEDBig) >= 0)) {
                    errorMsg += "请填写户外LED大屏系统数量,户外LED大屏系统数量必须大于0。<br/>";
                }

                if (!(postData.SysCampusTV != undefined && parseInt(postData.SysCampusTV) >= 0)) {
                    errorMsg += "请填写校园电视台数量,校园电视台数量必须大于0。<br/>";
                }
                if (!(postData.SysNormalizedBroadcast != undefined && parseInt(postData.SysNormalizedBroadcast) >= 0)) {
                    errorMsg += "请填写录播系统数量,录播系统数量必须大于0。<br/>";
                }
                if (!(postData.SysCampusVisitor != undefined && parseInt(postData.SysCampusVisitor) >= 0)) {
                    errorMsg += "请填写校园访客系统数量,校园访客系统数量必须大于0。<br/>";
                }
                if (!(postData.SysOneCard != undefined && parseInt(postData.SysOneCard) >= 0)) {
                    errorMsg += "请填写一卡通系统数量,一卡通系统数量必须大于0。<br/>";
                }

                if (!(postData.SysLoT != undefined && parseInt(postData.SysLoT) >= 0)) {
                    errorMsg += "请填写物联网系统数量,物联网系统数量必须大于0。<br/>";
                }
                if (!(postData.SysStandardizedRoom != undefined && parseInt(postData.SysStandardizedRoom) >= 0)) {
                    errorMsg += "请填写标准化考场数量,标准化考场数量必须大于0。<br/>";
                }
            } else if (opttype == 4) {
                if (!(postData.NetworkAdminNum != undefined && parseInt(postData.NetworkAdminNum) >= 0)) {
                    errorMsg += "请填写网络管理员,网络管理员必须不小于0。<br/>";
                }
                if (!(postData.NetworkFulltimeNum != undefined && parseInt(postData.NetworkFulltimeNum) >= 0)) {
                    errorMsg += "请填写网络管理员-其中专职人员,其中专职人员必须不小于0。<br/>";
                }
                if (errorMsg == '') {
                    if (parseFloat(postData.NetworkFulltimeNum) > parseFloat(postData.NetworkAdminNum)) {
                        errorMsg += "网络管理员必须不小于其中专职人员。<br/>";
                    }
                }
            } else if (opttype == 5) {
                if (!(postData.BookTotalNum != undefined && parseInt(postData.BookTotalNum) >= 0)) {
                    errorMsg += "请填写图书总量,图书总量必须不小于0。<br/>";
                }
                if (!(postData.BookNewYearNum != undefined && parseInt(postData.BookNewYearNum) >= 0)) {
                    errorMsg += "请填写当年新增图书,当年新增图书必须不小于0。<br/>";
                }
                if (!(postData.BookReadersTerminal != undefined && parseInt(postData.BookReadersTerminal) >= 0)) {
                    errorMsg += "请填写供读者使用的终端,供读者使用的终端必须不小于0。<br/>";
                }
                if (!(postData.BookCabinetNum != undefined && parseInt(postData.BookCabinetNum) >= 0)) {
                    errorMsg += "请填写移动图书柜,移动图书柜必须不小于0。<br/>";
                }
                if (!(postData.BookPavilionNum != undefined && parseInt(postData.BookPavilionNum) >= 0)) {
                    errorMsg += "请填写朗读亭,朗读亭必须不小于0。<br/>";
                }
                if (!(postData.BookLibraryArea != undefined && parseInt(postData.BookLibraryArea) >= 0)) {
                    errorMsg += "请填写图书馆总面积,图书馆总面积必须不小于0。<br/>";
                }
                if (!(postData.BookRoomSeatNum != undefined && parseInt(postData.BookRoomSeatNum) >= 0)) {
                    errorMsg += "请填写阅览室座位,阅览室座位必须不小于0。<br/>";
                }
                if (!(postData.BookAdminNum != undefined && parseInt(postData.BookAdminNum) >= 0)) {
                    errorMsg += "请填写图书管理员,图书管理员必须不小于0。<br/>";
                }
                if (!(postData.BookFulltimeNum != undefined && parseInt(postData.BookFulltimeNum) >= 0)) {
                    errorMsg += "请填写图书管理员-其中专职人员,其中专职人员必须不小于0。<br/>";
                }
                if (errorMsg == '') {
                    if (parseFloat(postData.BookNewYearNum) > parseFloat(postData.BookTotalNum)) {
                        errorMsg += "图书总量必须不小于当年新增图书。<br/>";
                    }
                }
                if (errorMsg == '') {
                    if (parseFloat(postData.BookFulltimeNum) > parseFloat(postData.BookAdminNum)) {
                        errorMsg += "图书管理员必须不小于其中专职人员。<br/>";
                    }
                }
            } else if (opttype == 6) {
                errorMsg += verifyNoLessZeroMsg(postData.SportRunwayLength, '跑道长度');
                errorMsg += verifyNoLessZeroMsg(postData.SportRoomNum, '室内运动场');
                errorMsg += verifyNoLessZeroMsg(postData.SportPingPongNum, '乒乓球桌');
                errorMsg += verifyNoLessZeroMsg(postData.SportBasketballNum, '篮球场');
                errorMsg += verifyNoLessZeroMsg(postData.SportVolleyballNum, '排球场');
                errorMsg += verifyNoLessZeroMsg(postData.SportFootballNum, '足球场');
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            postData.OptType = opttype;
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/Report/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        if (opttype == 1) {
                            $("#spanTotalClassAverage").text(getFloatFormat(obj.Data.ClassAmountRate));
                            $("#spanTotalStudentAverage").text(getFloatFormat(obj.Data.StudentAmountRate));
                            $("#spanClassAverage").text(getFloatFormat(obj.Data.ClassAmountCurrentRate));
                            $("#spanStudentAverage").text(getFloatFormat(obj.Data.StudentAmountCurrentRate));
                        } else if (opttype == 3) {
                            $("#spanStudentComputerAverage").text(obj.Data.StudentComputRate);
                            $("#spanTeacherComputerAverage").text(obj.Data.TeacherComputRate);
                        } else if (opttype == 5) {
                            $("#spanStudentBookAverage").text(getFloatFormat(obj.Data.StudentBookRate));
                            $("#spanStudentBookNewAverage").text(getFloatFormat(obj.Data.StudentBookNewRate));
                        }
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function saveEquipmentForm(opttype) {
        var postData = [];
        var errorMsgTotal = "";
        if (EquipmentTableObj != undefined && EquipmentTableObj.data != undefined) {
            EquipmentTableObj.data.forEach(function (item, i, data) {
                var isempty = 0;
                var errorMsg = "";
                if (!(item.EquipmentNameConfigId != undefined && item.EquipmentNameConfigId > 0)) {
                    errorMsg += "请选择设备名称。<br/>";
                } else {
                    isempty = 1;
                }
                if (!(item.Brand != undefined && item.Brand.length > 0)) {
                    errorMsg += "请填写设备品牌。<br/>";
                } else if (item.Brand.length == 100) {
                    errorMsg += "填写的设备品牌请控制在50个字符内。<br/>";
                } else {
                    isempty = 1;
                }
                if (!(item.Modelz != undefined && item.Modelz.length > 0)) {
                    errorMsg += "请填写设备型号。<br/>";
                } else if (item.Modelz.length == 200) {
                    errorMsg += "填写的设备型号请控制在200个字符内。<br/>";
                } else {
                    isempty = 1;
                }

                if (item.Num && item.Num.length > 0) {
                    item.Num = item.Num.replace(/[^0-9.]/g, '');
                }
                if (!(item.Num != undefined && parseInt(item.Num) > 0)) {
                    errorMsg += "请填写设备数量,数量必须大于0。<br/>";
                } else {
                    isempty = 1;
                }
                if (item.Remark != undefined && item.Remark.length >= 500) {
                    errorMsg += "填写的备注请控制在500个字符内。<br/>";
                }
                if (isempty == 1) {
                    if (errorMsg != '') {
                        errorMsgTotal += ('第' + (i + 1) + '行：<br/>' + errorMsg);
                    }
                    item.Sort = i + 1;
                    item.OptType = opttype;
                    postData.push(item);
                }
            });
        } else {
            errorMsgTotal = "请填写科创设备。";
        }
        if (errorMsgTotal != '') {
            ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsgTotal);
            return;
        }
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/Report/SaveEquipmentFormJson")',
            type: 'post',
            data: { list: postData },
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    // parent.searchGrid();
                    // parent.layer.close(index);
                    $.observable(EquipmentTableObj).setProperty("data", obj.Data);
                    setJsrenderXFormat();
                    loadDormatJsrenderX();
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function saveSpecialRoomForm(opttype) {
        var postData = [];
        var errorMsgTotal = "";
        if (SpecialRoomTableObj != undefined && SpecialRoomTableObj.data != undefined) {
            SpecialRoomTableObj.data.forEach(function (item, i, data) {
                var isempty = 0;
                var errorMsg = "";
                if (!(item.CategoryName != undefined && item.CategoryName.length > 0)) {
                    errorMsg += "请选择分类名称。<br/>";
                } else {
                    isempty = 1;
                }
                if (!(item.Name != undefined && item.Name.length > 0)) {
                    errorMsg += "请填写专用室名称。<br/>";
                } else if (item.Name.length == 100) {
                    errorMsg += "填写的专用室名称请控制在100个字符内。<br/>";
                } else {
                    isempty = 1;
                }
                if (item.UseArea && item.UseArea.length > 0) {
                    item.UseArea = item.UseArea.replace(/[^0-9.]/g, '');
                }
                if (!(item.UseArea != undefined && parseInt(item.UseArea) > 0)) {
                    errorMsg += "请填写使用面积,使用面积必须大于0。<br/>";
                } else {
                    isempty = 1;
                }
                if (!(item.BuildTime != undefined && item.BuildTime.length >= 8)) {
                    errorMsg += "请填写起初建设时间。<br/>";
                } else {
                    isempty = 1;
                }
                if (!(item.ReformTime != undefined && item.ReformTime.length >= 8)) {
                    errorMsg += "请填写最新改造时间。<br/>";
                } else {
                    isempty = 1;
                }
                if (item.Remark != undefined && item.Remark.length >= 500) {
                    errorMsg += "填写的备注请控制在500个字符内。<br/>";
                }
                //全部为空不提交
                if (isempty == 1) {
                    if (errorMsg != '') {
                        errorMsgTotal += ('第' + (i + 1) + '行：<br/>' + errorMsg);
                    }
                    item.Sort = i;
                    item.OptType = opttype;
                    postData.push(item);
                }
            });
        } else {
            errorMsgTotal = "请填幼儿园专用室信息。";
        }
        if (errorMsgTotal != '') {
            ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsgTotal);
            return;
        }
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/Report/SaveSpecialRoomFormJson")',
            type: 'post',
            data: { list: postData },
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    // parent.searchGrid();
                    // parent.layer.close(index);
                    $.observable(SpecialRoomTableObj).setProperty("data", obj.Data);
                    setJsrenderXFormat();
                    loadDormatJsrenderX();
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    //#endregion


    function getCheckboxHtml2(idstr, namelabel, selected) {
        if (!(selected != undefined && parseInt(selected) > 0)) {
            selected = 0;
        }
        var html = '<label class="check-box">';
        if (selected > 0) {
            html += $.Format('<div class="icheckbox-blue checked"><input name="sys_checkbox" id="{0}" col="{0}" type="checkbox" value="1" style="position: absolute; opacity: 0;" checked="checked"></div>', idstr);
        } else {
            html += $.Format('<div class="icheckbox-blue"><input name="sys_checkbox" id="{0}" col="{0}" type="checkbox" value="0" style="position: absolute; opacity: 0;"></div>', idstr);
        }
        html += $.Format('{0}</label>', namelabel);

        return html;
    }
    function getCheckboxHtml(idstr, namelabel, selected) {
        if (!(selected != undefined && parseInt(selected) > 0)) {
            selected = 0;
        }
        var html = ' <label class="col-sm-6 control-label " >' + namelabel + '（套）：<font class="red" > * </font></label >';
        html += $.Format('<div class="col-sm-6" > <input id="{0}" col = "{0}" type = "text" class="form-control" value="{1}"/> </div>', idstr, selected);
        return html;
    }

    // function setCheckboxEvent() {
    //     $('input[name="sys_checkbox"]').change(function () {
    //         var idstr = $(this).attr("id");
    //         if ($(this).prop("checked")) {
    //             $(this).parent(".icheckbox-blue").addClass("checked");
    //             $(this).val("1")
    //             if (idstr == "SysNormalizedBroadcast-chk") {
    //                 $("#divBroadcastInput").show();
    //             }
    //         } else {
    //             $(this).val("0")
    //             $(this).parent(".icheckbox-blue").removeClass("checked");
    //             if (idstr == "SysNormalizedBroadcast-chk") {
    //                 $("#divBroadcastInput").hide();
    //             }
    //         }
    //     });
    // }

    function textClick(obj) {
        //$(obj).next().next().removeClass("show").addClass("hide")
        $(obj).blur(() => {
            //$(obj).next().next().removeClass("hide").addClass("show")
            if (!$(obj).val()) {
                $(obj).addClass('noform-control')
            } else {
                $(obj).removeClass('noform-control')
            }
        })
    }

    function textAreaClick(obj) {
        $(obj).blur(() => {
            if (!$(obj).text()) {
                $(obj).addClass('noform-control')
            } else {
                $(obj).removeClass('noform-control')
            }
        })
    }
    /**
    *
    * 备注信息弹出框修改
    */
    function modelTextarea(obj) {
        //赋值给span标签
        $($(obj).parent().prev()).text($(obj).val());
        $(obj).parent().parent().children(".model").removeClass('noform-control');
        //console.log(course_info.data, $(obj).parent().parent().children(".model").text())
    }
    $(document).on('mouseup', function (e) {
        //只有在mouseup是展示的效果是正确的，down不行
        let e_class = e.target.className;
        if (e_class != 'form-control form-control-lg modeltextarea' && e_class != 'area-control modelDiv') {
            $('.area-control').removeClass("modelDiv")
            $('.area-control').addClass("hide")
        }
    })

    //数据输入数字格式化。
    function loadFormDataFormat() {
        //表单所有数据格式化。
        $('input[type="text"]').bind("keyup", function () { Syjx.FormatDiGitNumChar(this) });
        $('input[type="text"]').focus(function () {
            Syjx.FormatDiGitNumChar(this);
        }).blur(function () {
            var value = $(this).val();
            if (value != undefined && parseFloat(value) > 0) {
                $(this).val(parseFloat(value).toLocaleString());
            }
        });
    }

    function setFormDataFormat() {
        $('input[type="text"]').each(function (i, item) {
            var val = $(this).val();
            if (val && val.length > 0) {
                $(this).val(parseFloat(val).toLocaleString());
            }
        })
    }

    function getFloatFormat(valnum) {
        if (valnum && parseFloat(valnum) > 0) {
            return parseFloat(valnum).toLocaleString();
        } else {
            return "0.00";
        }
    }

    function loadDormatJsrenderX() {
        $(".jsrender-x-num").bind("keyup", function () { Syjx.FormatDiGitNumChar(this) });
        $(".jsrender-x-num").focus(function () {
            Syjx.FormatDiGitNumChar(this);
        }).blur(function () {
            var value = $(this).val();
            if (value != undefined && parseInt(value) > 0) {
                $(this).val(parseFloat(value).toLocaleString());
            }
        });
    }

    function setJsrenderXFormat() {
        $(".jsrender-x-num").each(function (i, item) {
            var val = $(this).val();
            if (val && val.length > 0) {
                $(this).val(parseFloat(val).toLocaleString());
            }
        })
    }

    //#region 右侧锚点导航栏

    //添加目录
    var currObj;
    var offsetTop = 0;
    var h2List = new Array(), h3List = new Array();

    function CateNav(elem1, elem2) {
        var i1 = 0, i2 = 0, n1 = 0, n2 = 0;
        var temp = '<dl style="display:none;">';
        var cateList = $(elem1).html().match(/(<h[4][^>]*>.*?<\/h[4]>)/ig);
        for (var i = 0; i < cateList.length; i++) {
            if (IsKindSchool == 0) {
                var tempName = cateList[i].replace(/<[^>].*?>/g, "");
                if (tempName == '幼儿园专用室') {
                    continue;
                }
            }
            if (/(<h4[^>]*>.*?<\/h4>)/ig.test(cateList[i])) {
                n1++;
                n2 = 0;
                temp += '<dd class="cate-item1"><span></span><a href="#' + n1 + '">' + cateList[i].replace(/<[^>].*?>/g, "") + '</a></dd>';
                h2List[i1] = n1;
                i1++;
            } else {
                n2++;
                temp += '<dd class="cate-item2"><span></span><a href="#' + n1 + '_' + n2 + '">' + cateList[i].replace(/<[^>].*?>/g, "") + '</a></dd>';
                h3List[i2] = n1 + '_' + n2;
                i2++;
            }
        }
        temp += '</dl>';
        $(elem2).append(temp);

        //添加锚点

        var i1 = i2 = 0;
        $(elem1).find('h4').each(function () {
            $(this).prepend('<a name="' + h2List[i1] + '"></a>');
            i1++;
        });
        $(elem1).find('h5').each(function () {
            $(this).prepend('<a name="' + h3List[i2] + '"></a>');
            i2++;
        });

        //点击锚点，跳转制定位置

        $(elem2 + ' a').click(function (e) {
            e.preventDefault();
            $(elem2 + ' dd').removeClass('active');
            $(this).parent('dd').addClass('active');
            currObj = $("[name='" + $(this).attr('href').replace(/#/, '') + "']");
            offsetTop = currObj.position().top;
            $('html,body').animate({
                scrollTop: offsetTop
            }, 500, 'swing');
        });

        //屏幕滚动，显示并选中锚点

        var windowTop = 0;
        $('.gray-bg').scroll(function () {
            var panelwidth = $(this).width();
            if (panelwidth < 800) {
                $(elem2).hide();
            } else {
                $(elem2).show();
            }

            windowTop = $(window).scrollTop();
            if (windowTop >= $(elem1).offset().top) {
                $(elem2 + ' dl').slideDown(900);
            } else {
                $(elem2 + ' dl').slideUp(900);
            }
            $(elem2 + ' a').each(function () {
                currObj = $("[name='" + $(this).attr('href').replace(/#/, '') + "']");
                offsetTop = currObj.offset().top;
                if (windowTop > offsetTop) {
                    $(elem2 + ' dd').removeClass('active');
                    $(this).parent('dd').addClass('active');
                    return;
                }
            });
        });
    }

    //#endregion

    //#region
    var Old_Data = 0;
    function loadBindInputChange() {
        //设置分组
        $('.input_change').off("blur").off("focus");
        $(".input_change").blur(function () {
            var currentval = $(this).val();
            var opttype = $(this).attr("opttype");
            if (currentval != Old_Data) {
                setCountValForm(opttype);
            }
        }).focus(function () {
            Old_Data = $(this).val();
        });
    }


    function setCountValForm(opttype) {
        if ($('#form').validate().form()) {
            var errorMsg = "";
            var postData = $('#form').getWebControls();

            for (var i in postData) {
                if (postData[i]) {
                    postData[i] = postData[i].replace(/[^0-9.]/g, '');
                }
            }
            if (opttype == 1) {
                if (!(postData.TotalAmount != undefined && parseInt(postData.TotalAmount) >= 0)) {
                    errorMsg += "请填写装备累计投入经费,装备累计投入经费必须不小于0。<br/>";
                }
            } else if (opttype == 3) {
                if (!(postData.ComputerStudentNum != undefined && parseInt(postData.ComputerStudentNum) >= 0)) {
                    errorMsg += "请填写学生计算机,学生计算机必须不小于0。<br/>";
                }
                if (!(postData.ComputerTeacherNum != undefined && parseInt(postData.ComputerTeacherNum) >= 0)) {
                    errorMsg += "请填写教师计算机,教师计算机必须不小于0。<br/>";
                }
            } else if (opttype == 5) {
                if (!(postData.BookTotalNum != undefined && parseInt(postData.BookTotalNum) >= 0)) {
                    errorMsg += "请填写图书总量,图书总量必须不小于0。<br/>";
                }
            }
            if (errorMsg != '') {
                return false;
            }
            postData.OptType = opttype;
            $.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/Report/GetReportDetailCountEntityJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (opttype == 1) {
                            $("#spanTotalClassAverage").text(getFloatFormat(obj.Data.ClassAmountRate));
                            $("#spanTotalStudentAverage").text(getFloatFormat(obj.Data.StudentAmountRate));
                            $("#spanClassAverage").text(getFloatFormat(obj.Data.ClassAmountCurrentRate));
                            $("#spanStudentAverage").text(getFloatFormat(obj.Data.StudentAmountCurrentRate));
                        } else if (opttype == 3) {
                            $("#spanStudentComputerAverage").text(obj.Data.StudentComputRate);
                            $("#spanTeacherComputerAverage").text(obj.Data.TeacherComputRate);
                        } else if (opttype == 5) {
                            $("#spanStudentBookAverage").text(getFloatFormat(obj.Data.StudentBookRate));
                            $("#spanStudentBookNewAverage").text(getFloatFormat(obj.Data.StudentBookNewRate));
                        }
                    }
                }
            });
        }
    }

    //#endregion
</script>