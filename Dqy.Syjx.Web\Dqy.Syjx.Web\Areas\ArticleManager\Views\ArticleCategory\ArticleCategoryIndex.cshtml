﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        名称：<input id="Name" col="Name" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ArticleManager/ArticleCategory/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: "Sort",
            sortOrder:"asc",
            columns: [
                { checkbox: true, visible: true },
                { field: 'Name', title: '名称', width: 120, sortable: true, halign: 'center', align: 'center', },
                {
                    field: 'CateType', title: '显示位置', width: 120, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = "@DisplayLocationEnum.Index.GetDescription()";
                        if (value == "@DisplayLocationEnum.Other.ParseToInt()") {
                            html = "@DisplayLocationEnum.Other.GetDescription()";
                        }
                        return html;
                    }
                },
                { field: 'Sort', title: '排序值', width: 120, sortable: true, halign: 'center', align: 'center', },
                { field: 'ConfigCode', title: '自定义编码', width: 120, sortable: true, halign: 'center', align: 'center', },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function clearGrid() {
        $("#Name").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/ArticleManager/ArticleCategory/ArticleCategoryForm")' + '?id=' + id,
            width: '768px',
            height: '400px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ArticleManager/ArticleCategory/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }
</script>
