﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
   @* <div class="row" style="height:auto;">
        <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-down"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            注：只能添加你所管理学科的校本实验！
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolStage" col="SchoolStage" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="GradeId" col="GradeId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="CourseId" col="CourseId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="ExperimentType" col="ExperimentType" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="IsNeedDo" col="IsNeedDo" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" type="text" placeholder="实验名称、章节" style="display: inline-block;width:260px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 新增</a>
            @*<a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>*@
            @*<a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>*@
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSchoolStage();
        loadGrade();
        loadCourse(0);
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            defaultName: '学期'
        });
        $("#ExperimentType").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())),
            defaultName: '实验类型'
        });
        $("#IsNeedDo").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())),
            defaultName: '实验要求'
        });
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: " Id ",
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                {
                    field: 'opt', title: '操作', width: 160, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<span class="btn btn-success btn-xs" onclick="showSaveForm(true,\'' + row.Id +'\')"><i class="fa fa-edit"></i>修改</span>');
                        actions.push('<span class="btn btn-danger btn-xs" onclick="deleteForm(\'' + row.Id +'\')"><i class="fa btn-delete fa-remove"></i>删除</span>');
                        return actions.join('');
                    }
                },
                //{ checkbox: true, visible: true },
                { field: 'Statuz', title: '状态' , sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html ='<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>'; 
                        if (value == "@StatusEnum.Yes.ParseToInt()") {
                             html = '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        }  
                        return html;
                    }
                },
                { field: 'SchoolStageName', title: '学段', sortable: true, halign: 'center', valign: 'middle', align: 'center', width: 80 },
                { field: 'GradeName', title: '年级', sortable: true, halign: 'center', valign: 'middle', align: 'center', width: 100 },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', valign: 'middle', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '@SchoolTermEnum.LastSemester.GetDescription()';
                        if ( @SchoolTermEnum.NextSemester.ParseToInt()== value) {
                            html = '@SchoolTermEnum.NextSemester.GetDescription()';
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center'},
                { field: 'Chapter', title: '章节', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'ExperimentCode', title: '实验代码', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'ExperimentName', title: '<p style="min-width:160px;">实验名称<p>', sortable: true, halign: 'center', valign: 'middle', align: 'left'},
                {
                    field: 'ExperimentType', title: '实验类型', sortable: true, width: 80, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return value == @ExperimentTypeEnum.Demo.ParseToInt() ? "@ExperimentTypeEnum.Demo.GetDescription()" : "@ExperimentTypeEnum.Group.GetDescription()";
                    }
                },
                {
                    field: 'IsNeedDo', title: '实验要求', sortable: true, width: 80, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return value == @IsNeedEnum.MustDo.ParseToInt() ? "@IsNeedEnum.MustDo.GetDescription()" : "@IsNeedEnum.SelectToDo.GetDescription()";
                    }
                },
                { field: 'EquipmentNeed', title: '<p style="min-width:160px;">所需仪器<p>', sortable: true, halign: 'center', align: 'left' },
                // { field: 'MaterialNeed', title: '<p style="min-width:160px;">实验材料（含试剂）<p>', sortable: true, halign: 'center', align: 'left' },
                // { field: 'Remark', title: '<p style="min-width:160px;">备注<p>', sortable: true,halign: 'center', align: 'left' }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $('#SchoolStage').ysComboBox('setValue', -1);
        $('#GradeId').ysComboBox('setValue', -1);
        $('#SchoolTerm').ysComboBox('setValue', -1);
        $('#CourseId').ysComboBox('setValue', -1);
        $('#ExperimentType').ysComboBox('setValue', -1);
        $('#IsNeedDo').ysComboBox('setValue', -1);
        $('#statuz').ysComboBox('setValue', -1);
        $('#Name').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd, id) {
        if (id == 0) {
            if (!bAdd) {
                var selectedRow = $('#gridTable').bootstrapTable('getSelections');
                if (!ys.checkRowEdit(selectedRow)) {
                    return;
                }
                else {
                    id = selectedRow[0].Id;
                }
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/Form")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(id) {
        var ids = '';
        var tagMsg = '';
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                tagMsg = '确认要删除选中的' + selectedRow.length + '条数据吗？';
                ids = ys.getIds(selectedRow);
            }
        } else {
            ids = id;
            tagMsg = '确认要删除当前条数据吗？';
        }
        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function loadSchoolStage() {
        $('#SchoolStage').ysComboBox({});
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomUse/GetSchoolStageJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#SchoolStage').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        defaultName:'学段'
                    });
                }
            }
        });
    }
      function loadGrade() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1002",
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var pids = '';
                    $.each(obj.Data, function (i, n) {
                        if (i > 0) {
                            pids += ',';
                        }
                        pids += n.DictionaryId;
                    });
                    $('#GradeId').ysComboBox({
                        url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1003&Pids=" + pids,
                        key: 'DictionaryId',
                        value: 'DicName',
                        defaultName: '年级',
                        onChange: function () {
                            var selectid = $('#GradeId').ysComboBox('getValue');
                            loadCourse(selectid);
                        }
                    });
                }
            }
        });
    }
    function loadCourse(gradeid) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + "?TypeCode=1005&Nature=1&Pid=" + gradeid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data);
                } else {
                    bindCourseData({});
                }
            }
        });
    }
    function bindCourseData(data) {
        $('#CourseId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '适用学科'
        });
    }
</script>
