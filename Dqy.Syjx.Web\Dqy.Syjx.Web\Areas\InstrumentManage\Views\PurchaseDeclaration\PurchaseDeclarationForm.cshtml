﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int IsAllowEditModel = (int)ViewBag.IsAllowEditModel;
}

<div class="container-div">
    <div class="btn-group d-flex" role="group" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>填报采购计划</h5>
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">采购年度<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <div id="purchaseYear" col="PurchaseYear"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="名称可修改"></span>
                        仪器名称<font class="red"> *</font>
                    </label>
                    <div class="col-sm-8">
                        <input id="name" col="Name" type="text" class="form-control" onclick="chooseStandard(0);" />
                        <input id="instrumentStandardId" col="InstrumentStandardId" type="hidden" />
                    </div>
                    <div class="col-sm-2" style="padding-left:0px;">
                        <a class="btn btn-sm btn-success" onclick="chooseStandard(1)"><i class="fa fa-search"></i> 选择</a>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">选择规格属性<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <div id="modelStandardId"></div>
                    </div>
                    <div class="col-sm-2" style="padding-left:0px;padding-top:5px;">
                        <span id="code" style="color:#999"></span>
                    </div>
                </div>
                <div class="form-group row" id="divModel" style="display:none;">
                    <label class="col-sm-2 control-label ">规格属性<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="model" col="Model" type="text" class="form-control" />
                    </div>
                    <div class="col-sm-2" style="padding-left:0px;">
                        <a class="btn btn-sm btn-success" onclick="chooseMallProduct()"><i class="fa fa-search"></i> 在线选型</a>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">单位<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="unitName" col="UnitName" type="text" class="form-control" readonly />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">数量<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="num" col="Num" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">单价(元)<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="price" col="Price" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">适用学段<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <div id="stageId" col="StageId"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">
                        <span class="glyphicon glyphicon-question-sign titleprompt inputprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-placement="right"
                              data-content="请确认所选仪器与适用学科匹配"></span>
                    适用学科<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <div id="courseId" col="CourseId"></div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label " id="labReason">采购理由</label>
                    <div class="col-sm-8">
                        <input id="Reason" col="Reason" type="text" class="form-control" placeholder="如损坏等" />
                    </div>
                </div>
                <div class="form-group row" style="color:red;display:none;" id="divApprovalRemark">
                    <label class="col-sm-2 control-label ">退回原因</label>
                    <div class="col-sm-8">
                        <input id="approvalRemark" col="ApprovalRemark" readonly type="text" class="form-control" />
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm(@InstrumentAuditStatuzEnum.DeclareIng.ParseToInt());"><i class="fa fa-save"></i> 保存</a>&nbsp;&nbsp;
            @*<a id="btnAdd" class="btn btn-info" onclick="saveForm(@InstrumentAuditStatuzEnum.WaitSchoolAudit.ParseToInt());"><i class="fa fa-edit"></i> 转交下一步</a>
            <span style="color:#999;">（下一步转交学校审批）</span>*@
        </div>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");

    var IsEdit = false;
    $(function () {
        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });

        getYear();
        $('#stageId').ysComboBox({ class: "form-control" });
        $('#courseId').ysComboBox({ class: "form-control" });

        getForm();
        loadFormVali();
    });

    function chooseStandard(type) {
        if (!$('#instrumentStandardId').val() || $('#name').val() == '' || type == 1) {
            ys.openDialog({
                title: '选择仪器',
                content: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/StandardChoose")',
                width: '980px',
                height: '600px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }
    }

    function chooseMallProduct() {
        if ($('#instrumentStandardId').val() > 0) {
            ys.openDialog({
                title: '在线选型',
                content: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/MallProductChoose")' + '?instrumentStandardId=' + $('#instrumentStandardId').val() + '&isAllowEditModel=@IsAllowEditModel',
                width: '980px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
            if (@IsAllowEditModel != 1) $('.layui-layer-btn0').hide();
        }
        else {
            ys.msgError('请先选择仪器分类！');
        }
    }

    function getYear() {
        var d = new Date();
        var nowYear = d.getFullYear();
        var nextYear = nowYear + 1;
        $("#purchaseYear").ysComboBox({
            data: [{
                "id": nowYear,
                "text": nowYear
            }, {
                "id": nextYear,
                "text": nextYear,
            }],
            key: "id",
            value: "text",
            class: "form-control"
        });
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        IsEdit = true;
                        getInstrumentModel(obj.Data.InstrumentStandardId, obj.Data.ModelStandardId);
                        $('#divModel').show();
                        getSchoolStage(obj.Data.StageId);
                        getCourse(obj.Data.CourseId);
                        $('#form').setWebControls(obj.Data);
                        if (obj.Data.IsGoBack == 1) {
                            $('#divApprovalRemark').show();
                        }

                        if (obj.Data.VarietyAttribute == "@VarietyAttributeEnum.Reagent.ParseToInt()") {
                            //试剂类可自定义输入计量单位
                            $('#unitName').prop('readonly', false);
                        }
                        else {
                            $('#unitName').prop('readonly', true);
                        }

                        $('#code').html(obj.Data.Code);
                    }
                }
            });
        }
        else {
            var defaultData = {
                PurchaseYear:new Date().getFullYear()
            };
            getInstrumentModel(0);
            getSchoolStage();
            getCourse();
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(statuz) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({
                Id: id,
                Statuz: statuz,
                ModelStandardId: $('#modelStandardId').ysComboBox('getValue')
            });

            var checkErr = checkMessage(postData);
            if (checkErr != '') {
                ys.msgError(checkErr);
                return false;
            }

            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/PurchaseDeclarationIndex")';
                        createMenuAndCloseCurrent(url, "填报采购计划");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function setInstrumentStandard(id, name) {
        $('#instrumentStandardId').val(id);
        $('#name').val(name);
        getInstrumentEntity(id);
        getInstrumentModel(id);
    }

    function getInstrumentModel(id, defaultValue) {
        $('#modelStandardId').ysComboBox({
            data: [],
            key: 'Id',
            value: 'Model',
            class: "form-control",
            onChange: function () {
                if (!IsEdit) {
                    if ($('#modelStandardId').ysComboBox('getValue') == '') {
                        $('#divModel').hide();
                        $('#model').val('');
                    }
                    else {
                        $('#divModel').show();
                        var model = $("#modelStandardId option:checked").text();
                        model = model == '手动填写' ? '' : model;
                        $('#model').val(model);
                    }
                    getStandardCode();
                }
                else {
                    IsEdit = false;
                }
            }
        });
        if (id > 0) {
            var param = { Pid: id, ClassType: 2 };
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetInstrumentStandard")',
                data: { param: param },
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        if (@IsAllowEditModel == 1) obj.Data.push({ Id: 0, Model: '手动填写' });
                        else $('#model').prop('disabled', true);
                        $('#modelStandardId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Model',
                            class: "form-control"
                        });
                        if (defaultValue > -1) {
                            $('#modelStandardId').ysComboBox('setValue', defaultValue);
                        }
                        else {
                            if (obj.Data.length == 1) {
                                $('#modelStandardId').ysComboBox('setValue', obj.Data[0].Id);
                            }
                        }
                    }
                }
            });
        }
    }

    function getStandardCode() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetStandardCode")' + '?modelStandardId=' + $('#modelStandardId').ysComboBox('getValue') + '&instrumentStandardId=' + $('#instrumentStandardId').val(),
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#code').html(obj.Data);
                }
                else {
                    $('#code').html('');
                }
            }
        });
    }

    function getInstrumentEntity(id) {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetEntity")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#unitName').val(obj.Data.UnitName);
                    $('#name').val(obj.Data.Name);

                    if (obj.Data.VarietyAttribute == "@VarietyAttributeEnum.Reagent.ParseToInt()") {
                        //试剂类可自定义输入计量单位
                        $('#unitName').prop('readonly', false);
                    }
                    else {
                        $('#unitName').prop('readonly', true);
                    }
                }
            }
        });
    }

    function getSchoolStage(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSchoolStageByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#stageId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control"
                    });
                    if (obj.Data.length == 0) {
                        ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.BusinessManager.ParseToInt());
                    }
                    else {
                        if (defaultValue > 0) {
                            $('#stageId').ysComboBox('setValue', defaultValue);
                        }
                        else if (obj.Data.length == 1) {
                            $('#stageId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                        }
                    }
                }
            }
        });
    }

    function getCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSubjectByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#courseId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control"
                    });
                    if (defaultValue > 0) {
                        $('#courseId').ysComboBox('setValue', defaultValue);
                    }
                    else if (obj.Data.length == 1) {
                        $('#courseId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                }
            }
        });
    }

    function openUserClassInfo() {
        var url = '@Url.Content("~/BusinessManage/UserSchoolStageSubject/Index")';
        createMenuItem(url, "管理员授权");
    }

    //保存校验
    function checkMessage(postData) {
        var checkErr = '';
        if (!postData.ModelStandardId) {
            checkErr += '请选择规格属性！<br />';
        }
        else if (postData.Model.trim() == '') {
            checkErr += '请填写规格属性！<br />';
        }
        if (!postData.StageId) {
            checkErr += '请选择适用学段！<br />';
        }
        if (!postData.CourseId) {
            checkErr += '请选择适用学科 ！<br />';
        }
        return checkErr;
    }

    //确认选择仪器商城仪器
    function setMallProduct(row) {
        if (@IsAllowEditModel == 1) {
            $('#model').val(row.Model);
            $('#price').val(row.Price);
        }
    }


    //#region
    //加载配置验证信息
    function loadFormVali() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/ConfigSet/GetListByCode")',
            type: 'Post',
            data: { typecode:'1SYYQ-1CGJH-1CGJH'},
            success: function (obj) {
                if (obj.Tag == 1) {
                    var validateRule = {};
                    var validateMessage = {};
                    validateRule.purchaseYear = { required: true };
                    validateMessage.purchaseYear = { required: '请选择采购年度' };
                    validateRule.name = { required: true };
                    validateMessage.name = { required: '请填写仪器名称' };
                    validateRule.model = { required: true };
                    validateMessage.model = { required: '请选择选择规格属性' };
                    validateRule.unitName = { required: true };
                    validateRule.num = { required: true, number: true, thanMinValue: 0 };
                    validateMessage.num = { required: '请填写数量', number: '请正确填写数量', thanMinValue: '请正确填写数量' };
                    validateRule.price = { required: true, number: true };
                    validateMessage.price = { required: '请填写单价(元)', number: '请正确填写单价(元)' };
                    validateRule.stageId = { required: true };
                    validateMessage.stageId = { required: '请选择适用学段' };
                    validateRule.courseId = { required: true };
                    validateMessage.courseId = { required: '请选择适用学科' };
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        for (var i = 0; i < obj.Data.length; i++) {
                            if (obj.Data[i].ConfigValue == "1") {
                                var typecode = obj.Data[i].TypeCode.replaceAll("1SYYQ-1CGJH-1CGJH-", "");
                                var verifyhint = "";
                                if (obj.Data[i].VerifyHint != undefined && obj.Data[i].VerifyHint.length > 0) {
                                    verifyhint = obj.Data[i].VerifyHint;
                                }
                                $("#lab" + typecode).append('<font class="red"> *</font>');
                                if (typecode == "Reason") {
                                    validateRule.Reason = { required: true };
                                    validateMessage.Reason = { required: verifyhint };
                                } else {
                                    SaveValiDate.push({ typecode: typecode, verifyhint: verifyhint });
                                }
                            }
                        }
                    }
                    $('#form').validate({
                        rules: validateRule,
                        messages: validateMessage
                    });
                }
            }
        });
    }

    //#endregion
</script>

