﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolYearStart" col="SchoolYearStart" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="GradeId" col="GradeId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="CourseId" col="CourseId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="ExperimentType" col="ExperimentType" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="IsNeedDo" col="IsNeedDo" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <span id="CreatorId" col="CreatorId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="PlanName" col="PlanName" placeholder="名称" style="display: inline-block;width:180px;" type="text" />
                    </li>
                    <li>
                    <li>
                        <div id="IsShowCurrentSchoolYear" col="IsShowCurrentSchoolYear" style="display:inline-block;width:136px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    $(function () {
        $('#IsShowCurrentSchoolYear').ysCheckBox({
            data: [{ Key: 1, Value: '只显示当前学年' }]
        });

        loadGrade();
        loadCourse(0);
        loadExperimentType();
        loadIsNeedDo();
        loadUser();

        ComBox.SchoolTermYear($('#SchoolYearStart'), undefined, '学年');
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            defaultName: '学期'
        });
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        initGrid();
    });
    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolGradeListJson")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级',
            onChange: function () {
                var selectid = $('#GradeId').ysComboBox('getValue');
                loadCourse(selectid);
            }
        }); 
    }
    function loadCourse(gradeid) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + "?TypeCode=1005&Nature=1&Pid=" + gradeid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data);
                } else {
                    bindCourseData({});
                }
            }
        });
    }
    function bindCourseData(data) {
        $('#CourseId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '课程'
        });
    }
    function loadExperimentType() {
        $("#ExperimentType").ysComboBox({
            data: ys.getJson([{ "Key": 1, "Value": "演示" }, { "Key": 2, "Value": "分组" }]),
            defaultName: '实验类型'
        });
    }

    function loadIsNeedDo() {
        $("#IsNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });//实验要求

    }
    function loadUser() {
        $('#CreatorId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '编制人'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetPageListJson")' + '?OptType=2';;
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                {
                    field: 'SchoolYearStart', title: '学年', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2);
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '@SchoolTermEnum.LastSemester.GetDescription()';
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== value) {
                            html = '@SchoolTermEnum.NextSemester.GetDescription()';
                        }
                        return html;
                    }
                },
                { field: 'GradeName', title: '年级', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'CourseName', title: '课程', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'PlanName', title: '实验计划名称', sortable: true, width: 300, halign: 'center', valign: 'middle' },
                { field: 'Num', title: '已编实验数量', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'CompulsoryType', title: '高中班级类型', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.GradeId >= @GradeEnum.GaoYi.ParseToInt()) {
                            if (value == @ClassCompulsoryTypeEnum.Select.ParseToInt()) {
                                html = $.Format('选修{0}班', row.CourseName);
                            } else {
                                html = $.Format('非选修{0}班', row.CourseName);
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'UseNum', title: '计划明细', width: 90, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-info btn-xs" href="#" onclick="openDetailForm(\'' + row.Id + '\')"><i class="fa fa-eye"></i>查看</a>&nbsp;');
                        return actions.join('');
                    }
                },
                {
                    field: 'ClassNamez', title: '班级', sortable: true, width: 300, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (value != undefined && value.length > 0) {
                            html = value;
                        }
                        return html;
                    }
                },
                { field: 'RealName', title: '编制人', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#GradeId").ysComboBox('setValue', -1);
        $("#CourseId").ysComboBox('setValue', -1);
        $("#SchoolYearStart").ysComboBox('setValue', -1);
        $('#SchoolTerm').ysComboBox('setValue', -1);
        $("#PlanName").val('');
        $("#ExperimentType").ysComboBox('setValue', -1);
        $("#IsNeedDo").ysComboBox('setValue', -1);
        $('#CreatorId').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function openDetailForm(id) {
        var url = '@Url.Content("~/ExperimentTeachManage/PlanDetail/ListDetail")' + '?id=' + id;
        createMenuItem(url, "计划明细");
    }
</script>
