﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <div class="btn-group d-flex" role="group" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            <h5>报废登记</h5>
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">仪器名称<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="name" col="Name" type="text" class="form-control" onclick="chooseInstrument();" />
                        <input id="schoolInstrumentId" col="SchoolInstrumentId" type="hidden" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">报废数量<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="scrapNum" col="ScrapNum" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">报废时间<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="scrapTime" col="ScrapTime" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">报废原因<font class="red"> *</font></label>
                    <div class="col-sm-8">
                        <input id="scrapContent" col="ScrapContent" type="text" class="form-control" />
                    </div>
                </div>
            </form>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-edit"></i> 保存</a>
        </div>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        laydate.render({ elem: '#scrapTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        getForm();

        $('#form').validate({
            rules: {
                name: { required: true },
                scrapNum: { required: true, number: true, thanMinValue: 0 },
                scrapTime: { required: true },
                scrapContent: { required: true }
            }
        });
    });

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentScrap/GetFormJson")' + '?id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        obj.Data.ScrapTime = obj.Data.ScrapTime.substring(0, 10).replace(/\//g, '-');
                        $('#form').setWebControls(obj.Data);
                    }
                }
            });
        }
        else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
        }
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentScrap/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        var url = '@Url.Content("~/InstrumentManage/InstrumentScrap/InstrumentScrapIndex")';
                        createMenuAndCloseCurrent(url, "报废登记");
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    function chooseInstrument() {
        ys.openDialog({
            title: '选择仪器',
            content: '@Url.Content("~/InstrumentManage/InstrumentScrap/ChooseInstrumentForm")',
            width: '980px',
            height: '600px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function setInstrumentId(row) {
        $('#schoolInstrumentId').val(row.Id);
        $('#name').val(row.Name);
    }
</script>

