﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.js"))
}

<div class="ui-layout-west">
    <div class="main-content">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    所属部门
                </div>
                <div class="box-tools pull-right">
                    <a type="button" class="btn btn-box-tool menuItem" href="#" onclick="showDepartmentForm()" title="管理部门"><i class="fa fa-edit"></i></a>
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div id="departmentTree" class="ztree"></div>
            </div>
        </div>
    </div>
</div>

<div class="container-div ui-layout-center">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <input type="hidden" id="departmentId" col="DepartmentId">
            <div class="select-list">
                <ul>
                    <li>
                        用户角色：<span id="roleId" col="RoleId"></span>
                    </li>
                    <li>
                        用户状态：<span id="userStatus" col="UserStatus"></span>
                    </li>
                    <li>
                        <input id="realName" col="RealName" type="text" placeholder="姓名、手机号、登录账号" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-primary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div id="toolbar" class="btn-group d-flex" role="group">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 添加</a>
            @*<a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>*@
            <a id="btnImport" class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();

        initTree();

        $('body').layout({ west__size: 185 });

       $("#userStatus").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });


        $("#roleId").ysComboBox({
            url: '@Url.Content("~/SystemManage/Role/GetListJson")',
            key: "Id",
            value: "RoleName",
        });

        $('#btnExpand').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });



        $('#btnRefresh').click(function () {
            initTree();
        });


        
        
    });

   function initTree() {
        $('#departmentTree').ysTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            async: true,
            expandLevel: 2,
            maxHeight: "700px",
            callback: {
                onClick: function (event, treeId, treeNode) {
                    $("#departmentId").val(treeNode.id);
                    searchGrid();
                }
            }
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/User/GetUserListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;&nbsp;');
                        //actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="showResetPasswordForm(\'' + row.Id + '\')"><i class="fa fa-key"></i>重置</a>&nbsp;&nbsp;');
                        if (row.UserStatus == "@StatusEnum.Yes.ParseToInt()") {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'禁用\')"><i class="fa fa-check"></i>禁用</a>&nbsp;&nbsp;');
                        } else {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'启用\')"><i class="fa fa-check"></i>启用</a>&nbsp;&nbsp;');
                        }
                        //actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="DelUser(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;');
                        return actions.join('');
                    }
                },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'DepartmentNames', title: '所属部门', sortable: false },
                { field: 'RealName', title: '姓名', sortable: false },
                { field: 'Mobile', title: '手机号', sortable: false },
                { field: 'UserName', title: '登录账号', sortable: false },
                { field: 'RoleNames', title: '用户角色', sortable: false },
                {
                    field: 'UserStatus', title: '用户状态', formatter: function (value, row, index) {
                        if (row.UserStatus == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $("#searchDiv").getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function clearGrid() {
        $('#roleId').ysComboBox('setValue', -1)
        $('#userStatus').ysComboBox('setValue', -1)
        initTree();
        $("#departmentId").val(null);
        $("#realName").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(id) {
        ys.openDialog({
            title: id > 0 ? "编辑用户" : "添加用户",
            height: "600px",
            content: '@Url.Content("~/OrganizationManage/User/UserListForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function ChangeStatuz(Id, strStatuz) {
        ys.confirm("确认要" + strStatuz +"吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/UpdateStatuzFormJson")' + '?id=' + Id,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(strStatuz+" 成功");
                            searchGrid();
                        }
                        else {
                            ys.msgError(strStatuz + " 失败!"+obj.Message);
                        }
                    }
                });
        });
    }

    function DelUser(id) {
         ys.confirm("确认要删除此条数据吗？", function () {
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/User/DeleteUserFormJsonById")' + '?id=' + id,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
    }

    function deleteForm() {
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm("确认要删除选中的" + selectedRow.length + "条数据吗？", function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/User/DeleteFormJson")' + '?ids=' + ids,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function showDepartmentForm() {
        var url = '@Url.Content("~/OrganizationManage/Department/DepartmentIndex")';
        createMenuItem(url, "部门管理");
    }

    function importForm() {
      ys.openDialog({
            title: "导入用户数据",
            content: '@Url.Content("~/OrganizationManage/User/UserImport")',
            height: "280px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showResetPasswordForm(id) {
        ys.openDialog({
            title: "重置密码",
            content: '@Url.Content("~/OrganizationManage/User/ResetPassword")' + '?id=' + id,
            height: "220px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
