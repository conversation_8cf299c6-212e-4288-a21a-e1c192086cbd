﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.1/laydate.js"), true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsrender.js"), true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsviews.js"), true)
<style type="text/css">
    /* 设置只展示时分，隐藏秒那一列 */
    .laydate-time-list {
        padding-bottom: 0;
        overflow: hidden;
    }

        .laydate-time-list > li {
            width: 50% !important;
        }

            .laydate-time-list > li:last-child {
                display: none;
            }

        .laydate-time-list ol li {
            width: 100% !important;
            padding-left: 0 !important;
            text-align: center !important;
        }

    .iradio-box {
        position: initial;
        display: inline-block;
        font-size: 14px;
    }

    .iradio-blue {
        position: inherit;
        display: inline-block;
        /*  top: 6px;*/
    }

    .select2-search {
        width: 100%;
    }

        .select2-search .select2-search__field {
            width: 100% !important;
        }


    .check-box {
        width: 150px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 12px;
    }
</style>
<div class="container-div gray-bg">
    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
            @*<div class="ibox-title">
            <h5 class="table-tswz">友情提示</h5>
            <div class="ibox-tools">
            <a class="collapse-link">
            <i class="fa fa-chevron-down"></i>
            </a>
            </div>
            </div>*@
            <div class="ibox-content" style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            @*                             注：如还未更新本学期“个人任课信息”，请点击【 <a class="btn btn-secondary btn-sm" onclick="updateClassInfo()"><i class="fa fa-default"></i>&nbsp;设置&nbsp;</a>】按钮，这将便捷你以下信息的填写！
                            *@
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <div class="ibox-title">
                <h5 id="iboxTitleName">实验课程</h5>
            </div>
            <div class="wrapper animated fadeInRight">
                <form id="form" class="form-horizontal m">
                    <div class="form-group row">
                        <label class="col-sm-3 control-label "></label>
                        <div class="col-sm-8">
                            <div id="temp001" style="height:30px;width:100%;"></div>
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">学年</label>
                        <div class="col-sm-8">
                            <div id="SchoolYearStart" col="SchoolYearStart"></div>
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">学期</label>
                        <div class="col-sm-8">
                            <div id="SchoolTerm" col="SchoolTerm"></div>
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">适用学科</label>
                        <div class="col-sm-8">
                            <div id="CourseId" col="CourseId"></div>
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">课程名称</label>
                        <div class="col-sm-8">
                            <input id="Name" col="Name" class="form-control" />
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">学生限额</label>
                        <div class="col-sm-8">
                            <input id="StudentNumLimit" col="StudentNumLimit" class="form-control" />
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">上课地点</label>
                        <div class="col-sm-8">
                            <input id="FunRoomName" col="FunRoomName" class="form-control" />
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">上课日期</label>
                        <div class="col-sm-8">
                            <input id="ClassTime" col="ClassTime" class="form-control" readonly />
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">上课时间</label>
                        <div class="col-sm-2">
                            <input id="BeginTime" class="form-control" readonly placeholder="开始时间" />
                        </div>
                        <div style="display:inline-block;float:left;">~</div>
                        <div class="col-sm-2">
                            <input id="EndTime" class="form-control" readonly placeholder="结束时间" />
                        </div>
                        <div class="col-sm-1"></div>
                    </div>

                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">实验清单</label>
                        <div class="col-sm-8">
                            <a id="btnAdd" class="btn btn-success" onclick="showAddListForm()"><i class="fa fa-plus"></i> 添加</a>
                            <span id="spanExperimentNumMsg">你还没添加实验</span>
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                    <div class="form-group row" id="isGridTable">
                        <label class="col-sm-1 control-label "></label>
                        <div class="col-sm-9">
                            <div class="col-sm-12 select-table table-striped">
                                <table id="gridTable" data-mobile-responsive="true"></table>
                            </div>
                            @*  <div class="container-div">
                            <div class="row">

                            </div>
                            </div> *@
                        </div>
                        <div class="col-sm-1"></div>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm hidden-xs  col-sm-8" id="toolbar" style="text-align: center;">
                <a id="btnAdd" class="btn btn-info" onclick="saveForm(1);"><i class="fa fa-edit"></i> 保存</a>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var id = ys.request("id");
    var Com_SchoolTermYear = 0;
    var Com_SchoolTerm = 1;
    var EntityJsonData = undefined;

    var CurrentSelectSchoolTerm = 0;
    var CurrentSelectCourseId = 0;
    var CourseListData = [];
    $(function () {
        $("#isGridTable").hide()
        $(".inputprompt").popover({
            trigger: 'hover',
            placement: 'left',
            html: true,
        });
        if (id > 0) {
            $("#iboxTitleName").text("实验课程编辑");
        } else {
            $("#iboxTitleName").text("实验课程添加");
        }
        initComboBox();
        getForm();
        initGrid("11111111", id);
    });

    function initComboBox() {
        // body...
        $('#SchoolYearStart').ysComboBox({
            class: 'form-control',
            key: 'id',
            value: 'name',
            onChange: function (argument) {
                var schootermArr = [];
                var schoolstartyear = $('#SchoolYearStart').ysComboBox('getValue');
                if (schoolstartyear > 0) {
                    if (schoolstartyear == Com_SchoolTermYear) {
                        var tempArr = ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString()));
                        if (tempArr != null && tempArr.length > 0) {
                            $.each(tempArr, function (index, item) {
                                if (item.Key >= Com_SchoolTerm) {
                                    schootermArr.push(item);
                                }
                            })
                        }
                    } else {
                        schootermArr = ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString()));
                    }
                    $("#SchoolTerm").ysComboBox({
                        data: schootermArr,
                        class: 'form-control'
                    });
                    if (schoolstartyear == Com_SchoolTermYear) {
                        $('#SchoolTerm').ysComboBox('setValue', Com_SchoolTerm);
                    } else if (EntityJsonData != undefined && EntityJsonData.SchoolTerm != undefined) {
                        $('#SchoolTerm').ysComboBox('setValue', EntityJsonData.SchoolTerm);
                    }
                }
            }
        });
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            class: 'form-control'
        });

        $('#CourseId').ysComboBox({
            class: 'form-control',
            key: 'DictionaryId',
            value: 'DicName',
        });
        //上课日期
        laydate.render({
            elem: '#ClassTime',
            format: 'yyyy-MM-dd',
            trigger: 'click',
            position: 'absolute',
        });
        // 时间插件
        laydate.render({
            elem: '#BeginTime',
            trigger: 'click',
            zIndex: 99999999,
            type: 'time',
            format: 'HH:mm',
            min: '05:00:00',
            max: '23:00:00',
        });
        laydate.render({
            elem: '#EndTime',
            trigger: 'click',
            zIndex: 99999999,
            type: 'time',
            format: 'HH:mm',
            min: '05:00:00',
            max: '23:00:00',
        });
    }

    function loadCourse(defaultValue) {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolUserCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data, defaultValue);
                } else {
                    bindCourseData({});
                }
            }
        });
    }

    function bindCourseData(data, defaultValue) {
        CourseListData = data;
        $('#CourseId').ysComboBox({
            class: 'form-control',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
        if (defaultValue > 0) {
            $('#CourseId').ysComboBox("setValue", defaultValue);
        }
    }

    function loadSchoolTermYear() {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined && obj.Data.SchoolTermStartYear > 0) {
                    var schoolYear = [];
                    Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                    Com_SchoolTerm = obj.Data.SchoolTerm;
                    var name = (obj.Data.SchoolTermStartYear + '').substr(2) + '~' + ((obj.Data.SchoolTermStartYear + 1) + '').substr(2);
                    schoolYear.push({ id: obj.Data.SchoolTermStartYear, name: name });

                    var nextYear = obj.Data.SchoolTermStartYear + 1;
                    var nextEndyear = nextYear + 1;
                    name = (nextYear + '').substr(2) + '~' + (nextEndyear + '').substr(2);
                    schoolYear.push({ id: nextYear, name: name });
                    $('#SchoolYearStart').ysComboBox({
                        data: schoolYear,
                        class: 'form-control',
                        key: 'id',
                        value: 'name'
                    });
                    if (EntityJsonData != undefined && EntityJsonData.SchoolYearStart != undefined && parseInt(EntityJsonData.SchoolYearStart) > 0) {
                        $("#SchoolYearStart").ysComboBox('setValue', EntityJsonData.SchoolYearStart);
                    } else {
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                } else {
                    var schoolYear = [];
                    var SchoolYearStart = new Date().getFullYear();
                    //根据8月25号，判断默认是上学期还是下学期。
                    Com_SchoolTerm = 1;
                    if (new Date() < new Date(SchoolYearStart + '-08-25 00:00:00')) {
                        Com_SchoolTerm = 2;
                        SchoolYearStart = SchoolYearStart - 1;
                    }
                    Com_SchoolTermYear = SchoolYearStart;
                    SchoolYearEnd = SchoolYearStart + 1;
                    var name = (SchoolYearStart + '').substr(2) + '~' + (SchoolYearEnd + '').substr(2);
                    schoolYear.push({ id: SchoolYearStart, name: name });

                    var nextYear = SchoolYearStart + 1;
                    var nextEndyear = SchoolYearEnd + 1;
                    name = (nextYear + '').substr(2) + '~' + (nextEndyear + '').substr(2);
                    schoolYear.push({ id: nextYear, name: name });
                    $('#SchoolYearStart').ysComboBox({
                        data: schoolYear,
                        class: 'form-control',
                        key: 'id',
                        value: 'name'
                    });
                    if (EntityJsonData != undefined && EntityJsonData.SchoolYearStart != undefined && parseInt(EntityJsonData.SchoolYearStart) > 0) {
                        $("#SchoolYearStart").ysComboBox('setValue', EntityJsonData.SchoolYearStart);
                    } else {
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                }
            }
        });
    }

    function getForm() {
        if (id > 0) {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/GetFormJson")' + '?id=' + id,
                type: 'get',
                async: false,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#form').setWebControls(obj.Data);
                        $("#ClassTime").val(ys.formatDate(obj.Data.ClassTime, "yyyy-MM-dd"));
                        loadSchoolTermYear();
                        CurrentSelectCourseId = obj.Data.CourseId;
                        loadCourse(obj.Data.CourseId);

                        EntityJsonData = obj.Data;
                        if (obj.Data.Num > 0) {
                            $("#spanExperimentNumMsg").text("已添加" + obj.Data.Num + "个实验");
                            // $("#isGridTable").show()
                        } else {
                            $("#spanExperimentNumMsg").text("你还没添加实验");
                            // $ ("#isGridTable").hide()
                        }
                        // $("#PlanName").val(obj.Data.PlanName);
                        if (obj.Data.SectionShow && obj.Data.SectionShow.indexOf('~')) {
                            var times = obj.Data.SectionShow.split('~');
                            $("#BeginTime").val(times[0]);
                            $("#EndTime").val(times[1]);
                        }
                    }
                }
            });
        } else {
            var defaultData = {};
            $('#form').setWebControls(defaultData);
            loadSchoolTermYear();
            loadCourse();
        }
    }
    //添加清单
    function showAddListForm() {
        if (id > 0) {
            //  var gotoListUrl='@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddDetailList")'+'?id='+id+'&v=0&e=1';
            // // createMenuAndCloseCurrent(gotoListUrl,"管理实验清单");
            //  createMenuItem(gotoListUrl, "管理实验清单");
            showAddDetailForm();
        } else {
            saveForm(2);
        }
    }
    //savetype 1:按钮直接保存  2：清单保存
    function saveForm(savetype) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            var errorMsg = '';
            if (!(parseInt(postData.SchoolYearStart) > 0)) {
                errorMsg += '请选择学年。<br/>';
            }
            if (!(parseInt(postData.SchoolTerm) > 0)) {
                errorMsg += '请选择学期。<br/>';
            }
            if (!(parseInt(postData.CourseId) > 0)) {
                errorMsg += '请选择课程。<br/>';
            }
            if (!(postData.Name != undefined && postData.Name.length > 0)) {
                errorMsg += '请填写课程名称 。<br/>';
            } else if (postData.Name.length > 50) {
                errorMsg += '填写的课程名称字符请控制在50以内 。<br/>';
            }
            if (!(parseInt(postData.StudentNumLimit) > 0)) {
                errorMsg += '请填写学生限额，必须大于0。<br/>';
            }
            var beginTime = $('#BeginTime').val();
            var endTime = $('#EndTime').val();
            if (beginTime == '' || endTime == '') {
                errorMsg += '请填写完成上课时间。<br/>';
            }
            if (beginTime >= endTime) {
                errorMsg += '请填写的上课时间开始时间必须小于结束时间。<br/>';
            }
            if (errorMsg != '') {
                ys.msgError('验证失败，请填写完成再提交！<br/>' + errorMsg);
                return;
            }
            postData.SectionShow = (beginTime + '~' + endTime)
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/SaveOutFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        id = obj.Data;
                        if (savetype == 1) {
                            //返回列表，添加实验课程
                            var backUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddList")';
                            createMenuAndCloseCurrent(backUrl, "添加实验课程");
                        } else {
                            //跳转清单管理页面。
                            // var gotoListUrl='@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddDetailList")'+'?id='+obj.Data+'&v=0&e=1';
                            // // createMenuAndCloseCurrent(gotoListUrl,"添加实验清单");
                            // createMenuItem(gotoListUrl, "添加实验清单");
                            $('#gridTable').bootstrapTable('destroy');
                            initGrid();
                            showAddDetailForm();
                        }
                        // ys.msgSuccess(obj.Message);
                        // parent.$('#gridTable').ysTable('refresh');
                        // parent.resetToolbarStatus();
                        // parent.layer.close(index);
                    } else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }

    //#region 添加实验清单

    function showAddDetailForm() {

        ys.openDialog({
            title: '添加',
            content: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddDetailForm")' + '?id=' + id,
            width: '868px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);

            }
        });
    }

    function deleteForm(id, experimentPublishId) {
        ys.confirm('确认要删除当前条数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/SaveOutDelDetailFormJson")' + '?Id=' + id + '&ExperimentPublishId=' + experimentPublishId,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }


    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    // function resetGrid() {
    //     //清空条件
    //     $('#GradeId').ysComboBox('setValue', -1);
    //     $('#CourseId').ysComboBox('setValue', -1);
    //     $('#ExperimentType').ysComboBox('setValue', -1);
    //     $('#Name').val('');
    //     $('#gridTable').ysTable('search');
    //     resetToolbarStatus();
    // }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/GetDetailPageListJson")' + '?ExperimentPublishId=' + id;
       
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: " Id ",
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                {
                    field: 'opt', title: '操作', width: 70, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<span class="btn btn-danger btn-xs" onclick="deleteForm(\'' + row.Id + '\',\'' + row.ExperimentPublishId + '\')"><i class="fa btn-delete fa-remove"></i>删除</span>');
                        return actions.join('');
                    }
                },
                // { checkbox: true, visible: true },
                { field: 'CourseName', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'GradeName', title: '适用年级', halign: 'center', valign: 'middle', align: 'center', width: 100 },
                { field: 'ExperimentTypeName', title: '实验类型', sortable: true, width: 80, halign: 'center', align: 'center' },
                { field: 'ExperimentName', title: '实验名称', sortable: true, width: 180, halign: 'center', valign: 'middle', align: 'left' },
                { field: 'GuidanceTeacherName', title: '指导老师', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'left' },
                { field: 'EquipmentNeed', title: '主要仪器和耗材', sortable: true, halign: 'center', align: 'left' }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.pageSize = 100;
                return queryString;
            },
            onLoadSuccess: function (obj) {
                if (obj.Data && obj.Data.length>0) {
                    $("#isGridTable").show();
                    $("#spanExperimentNumMsg").text("已添加" + obj.Total + "个实验");
                }else{
                    $("#isGridTable").hide();
                    $("#spanExperimentNumMsg").text("你还没添加实验");
                }
            },
        });
    }
    //#endregion
</script>