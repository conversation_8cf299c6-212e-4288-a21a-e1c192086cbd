﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: 100% !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input type="text" placeholder="名称" id="Name" col="Name" style="display: inline-block;width:180px;" value="" />
                    </li>
                    <li>
                        <input type="text" placeholder="单位" id="UnitName" col="UnitName" style="display: inline-block;width:180px;" value="" />
                    </li>
                    <li>
                        <input type="text" placeholder="备注" id="Remark" col="Remark" style="display: inline-block;width:180px;" value="" />
                    </li>
                    <li>
                        <div id="Statuz" col="Statuz" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 添加</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        $("#Statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), defaultName: '状态' });
        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/EquipmentNameConfig/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                 {
                    field: 'opt', title: '操作', halign: 'center', valign: 'middle', align: 'center', width: 240,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="statuzOnChange(\'' + row.Id + '\',2,\'禁用\')"><i class="fa fa-remove"></i>禁用</a>');
                        } else {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="statuzOnChange(\'' + row.Id + '\',1,\'启用\')"><i class="fa fa-check"></i>启用</a>');
                        }
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { field: 'Name', title: '设备名称', sortable: true, width: 200, halign: 'center', valign: 'middle', align: 'left' },
                { field: 'UnitName', title: '单位', sortable: true, width: 60, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Statuz', title: '状态', sortable: true, width: 60, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                { field: 'Remark', title: '备注', sortable: true, width: 300, halign: 'center', valign: 'middle', align: 'left' },
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#Statuz").ysComboBox('setValue', -1);
        $('#Name').val("");
        $('#UnitName').val("");
        $('#Remark').val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(id) { 
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/EquipmentStatisticsManage/EquipmentNameConfig/Form")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(id) {
        ys.confirm('确认要删除这条数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/EquipmentNameConfig/DeleteFormJson")' + '?ids=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }


    function statuzOnChange(Id, statuz, strStatuz) {
        ys.confirm("确认要" + strStatuz + "吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/EquipmentNameConfig/UpdateStatuz")' + '?id=' + Id + '&statuz=' + statuz,
                type: "post",
                error: ys.ajaxError,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(strStatuz + " 成功");
                        searchGrid();
                    }
                    else {
                        ys.msgError(strStatuz + " 失败!" + obj.Message);
                    }
                }
            });
        });
    }
</script>
