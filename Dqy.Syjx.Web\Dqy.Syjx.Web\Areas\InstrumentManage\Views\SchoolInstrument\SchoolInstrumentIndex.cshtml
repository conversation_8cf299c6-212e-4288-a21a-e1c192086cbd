﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
    .select-list li input{
        width: auto !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row" style="height:auto;">
       @* <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、导入的仪器需要设置适用学科，否则不能入库，可多选后点击【设置适用学科】；<br />
                            2、当下表中的多个仪器入室到同一个地点，可多选后点击【批量入室】；<br />
                            3、入室完成后，多选点击【提交入库】完成仪器入库。<br />
                            4、操作步骤：（第一步：设置适用学科；第二步：入室操作；第三步：入库操作）
                        </div>
                    </div>
                </div>
            </div>
        </div>*@
    </div>
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <span id="statuz" col="Statuz" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <span id="isSelfMade" col="IsSelfMade" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="display:inline-block;width:150px;" />
                    </li>
                    <li>
                        <div id="isShowAlreadyInputStorage" col="IsShowAlreadyInputStorage" style="display:inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnSetcourse" class="btn btn-success disabled" onclick="showSetCourseForm()"><i class="fa fa-cog"></i> 设置适用学科</a>
            <a id="btnBatchinput" class="btn btn-primary disabled" onclick="showInputRoomForm()"><i class="fa fa-legal"></i> 批量入室</a>
            <a id="btnEdit" class="btn btn-info disabled" onclick="showSaveForm()"><i class="fa fa-edit"></i> 修改录入信息</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="batchDel()"><i class="fa fa-remove"></i> 批量删除</a>
            <a id="btnInputstorage" class="btn btn-warning disabled" onclick="batchInputStorage()"><i class="fa fa-check"></i> 提交入库</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var SELECTED_IDS = ''; //选中的行Id
    $(function () {
        $('#isShowAlreadyInputStorage').ysCheckBox({
            data: [{ Key: 0, Value: '不显示已入库' }]
        });
        $('#isShowAlreadyInputStorage').ysCheckBox('setValue',0);

        $("#isSelfMade").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), defaultName: '自制教具' });

        loadCourse();
        loadStatuz();

        initGrid();

        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnSetcourse')) {
                $('#btnSetcourse').toggleClass('disabled', !ids.length);
            }
            if ($('#btnBatchinput')) {
                $('#btnBatchinput').toggleClass('disabled', !ids.length);
            }
            if ($('#btnInputstorage')) {
                $('#btnInputstorage').toggleClass('disabled', !ids.length);
            }
            if ($('#btnEdit')) {
                $('#btnEdit').toggleClass('disabled', ids.length != 1);
            }
        });

        
        

    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/SchoolInstrument/GetPageListJson")' + '?ListType=2';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                //{ 
                //    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80,
                //    formatter: function (value, row, index) {
                //        var html = value;
                //        if (row.ModelCode != "" && row.ModelCode != null) {
                //            html = row.ModelCode;
                //        }
                //        return html;
                //    }
                //},
                //{ field: 'ModelCode', title: '规格代码', halign: 'center', align: 'center', sortable: true, width: 80 },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt2,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Statuz == @InstrumentInputStatuzEnum.WaitInputRoom.ParseToInt()) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="inputRoomForm(this)" value="{0}" t="1"><i class="fa fa-save"></i>入室</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(this)" value="{0}"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                        }
                        else if (row.Statuz == @InstrumentInputStatuzEnum.WaitInputStorage.ParseToInt()) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="inputRoomForm(this)" value="{0}" t="2"><i class="fa fa-edit"></i>入室修改</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(this)" value="{0}"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                        }
                        else {
                            html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        }
                        return html;
                    }
                },
                { field: 'Course', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course },
                 {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        if (row.IsSelfMade == 1) {
                            html += Syjx.GetSelfMadeHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'Num', title: '数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },  //取入室数量
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'Price', title: '单价', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    }
                },
               
                {
                    field: 'FunRoom', title: '存放地', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.FunRoom,
                    formatter: function (value, row, index) {
                        if (row.Cupboard) {
                            value += '>' + row.Cupboard;
                        }
                        if (row.Floor) {
                            value += '>' + row.Floor;
                        }
                        return value;
                    }
                },
                {
                    field: 'Statuz', title: '状态', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Statuz,
                    formatter: function (value, row, index) {
                        return row.StatuzDesc;
                    }
                },
                //{
                //    field: 'IsSelfMade', title: '自制教具', halign: 'center', align: 'center', sortable: true, width: 50,
                //    formatter: function (value, row, index) {
                //        return value == 1 ? '是' : '否';
                //    }
                //},
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm() {
        var id = 0;
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (!ys.checkRowEdit(selectedRow)) {
            return;
        }
        else if (selectedRow[0].Statuz == @InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()) {
            ys.msgError('已入库仪器不可修改。');
            return;
        }
        else {
            id = selectedRow[0].Id;
            var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentForm")' + '?id=' + id;
            createMenuItem(url, "仪器修改");
        }
    }

    function showSetCourseForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            // if (selectedRow.length > 100){
            //     ys.msgWarning('最多只能选择100条数据设置');
            //     return;
            // }
            var ids = ys.getIds(selectedRow);
            SELECTED_IDS = ids;
            ys.openDialog({
                title: '设置适用学科',
                width: '850px',
                height: '400px',
                content: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchEditCourseForm")',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }
    }

    function showInputRoomForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
            SELECTED_IDS = ids;
            ys.openDialog({
                title: '批量入室',
                width: '800px',
                height: '400px',
                content: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchInputRoomForm")',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }
    }

    function inputRoomForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: $(obj).attr("t") == 1 ? '单条入室' : '入室修改',
            width: '800px',
            height: '400px',
            content: '@Url.Content("~/InstrumentManage/SchoolInstrument/SingleInputRoomForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要删除该数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/DeleteFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function lookForm(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/LookDetailForm")' + '?id=' + id;
        //createMenuItem(url, "仪器查看");
        ys.openDialog({
            title: "仪器查看",
            width: "800px",
            content: url,
            btn: ['关闭'],
            yes: function (index) {
                $.modal.close(index);
            },
        });

    }

    function batchInputStorage() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
            ys.confirm('确认要批量入库吗？', function () {
                ys.ajax({
                    url: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchInputStorage")',
                    type: 'post',
                    data: { Ids: ids },
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function batchDel() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
            ys.confirm('您确认要批量删除所选的数据吗？', function () {
                ys.ajax({
                    url: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchDeleteJson")',
                    type: 'post',
                    data: { Ids: ids },
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });
    }
    function loadStatuz() {
        $("#statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(InstrumentInputStatuzEnum).EnumToDictionaryString())), defaultName:'状态' });
    }

    function resetGrid() {
        //清空条件
        $('#courseId').ysComboBox('setValue', -1);
        $('#statuz').ysComboBox('setValue', -1);
        $("#isSelfMade").ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#isShowAlreadyInputStorage').ysCheckBox('setValue', 0);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>

<style type="text/css">
    inputp[name="isShowAlreadyInputStorage_checkbox"] {
        width: auto;
    }
</style>