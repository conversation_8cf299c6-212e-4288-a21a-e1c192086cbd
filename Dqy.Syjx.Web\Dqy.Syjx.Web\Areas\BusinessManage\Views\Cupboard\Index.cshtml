﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container { width: 100% !important; }
    .select-list li input{width:100% !important;}
</style>
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="DictionaryId1006A" col="DictionaryId1006A" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006B" col="DictionaryId1006B" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="RoomAttribute" col="RoomAttribute" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1005" col="DictionaryId1005" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SysDepartment" style="display: inline-block;"></div>
                        <input type="hidden" id="SysDepartmentId" col="SysDepartmentId" value="" />
                    </li>
                   @* <li>
                        <div id="SysUserId" col="SysUserId" style="display: inline-block;width:100px;"></div>
                    </li>*@
                    <li>
                        <div id="Address" style="display: inline-block;width:200px;"></div>
                        <input type="hidden" id="AddressId" col="AddressId" value="" />
                    </li>
                    <li>
                        <input type="text" placeholder="名称" id="Name" col="Name" style="display: inline-block;width:180px;" value="" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a id="btnSearch" class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showAddForm()"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showEditForm(0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        $("#DictionaryId1006A").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName:'一级分类',
            onChange: function () {
                var dicA = $("#DictionaryId1006A").ysComboBox("getValue");
                loadDictionaryId1006B(dicA);
            }
        });
        loadDictionaryId1006B(0);
        loadNature();
        loadSubject();
        loadSysDepartment();
        //loadSysUser(0);
        loadAddress();
        initGrid();
        
        
    });
    function loadDictionaryId1006B(dicA) {
        if (parseInt(dicA) > 0) {
            $("#DictionaryId1006B").ysComboBox({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5' + '&Pid=' + dicA,
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
            });
        } else {
            $("#DictionaryId1006B").ysComboBox({
                data: [],
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
            });
        }
    }
    function loadNature() {
        $("#RoomAttribute").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1009',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName:'属性'
        });
    }
    function loadSubject() {
        $("#DictionaryId1005").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1005&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName:'适用学科',
        });
    }
    function loadSysDepartment() {
        $('#SysDepartment').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            defaultName:'管理部门',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    //if (typeof (treeNode) == "string") {
                    //    treeNode = JSON.parse(treeNode);
                    //}
                    /*loadSysUser(treeNode.id);*/
                    $("#SysDepartmentId").val(treeNode.id);
                }
            }
        });
        $('#SysDepartment').ysComboBoxTree('setValue', -1);
    }
    function loadSysUser(departmentid) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetUserListJson")' + '?DepartmentId=' + departmentid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#SysUserId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'RealName',
                        defaultName: '管理人'
                    });
                    if (obj.Data.length == 1) {
                        $("#SysUserId").ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                } else {
                    $('#SysUserId').ysComboBox({ data: [], key: 'Id', value: 'RealName', defaultName: '管理人' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    function loadAddress() {
        $('#Address').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Address/GetZtreeListJson")',
            class: 'form-control',
            defaultName: '地点',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    $("#AddressId").val(treeNode.id);
                }
            }
        });
        $('#Address').ysComboBoxTree('setValue', -1);
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/BusinessManage/Cupboard/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sortName: 'FunRoomId ASC,Sort ASC',
            sortOrder: 'ASC',
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'Name', title: '实验（专用）室名称', sortable: true, width: 260, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            switch (row.NatureName) {
                                case '@FunNatureTypeEnum.Room.GetDescription()':
                                    html += Syjx.GetCircleRoomHtml();
                                    break;
                                case '@FunNatureTypeEnum.ZhuanRoom.GetDescription()':
                                    html += Syjx.GetCircleZhuanRoomHtml();
                                    break;
                                case '@FunNatureTypeEnum.FuRoom.GetDescription()':
                                    html += Syjx.GetCircleFuRoomHtml();
                                    break;
                            }
                            html += row.Name;

                        }
                        return html;
                    }
                },
                {
                    field: 'CupboardName', title: '橱柜名称', sortable: true, width: 200, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (value != undefined && value.length > 0) {
                            html = value;
                        }
                        return html;
                    }
                },
                { field: 'Sort', title: '排序', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'DepartmentName', title: '管理部门', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'AddressId', title: '地点', sortable: true, width: 160, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.HouseName != undefined && row.HouseName.length > 0) {
                            html = (row.HouseName + "(" + row.RoomName + ")");
                        } else if (row.RoomName != undefined && row.RoomName.length > 0) { 
                            html = row.RoomName;
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }
    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#DictionaryId1006A").ysComboBox('setValue',-1);
        loadDictionaryId1006B(0);
        $("#RoomAttribute").ysComboBox('setValue', -1);
        $("#DictionaryId1005").ysComboBox('setValue', -1);
        $('#SysDepartment').ysComboBoxTree('setValue', -1);
        $('#Address').ysComboBoxTree('setValue', -1);
        $('#Name').val("");
        //$("#SysUserId").ysComboBox('setValue',-1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showAddForm() {
        ys.openDialog({
            title: '添加',
            content: '@Url.Content("~/BusinessManage/Cupboard/AddForm")',
            width: '768px',
            height: '360px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function showEditForm(id) {
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError('每次只能修改一条数据，请选择一条数据！');
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: '编辑',
            content: '@Url.Content("~/BusinessManage/Cupboard/Form")' + '?id=' + id,
            width: '768px',
            height: '360px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm(id) {
        var ids = '';
        var msg = '';
        if (id != 0) {
            ids = id;
            msg = '确认要删除该条数据吗？';
        } else {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                ids = ys.getIds(selectedRow);
                msg = ('确认要删除选中的' + selectedRow.length + '条数据吗？');
            } else {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
        }
        ys.confirm(msg, function () {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/Cupboard/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>
