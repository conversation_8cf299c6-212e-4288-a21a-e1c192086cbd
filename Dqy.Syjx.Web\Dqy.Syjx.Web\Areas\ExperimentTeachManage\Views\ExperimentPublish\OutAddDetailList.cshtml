﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="CourseId" col="CourseId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="GradeId" col="GradeId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="ExperimentType" col="ExperimentType" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" type="text" placeholder="实验名称" style="display: inline-block;width:260px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 添加</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">
    var id = ys.request("id");//添加实验课程Id
    var Com_IsLook = ys.request("v");//验证是否是查看
    var Com_IsEdit = ys.request("e");//是否是编辑页面跳转过来的。
    $(function () {
        loadGrade();
        loadCourse();
        loadExperimentType();
        if (Com_IsLook == 1) {
            $("#toolbar").hide();
        }else{
            Com_IsLook=0;
        }
        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/GetDetailPageListJson")' + '?ExperimentPublishId=' + id;
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: " Id ",
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                {
                    field: 'opt', title: '操作', width: 70, halign: 'center', valign: 'middle', align: 'center', visible: (Com_IsLook != 1),
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<span class="btn btn-danger btn-xs" onclick="deleteForm(\'' + row.Id + '\',\'' + row.ExperimentPublishId + '\')"><i class="fa btn-delete fa-remove"></i>删除</span>');
                        return actions.join('');
                    }
                },
                // { checkbox: true, visible: true },
                { field: 'CourseName', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'GradeName', title: '适用年级', halign: 'center', valign: 'middle', align: 'center', width: 100 },
                { field: 'ExperimentTypeName', title: '实验类型', sortable: true, width: 80, halign: 'center', align: 'center' },
                { field: 'ExperimentName', title: '实验名称', sortable: true, width: 180, halign: 'center', valign: 'middle', align: 'left' },
                { field: 'GuidanceTeacherName', title: '指导老师', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'left' },
                { field: 'EquipmentNeed', title: '主要仪器和耗材', sortable: true, halign: 'center', align: 'left' }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $('#GradeId').ysComboBox('setValue', -1);
        $('#CourseId').ysComboBox('setValue', -1);
        $('#ExperimentType').ysComboBox('setValue', -1);
        $('#Name').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(id) {
        ys.openDialog({
            title: '添加',
            content: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/OutAddDetailForm")' + '?id=' + id,
            width: '868px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(id, experimentPublishId) {
        ys.confirm('确认要删除当前条数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentPublish/SaveOutDelDetailFormJson")' + '?Id=' + id + '&ExperimentPublishId=' + experimentPublishId,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolGradeListByPidJson")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '适用年级',
        });
    }
    function loadCourse() {
        $('#CourseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolCourseListJson")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '适用学科'
        });
    }
    function loadExperimentType() {
        $('#ExperimentType').ysComboBox({
            url: '@Url.Content("~/SystemManage/DataDictDetail/GetSortListJson")' + '?TypeCode=OET101001',
            key: 'DictKey',
            value: 'DictValue',
            defaultName: '实验类型',
        });
    }
</script>
