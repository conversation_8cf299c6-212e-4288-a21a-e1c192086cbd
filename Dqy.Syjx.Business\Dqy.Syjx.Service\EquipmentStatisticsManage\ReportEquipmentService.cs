﻿using System;
using System.Linq;
using System.Text;
using System.Data.Common;
using System.Linq.Expressions;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Data;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Entity.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Param.EquipmentStatisticsManage;

namespace Dqy.Syjx.Service.EquipmentStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-01-29 10:55
    /// 描 述：学校上报科技信息服务类
    /// </summary>
    public class ReportEquipmentService :  RepositoryFactory
    {
        #region 获取数据
        public async Task<List<ReportEquipmentEntity>> GetList(ReportEquipmentListParam param)
        {
            var expression = ListFilter(param);
            var list = await this.BaseRepository().FindList(expression);
            return list.ToList();
        }

        public async Task<List<ReportEquipmentEntity>> GetPageList(ReportEquipmentListParam param, Pagination pagination)
        {
            var expression = ListFilter(param);
            var list= await this.BaseRepository().FindList(expression, pagination);
            return list.ToList();
        }

        public async Task<ReportEquipmentEntity> GetEntity(long id)
        {
            return await this.BaseRepository().FindEntity<ReportEquipmentEntity>(id);
        }

        public async Task<List<ReportEquipmentEntity>> GetPageList(ReportEquipmentParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ReportEquipmentEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }

        public async Task<List<ReportEquipmentEntity>> GetAllUnitPageList(ReportEquipmentParam param, Pagination pagination)
        {
            StringBuilder strSql = new StringBuilder();
            List<DbParameter> filter = ListAllUnitFilter(param, strSql);
            var list = await this.BaseRepository().FindList<ReportEquipmentEntity>(strSql.ToString(), filter.ToArray(), pagination);
            return list.ToList();
        }
        #endregion

        #region 提交数据
        public async Task SaveForm(ReportEquipmentEntity entity)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await this.BaseRepository().Insert(entity);
            }
            else
            {
                await entity.Modify();
                await this.BaseRepository().Update(entity);
            }
        }

        public async Task SaveTransForm(ReportEquipmentEntity entity ,Repository db)
        {
            if (entity.Id.IsNullOrZero())
            {
                await entity.Create();
                await db.Insert(entity);
            }
            else
            {
                await entity.Modify();
                await db.Update(entity);
            }
        }

        public async Task DeleteForm(string ids)
        {
            ids = StringFilter.ValidateAndCleanIds(ids);
            if(ids == null)
                throw new ArgumentException("无效的参数: 只允许数字和逗号组合");
            string strSql;
            if (ids.IndexOf(',') != -1)
            {
               strSql = $" update zb_ReportEquipment set BaseIsDelete = 1 where Id in ({ids}) ";
            }
            else
            {
               strSql = $"update zb_ReportEquipment set BaseIsDelete = 1 where id = {ids}";
            }
            await this.BaseRepository().ExecuteBySql(strSql);
        }

        public async Task DeleteTransForm(long id, Repository db)
        {
            string strSql = $"update zb_ReportEquipment set BaseIsDelete = 1 where id = {id}";
            await db.ExecuteBySql(strSql);
        }
        #endregion

        #region 私有方法
        /// <summary>
        ///
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListFilter(ReportEquipmentParam param, StringBuilder strSql, string funSql = "*")
        {
            strSql.Append($@"  SELECT {funSql} From (
                               SELECT RE.Id,U.Sort,U.Id AS SchoolId,U.Code AS SchoolCode,U.Name AS SchoolName,RE.Name,RE.Brand,RE.Num,
	                                   RE.Modelz,RE.UnitName,RE.Remark ,ur.UnitId AS CountyId
                                FROM zb_ReportEquipment AS RE
                                INNER JOIN zb_Report AS R ON RE.ReportId = R.Id AND R.BaseIsDelete = 0
                                INNER JOIN up_Unit AS U ON R.SchoolId = U.Id AND U.BaseIsDelete = 0
                                INNER JOIN up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND R.SchoolId = ur.ExtensionObjId
                                WHERE R.IsReport = 1 AND R.IsCurrent = 1 AND RE.BaseIsDelete = 0
                        ) as T WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            return parameter;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="param"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        private List<DbParameter> ListAllUnitFilter(ReportEquipmentParam param, StringBuilder strSql, string funSql = "*")
        {
            strSql.Append($@"  SELECT {funSql} From (
                               SELECT RE.Id,U.Sort,U.Id AS SchoolId,U.Code AS SchoolCode,U.Name AS SchoolName,RE.Name,RE.Brand,RE.Num,
                                RE.Modelz,RE.UnitName,RE.Remark ,ur.UnitId AS CountyId
                                 FROM up_Unit AS U
                                 INNER JOIN up_UnitRelation AS ur ON ur.BaseIsDelete = 0 AND U.Id = ur.ExtensionObjId
                                 LEFT JOIN zb_Report AS R ON R.BaseIsDelete = 0 AND U.Id = R.SchoolId AND R.IsReport = 1 AND R.IsCurrent = 1
                                 LEFT JOIN zb_ReportEquipment AS RE ON  RE.BaseIsDelete = 0 AND R.Id = RE.ReportId
                                 WHERE  U.BaseIsDelete = 0
                        ) as T WHERE 1 = 1 ");

            var parameter = new List<DbParameter>();
            if (param != null)
            {
                if (param.SchoolId > 0)
                {
                    strSql.Append(" AND SchoolId = @SchoolId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@SchoolId", param.SchoolId));
                }
                if (param.CountyId > 0)
                {
                    strSql.Append(" AND CountyId = @CountyId ");
                    parameter.Add(DbParameterExtension.CreateDbParameter("@CountyId", param.CountyId));
                }
            }
            return parameter;
        }


        public async Task<ReportEquipmentEntity> GetEquipmentNumSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListFilter(param, sql, " '总计：' AS SchoolName,ISNULL(SUM(Num) ,0) AS Num");
            var list = await this.BaseRepository().FindList<ReportEquipmentEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        public async Task<ReportEquipmentEntity> GetAllUnitEquipmentNumSum(ReportEquipmentParam param)
        {
            StringBuilder sql = new StringBuilder();
            var expression = ListAllUnitFilter(param, sql, " '总计：' AS SchoolName,ISNULL(SUM(Num) ,0) AS Num");
            var list = await this.BaseRepository().FindList<ReportEquipmentEntity>(sql.ToString(), expression.ToArray());
            return list.FirstOrDefault();
        }

        private Expression<Func<ReportEquipmentEntity, bool>> ListFilter(ReportEquipmentListParam param)
        {
            var expression = LinqExtensions.True<ReportEquipmentEntity>();
            expression = expression.And(t => t.BaseIsDelete.Value == 0);
            if (param != null)
            {
                if (!string.IsNullOrEmpty(param.Ids))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.Ids, ',');
                    expression = expression.And(t => idArr.Contains(t.Id.Value));
                }
                if (param.ReportId > 0)
                {
                    expression = expression.And(t => t.ReportId == param.ReportId);
                }
                if (!string.IsNullOrEmpty(param.EquipmentNameConfigIds))
                {
                    long[] idArr = TextHelper.SplitToArray<long>(param.EquipmentNameConfigIds, ',');
                    expression = expression.And(t => idArr.Contains(t.EquipmentNameConfigId));
                }
            }
            return expression;
        }
        #endregion
    }
}
