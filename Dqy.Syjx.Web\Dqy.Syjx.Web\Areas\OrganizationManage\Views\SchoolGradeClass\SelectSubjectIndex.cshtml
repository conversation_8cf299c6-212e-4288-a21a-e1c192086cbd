﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
</style>
<div class="container-div">
    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、此功能是指高中选科分班后，班级所选的物理、化学和生物科目；<br />
                            2、请在高中选科分班后，才进行设置，否则会影响实验开出率；
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="GradeId" col="GradeId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="SubjectId" col="SubjectId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        @Html.Hidden("IsGraduate", 0, new { col = "IsGraduate" })
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div id="toolbar" class="btn-group d-flex" role="group"> 
            <span id="btnEdit" class="btn btn-primary" onclick="showEditForm(this)"><i class="fa fa-lock"></i>&nbsp;&nbsp;开启编辑</span>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>


<script type="text/javascript">
    var Com_SubjectList = [];
    $(function () {
        loadComboBox();
    });

    function loadComboBox() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + "?TypeCode="+"@DicTypeCodeEnum.Grade.ParseToInt()"+"&Pid=" + "@SchoolStageEnum.GaoZhong.ParseToInt()",
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级'
        });
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + "?TypeCode=" + "@DicTypeCodeEnum.Course.ParseToInt()" + "&Nature=1" + "&Pid=" + "@SchoolStageEnum.GaoZhong.ParseToInt()",
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#SubjectId').ysComboBox({
                        //url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + "?TypeCode=" + "@DicTypeCodeEnum.Course.ParseToInt()" + "&Nature=1" + "&Pid=" + "@SchoolStageEnum.GaoZhong.ParseToInt()",
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        defaultName: '所选科目'
                    });
                    Com_SubjectList = obj.Data;
                    initGrid();
                }  
            }
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetPageListJson")' + '?SchoolStage=' + '@SchoolStageEnum.GaoZhong.ParseToInt()'+'&IsTotal=0';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'GradeId ASC,ClassId ASC',
            sortOrder: 'ASC',
            pageSize: 25,
            columns: [
                {
                    field: 'StartYear', title: '入学年份', width: 80, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'GradeName', title: '年级', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'ClassDesc', title: '班级', width: 160, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return "";//"<b>" + value + "（个）</b>";
                    }
                },
                {
                    field: 'SelectSubject', title: '所选科目', width: 200, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.Id) {
                            html = '';
                            html += $.Format('<div id="divSelectSubject_{0}" class="selectsubject" value="{1}" gradeclassid="{0}">', row.Id, value);
                            html += getSubjectHtml(value, row.Id);
                            html += '</div>';
                        }
                        return html;
                    }, cellStyle: function (value, row, index) {
                        return { css: { 'padding': '0px', 'height': '36px' } };
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.Statuz = @StatusEnum.Yes.ParseToInt();
                return queryString;
            },
            onLoadSuccess: function () {
            },
            onPostBody:function(){
                $('input[name="class_checkbox"]').change(function () {
                    var classstr = $("#btnEdit").attr('class');
                    if (classstr && classstr.indexOf('btn-danger') > -1) {
                        if ($(this).prop("checked")) {
                            $(this).parent(".icheckbox-blue").addClass("checked");
                        } else {
                            $(this).parent(".icheckbox-blue").removeClass("checked");
                        }
                        var gradeclassid = $(this).attr("gradeclassid");
                        var subjectid = $(this).attr("value");
                        var subjectArr = $('input[gradeclassid="' + gradeclassid + '"]:checked').map(function () { return this.value }).get();

                        setSaveSubject(gradeclassid, subjectArr, subjectid);
                    } else {
                        if ($(this).prop("checked")) {
                            $(this).prop("checked", false);
                        } else {
                            $(this).prop("checked", true);
                        }
                        layer.msg('请先开启编辑，在设置班级所选学科。', { icon: 2, time: 3000, btn: ['关闭'], yes: function () { layer.closeAll(); }, area: ['400px'] });
                    }
                });
                // $(".checkboxprompt").popover({
                //     trigger: 'hover',
                //     placement: 'top',
                //     html: true
                // });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#GradeId").ysComboBox("setValue", -1);
        $("#SubjectId").ysComboBox("setValue", -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showEditForm(obj){
        var classstr = $(obj).attr('class');
        if (classstr && classstr.indexOf('btn-danger') > -1) {
            $(obj).removeClass('btn-danger');
            $(obj).addClass('btn-primary');
            $(obj).html('<i class="fa fa-lock"></i>&nbsp;&nbsp;开启编辑');
        } else {
            $(obj).removeClass('btn-primary');
            $(obj).addClass('btn-danger');
            $(obj).html('<i class="fa fa-unlock"></i>&nbsp;&nbsp;关闭编辑');
        }
    }

    function setSaveSubject(gradeclassid, subjectArr,subjectid) {
        var postData = { Id: gradeclassid, SubjectList: subjectArr, SubjectId: subjectid };
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/SaveSubjectJson")',
            type: 'post',
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
     
    function getSubjectHtml(idsArr,classid){
        var html='';
        if(Com_SubjectList!=undefined && Com_SubjectList.length > 0){
            var data=Com_SubjectList;
            for (var i = 0; i < data.length; i++) {
                html += $.Format('<span class="titleprompt checkboxprompt" aria-hidden="true" data-container="body" data-toggle="tooltip" data-content="{0}">', data[i].DicName);
                html += '<label class="check-box">';
                if (idsArr && idsArr.indexOf(data[i].DictionaryId) > -1) {
                    html += $.Format('<div class="icheckbox-blue checked"><input name="class_checkbox" type="checkbox" value="{0}" gradeclassid="{1}" style="position: absolute; opacity: 0;" checked="checked"></div>', data[i].DictionaryId, classid);
                } else {
                    html += $.Format('<div class="icheckbox-blue"><input name="class_checkbox" type="checkbox" value="{0}" gradeclassid="{1}" style="position: absolute; opacity: 0;" class=""></div>', data[i].DictionaryId,classid);
                }
                html += $.Format('{0}</label>', data[i].DicName);
                html += '</span>';
            }
        }
        return html;
    }
</script>
