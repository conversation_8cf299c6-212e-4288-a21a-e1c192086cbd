﻿@{ Layout = "~/Views/Shared/_Index.cshtml"; }
<style type="text/css">
    .select2-container { width: 100% !important; }
</style>
@{
    OperatorInfo operatorInfo = ViewBag.OperatorInfo;
}

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    @await Html.PartialAsync("/Areas/OrganizationManage/Shared/SchoolNatureIndexPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    @await Html.PartialAsync("/Areas/OrganizationManage/Shared/SchoolPropIndexPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    <li>
                        <input Id="Name" col="Name" type="text" placeholder="单位名称" style="display: inline-block;width:200px;"/>
                    </li>
                    <li>
                        @await Html.PartialAsync("/Areas/OrganizationManage/Shared/EnableDisableIndexPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" role="group">
            @if (operatorInfo.IsThirdLogin == 0)
            {
                <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,'0')"><i class="fa fa-plus"></i> 新增</a>
            }
            <a id="btnEdit" class="btn btn-primary" onclick="showSaveForm(false,'0')"><i class="fa fa-edit"></i> 修改</a>
            @*<a id="btnDelete" class="btn btn-danger" onclick="deleteForm('0')"><i class="fa fa-remove"></i> 删除</a>*@
            @if (operatorInfo.IsThirdLogin == 0)
            {
                <a class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a>
            }
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList?IsHasKindergarten=true")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Sort',
            sortOrder: 'ASC',
            columns: [
                { checkbox: true, visible: true }, 
                {
                    field: 'opt1', title: '操作', width: 220, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var actions = [];
                        var html = '';
                        if (row.Id != undefined) {
                            actions.push($.Format('<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'{0}\');"><i class="fa fa-edit"></i>修改</a>', row.Id));
                            actions.push($.Format('<a class="btn btn-primary btn-xs" href="#" onclick="showSortForm(\'{0}\');"><i class="fa fa-edit"></i>排序</a>', row.Id));
                            if (row.Statuz == 1) {
                                actions.push($.Format('<a class="btn btn-warning btn-xs" href="#" onclick="ShowIsEnableForm(2,\'{0}\');"><i class="fa fa-check"></i>禁用</a>', row.Id));
                            } else {
                                actions.push($.Format('<a class="btn btn-primary btn-xs" href="#" onclick="ShowIsEnableForm(1,\'{0}\');"><i class="fa fa-check"></i>启用</a>', row.Id));
                            }
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'SchoolNature', title: '单位性质', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id!=undefined) {
                            html = '--';
                            if (row.SchoolNature == 1) {
                                html = '公办';
                            } else if (row.SchoolNature == 2) {
                                html = '民办';
                            }
                        }
                        return html;
                    }
                },
                { field: 'SchoolPropName', title: '单位属性', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'Name', title: '单位名称', sortable: true, halign: 'center', valign: 'middle' },
                { field: 'Sort', title: '排序号', sortable: true, width: 70, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Statuz', title: '单位状态', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html = '--';
                            if (value > 0) {
                                html = top.getDataDictValue('EnableStatuz', value);
                            }
                        }
                        return html;
                    }
                },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $('#SchoolNature').ysComboBox('setValue', -1);
        $('#SchoolProp').ysComboBox('setValue', -1);
        $("#Statuz").ysComboBox('setValue', -1);
        $("#Name").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,editid) {
        var id = 0;
        if (editid == '0' && !bAdd) {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        } else {
            id = editid;
        }
        ys.openDialog({
            title: id > 0 ? "修改单位信息" : "添加单位信息",
            content: '@Url.Content("~/OrganizationManage/Unit/UnitForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSortForm(editid) {
        var id = 0;
        if (editid == '0') {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        } else {
            id = editid;
        }
        ys.openDialog({
            title: "设置单位排序信息",
            content: '@Url.Content("~/OrganizationManage/Unit/UnitSortForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(editid) {
        var ids = editid;
        var message = '确认要删除该数据吗？';
        if (editid == '0') {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (ys.checkRowDelete(selectedRow)) {
                message = ("确认要删除选中的" + selectedRow.length + "条数据吗？");
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/DeleteFormJson")' + '?ids=' + ids,
                type: "post",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function ShowIsEnableForm(IsEnable, editid) {
        var ids = editid + '';
        var message = '确定禁用';
        if (IsEnable==1) {
            message = '确定启用';
        }
        if (editid != '0') {
            message += "该单位信息？";
        } else {
            var selectedRow = $("#gridTable").bootstrapTable("getSelections");
            if (ys.checkRowDelete(selectedRow)) {
                message += ("选中的" + selectedRow.length + "条数据吗？");
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgError("请选择要启用禁用的数据？");
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/SaveStatuzFormJson")' + '?ids=' + ids + '&isenable=' + IsEnable,
                type: "post",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    function importForm() {
        ys.openDialog({
            title: "导入地址数据",
            content: '@Url.Content("~/OrganizationManage/Unit/UnitChildrenImport")',
            height: "400px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
