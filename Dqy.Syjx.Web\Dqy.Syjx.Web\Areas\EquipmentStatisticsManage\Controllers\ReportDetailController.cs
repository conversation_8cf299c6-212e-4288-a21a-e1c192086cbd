﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Entity;
using Dqy.Syjx.Model;
using Dqy.Syjx.Web.Controllers;
using Dqy.Syjx.Entity.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Input.EquipmentStatisticsManage;
using Dqy.Syjx.Business.EquipmentStatisticsManage;
using Dqy.Syjx.Model.Param.EquipmentStatisticsManage;
using Dqy.Syjx.Business.ExperimentTeachManage;
using Dqy.Syjx.Entity.ExperimentTeachManage;
using Dqy.Syjx.Model.Param.ExperimentTeachManage;
using Dqy.Syjx.Web.Code;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using System.IO;
using Dqy.Syjx.Util.Tools;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.SystemManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Model.Input.SystemManage;
using Dqy.Syjx.Business.SystemManage;
using System.Text.Json.Nodes;
using Dqy.Syjx.Util.Extension;
using NetTopologySuite.Index.HPRtree;
using Dqy.Syjx.Enum.BusinessManage;
using NetTaste;
using Dqy.Syjx.Enum;

namespace Dqy.Syjx.Web.Areas.EquipmentStatisticsManage.Controllers
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-01-29 10:52
    /// 描 述：学校上报详情控制器类
    /// </summary>
    [Area("EquipmentStatisticsManage")]
    public class ReportDetailController :  BaseController
    {
        private ReportDetailBLL reportDetailBLL = new ReportDetailBLL();
        private StatisticsBackupsBLL statisticsBackupsbll = new StatisticsBackupsBLL();

        #region 视图功能
        [AuthorizeFilter("equipmentstatistics:reportdetail:view")]
        public ActionResult ReportDetailIndex()
        {
            return View();
        }

        public ActionResult ReportDetailForm()
        {
            return View();
        }


        /// <summary>
        /// 装备投入经费
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:view")]
        public ActionResult EquipmentFundsIndex()
        {
            return View();
        }

        /// <summary>
        /// 信息技术
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:view")]
        public ActionResult EquipmentInfoIndex()
        {
            return View();
        }

        /// <summary>
        /// 图书
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:view")]
        public ActionResult EquipmentBookIndex()
        {
            return View();
        }

        /// <summary>
        /// 体育场所
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:view")]
        public ActionResult EquipmentSportIndex()
        {
            return View();
        }

        /// <summary>
        /// 科创设备
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:view")]
        public ActionResult EquipmentTechnologyIndex()
        {
            return View();
        }

        /// <summary>
        /// 幼儿园专用室
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:view")]
        public ActionResult EquipmentKindergartenIndex()
        {
            return View();
        }

        /// <summary>
        /// 实验室管理员
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:reportdetail:view")]
        public ActionResult LabManagerList()
        {
            return View();
        }

        /// <summary>
        /// 学校数据表
        /// </summary>
        /// <returns></returns>
        [AuthorizeFilter("equipmentstatistics:reportdetail:view")]
        public ActionResult SchoolInfoDetail()
        {
            return View();
        }
        #endregion

        #region 获取数据
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:reportdetail:search")]
        public async Task<ActionResult> GetListJson(ReportDetailListParam param)
        {
            TData<List<ReportDetailEntity>> obj = await reportDetailBLL.GetList(param);
            return Json(obj);
        }

        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:reportdetail:search")]
        public async Task<ActionResult> GetPageListJson(ReportDetailListParam param, Pagination pagination)
        {
            TData<List<ReportDetailEntity>> obj = await reportDetailBLL.GetPageList(param, pagination);
            return Json(obj);
        }

        [HttpGet]
        public async Task<ActionResult> GetFormJson(long id)
        {
            TData<ReportDetailEntity> obj = await reportDetailBLL.GetEntity(id);
            return Json(obj);
        }


        /// <summary>
        /// 装备投入经费-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:search")]
        public async Task<ActionResult> GetFundsPageListJson(ReportEquipmentParam param, Pagination pagination)
        {
            if (param.BackupYear == DateTime.Now.Year)//判断年度不是当前年
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                if (operatorinfo != null && operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId ?? 0;
                }
                TData<List<ReportDetailEntity>> obj = await reportDetailBLL.GetAllUnitFundsPageList(param, pagination);
                return Json(obj);
            }
            else
            {
                param.PageCode = BackupsPageCodeEnum.InputFunds.ParseToInt().ToString();
                TData<List<object>> obj = await statisticsBackupsbll.GetZbAllUnitPageList(param, pagination);
                return Json(obj);
            }
        }

        /// <summary>
        /// 信息技术-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:search")]
        public async Task<ActionResult> GetInfoPageListJson(ReportEquipmentParam param, Pagination pagination)
        {
            if (param.BackupYear == DateTime.Now.Year)//判断年度不是当前年
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                if (operatorinfo != null && operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId ?? 0;
                }
                TData<List<ReportDetailEntity>> obj = await reportDetailBLL.GetAllUnitInfoPageList(param, pagination);
                return Json(obj);
            }
            else
            {
                param.PageCode = BackupsPageCodeEnum.InfelTechnology.ParseToInt().ToString();
                TData<List<object>> obj = await statisticsBackupsbll.GetZbAllUnitPageList(param, pagination);
                return Json(obj);
            }
        }

        /// <summary>
        /// 图书-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:search")]
        public async Task<ActionResult> GetBookPageListJson(ReportEquipmentParam param, Pagination pagination)
        {
            if (param.BackupYear == DateTime.Now.Year)//判断年度不是当前年
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                if (operatorinfo != null && operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId ?? 0;
                }
                TData<List<ReportDetailEntity>> obj = await reportDetailBLL.GetAllUnitBookPageList(param, pagination);
                return Json(obj);
            }
            else
            {
                param.PageCode = BackupsPageCodeEnum.Book.ParseToInt().ToString();
                TData<List<object>> obj = await statisticsBackupsbll.GetZbAllUnitPageList(param, pagination);
                return Json(obj);
            }
        }

        /// <summary>
        /// 体育场所-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:search")]
        public async Task<ActionResult> GetSportPageListJson(ReportEquipmentParam param, Pagination pagination)
        {
            TData<List<object>> obj = new TData<List<object>>();
            if (param.BackupYear == DateTime.Now.Year)//判断年度不是当前年
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                if (operatorinfo != null && operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId ?? 0;
                }
                obj = await reportDetailBLL.GetAllUnitSportPageList(param, pagination);
            }
            else
            {
                param.PageCode = BackupsPageCodeEnum.ZhunFunroom.ParseToInt().ToString();
                obj = await statisticsBackupsbll.GetZbAllUnitPageList(param, pagination);
            }
            return Json(obj);
        }

        /// <summary>
        /// 科创设备-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:search")]
        public async Task<ActionResult> GetEquipmentPageListJson(ReportEquipmentParam param, Pagination pagination)
        {
            if (param.BackupYear == DateTime.Now.Year)//判断年度不是当前年
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                if (operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId ?? 0;
                }
                TData<List<ReportEquipmentEntity>> obj = await reportDetailBLL.GetAllUnitEquipmentPageList(param, pagination);
                return Json(obj);
            }
            else
            {
                param.PageCode = BackupsPageCodeEnum.StiEquipment.ParseToInt().ToString();
                TData<List<object>> obj = await statisticsBackupsbll.GetZbAllUnitPageList(param, pagination);
                return Json(obj);
            }
       
        }

        /// <summary>
        /// 幼儿园专用室-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:fundsreportdetail:search")]
        public async Task<ActionResult> GetKindergartenPageListJson(ReportEquipmentParam param, Pagination pagination)
        {
            TData<List<ReportSpecialRoomEntity>> obj = await reportDetailBLL.GetAllUnitKindergartenPageList(param, pagination);
            return Json(obj);
        }
        
        [HttpPost]
        public async Task<ActionResult> GetDictionaryListJson(List<int> dicids)
        {
            var obj = await reportDetailBLL.GetDictionaryList(dicids);
            return Json(obj);
        }

        /// <summary>
        /// 实验室管理员-区县端
        /// </summary>
        /// <param name="param"></param>
        /// <param name="pagination"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:reportdetail:search")]
        public async Task<ActionResult> GetLabManagerListJson(ReportEquipmentParam param, Pagination pagination)
        {
            TData<List<object>> obj = new TData<List<object>>();
            if (param.BackupYear != DateTime.Now.Year)//判断年度不是当前年
            {
                param.PageCode = BackupsPageCodeEnum.LabManager.ParseToInt().ToString();
                obj = await statisticsBackupsbll.GetZbAllUnitPageList(param, pagination);
            }
            else
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current();
                if (operatorinfo != null && operatorinfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorinfo.UnitId ?? 0;
                }
                obj = await reportDetailBLL.GetAllUnitLabManagerList(param, pagination);
            }
            return Json(obj);
        }
        /// <summary>
        /// 学校详情-学校
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        [AuthorizeFilter("equipmentstatistics:reportdetail:search")]
        public async Task<ActionResult> GetInfoDetailJson(ReportEquipmentParam param)
        {
            if (param.BackupYear == 0 || param.BackupYear == DateTime.Now.Year)//判断年度不是当前年
            {
                OperatorInfo operatorinfo = await Operator.Instance.Current(); 
                if (operatorinfo.UnitType == UnitTypeEnum.School.ParseToInt())
                {
                    param.SchoolId = operatorinfo.UnitId ?? 0;
                }
                var obj = await reportDetailBLL.GetInfoDetail(param);
                return Json(obj);
            }
            else
            {
                TData<object> obj = new TData<object>();
                OperatorInfo operatorInfo = await Operator.Instance.Current();
                var paramBackup = new StatisticsBackupsListParam();
                paramBackup.ModuleId= BackupsModuleIdEnum.ZbStatistics.ParseToInt();
                paramBackup.PageCode = BackupsPageCodeEnum.SchoolInfo.ParseToInt().ToString();
                paramBackup.BackupType = BackupTypeEnum.DataObjectJson.ParseToInt();
                paramBackup.BackupYear = param.BackupYear;
                paramBackup.SchoolId = operatorInfo.UnitId ?? 0;
                var  objData = await statisticsBackupsbll.GetEntity(paramBackup);
                if (objData.Tag == 1 && objData.Data != null)
                {
                    obj.Tag = 1;
                    obj.Data = JsonStringHelper.JSON2Object<object>(objData.Data.Content);
                }
                return Json(obj);
            }
          
        }
        #endregion

        #region 装备投入经费导出
        /// <summary>
        /// 装备投入经费-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> FundsExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorInfo.UnitId ?? 0;
                }
                List<ReportDetailEntity> list = (await reportDetailBLL.GetFundsPageList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                })).Data;
                if (list.Count > 0)
                {

                    string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), "学校装备投入经费.xls");
                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = FundsExportMemoryStream(list))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 装备投入经费
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream FundsExportMemoryStream(List<ReportDetailEntity> list)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);

            for (int i = 0; i <= 12; i++)
            {
                if (i == 1) sheet.SetColumnWidth(i, 22 * 256);
                else if(i == 2) sheet.SetColumnWidth(i, 35 * 256);
                else if (i == 0) sheet.SetColumnWidth(i, 7 * 256);
                else sheet.SetColumnWidth(i, 15 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;

            if (list.Count > 0)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, 12));
                IRow row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(0).SetCellValue("学校装备投入经费");
                row.GetCell(0).CellStyle = topTitleStyle;
                rowIndex++;

                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 0, 0));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 1, 1));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 2, 2));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 3, 6));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 7, 9));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 10, 12));

                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                
                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位编号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("学校基本信息");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("累计投入经费（元）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("当年投入经费（元）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                rowIndex++;

                cellIndex = 0;
                row = sheet.CreateRow(rowIndex);
                rowIndex++;
                row.HeightInPoints = 25;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("学校规模（轨）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("班级数（个）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("教师数（人）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("学生数（人）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("总金额");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("班均额");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("生均额");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("总金额");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("班均额");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("生均额");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                int index = 0;
                foreach (ReportDetailEntity r in list)
                {
                    cellIndex = 0;
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 25;
                    index++;

                    if (r.SchoolName.Equals("<b>总计：<b>"))
                    {
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue("总计：");
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.ClassNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.TeacherNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.StudentNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.TotalAmount.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;



                        row.CreateCell(cellIndex).SetCellValue(r.CurrentYearAmount.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                    }
                    else
                    {
                        row.CreateCell(cellIndex).SetCellValue(index.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.SchoolCode);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.SchoolName);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.SchoolRail.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.ClassNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.TeacherNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.StudentNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.TotalAmount.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue((double)r.TotalClassAvg);
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue((double)r.TotalPeopleAvg);
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;



                        row.CreateCell(cellIndex).SetCellValue(r.CurrentYearAmount.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue((double)r.CurrentClassAvg);
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue((double)r.CurrentPeopleAvg);
                        row.GetCell(cellIndex).CellStyle = contentRightStyle;
                    }
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }
        #endregion

        #region 信息技术导出
        /// <summary>
        /// 信息技术-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> InfoExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorInfo.UnitId ?? 0;
                }
                List<ReportDetailEntity> list = (await reportDetailBLL.GetInfoPageList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                })).Data;
                if (list.Count > 0)
                {

                    string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), "信息技术.xls");
                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = InfoExportMemoryStream(list))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 信息技术
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream InfoExportMemoryStream(List<ReportDetailEntity> list)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);

            for (int i = 0; i <= 30; i++)
            {
                if (i == 1) sheet.SetColumnWidth(i, 18 * 256);
                else if (i == 2) sheet.SetColumnWidth(i, 25 * 256);
                else sheet.SetColumnWidth(i, 15 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;

            if (list.Count > 0)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, 30));
                IRow row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(0).SetCellValue("信息技术");
                row.GetCell(0).CellStyle = topTitleStyle;
                rowIndex++;

                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 0, 0));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 1, 1));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 2, 2));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 3, 4));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 5, 6));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 7, 8));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 9, 12));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 13, 16));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 17, 28));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 29, 30));

                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;

                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位编号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("普通教室多媒体（套）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("专用教室多媒体（套）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其它场所多媒体（套）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("学生计算机（台）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("教师计算机（台）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;

                row.CreateCell(cellIndex).SetCellValue("信息化系统（套）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                for (int n = 0; n < 11; n++)
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue("");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("网络管理员（人）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                rowIndex++;
                row = sheet.CreateRow(rowIndex);
         
                cellIndex = 0;
                row.HeightInPoints = 25;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++; 
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其中交互式");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其中交互式");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其中交互式");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其中平板");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("学生数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("生机比");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其中便携式");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("教师数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("师机比");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("有线网络系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("无线网络系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("校园广播系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("标准化考场");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("户外LED大屏系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("电子班牌系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("安防监控系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("校园访客系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("一卡通系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("物联网系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("校园电视台");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("录播系统");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("人数");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其中专职");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                rowIndex++;
                int index = 0;
                foreach (ReportDetailEntity r in list)
                {
                    cellIndex = 0;
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 25;
                    index++;

                    if (r.SchoolName.Equals("<b>总计：<b>"))
                    {
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("总计：");
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaFunroomNum == null ? "" : r.MediaFunroomNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaInteractiveNum == null ? "" : r.MediaInteractiveNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MedialaboratoryNum == null ? "" : r.MedialaboratoryNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaInteractiveLabNum == null ? "" : r.MediaInteractiveLabNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaOtherNum == null ? "" : r.MediaOtherNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaInteractiveOtherNum == null ? "" : r.MediaInteractiveOtherNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerStudentNum == null ? "" : r.ComputerStudentNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerPadNum == null ? "" : r.ComputerPadNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.StudentNum);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerTeacherNum == null ? "" : r.ComputerTeacherNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerPortableNum == null ? "" : r.ComputerPortableNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.TeacherNum);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysNetworkWired == null ? "" : r.SysNetworkWired.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysNetworkWireless == null ? "" : r.SysNetworkWireless.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysCampusBroadcasting == null ? "" : r.SysCampusBroadcasting.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysStandardizedRoom == null ? "" : r.SysStandardizedRoom.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysOutdoorsLEDBig == null ? "" : r.SysOutdoorsLEDBig.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysElectronicClassCard == null ? "" : r.SysElectronicClassCard.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysSecurityMonitor == null ? "" : r.SysSecurityMonitor.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysCampusVisitor == null ? "" : r.SysCampusVisitor.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysOneCard == null ? "" : r.SysOneCard.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysLoT == null ? "" : r.SysLoT.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysCampusTV == null ? "" : r.SysCampusTV.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysNormalizedBroadcast == null ? "" : r.SysNormalizedBroadcast.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
               
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.NetworkAdminNum == null ? "" : r.NetworkAdminNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.NetworkFulltimeNum == null ? "" : r.NetworkFulltimeNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                    }
                    else
                    {
                        row.CreateCell(cellIndex).SetCellValue(index.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SchoolCode);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SchoolName);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue( r.MediaFunroomNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaInteractiveNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MedialaboratoryNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaInteractiveLabNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaOtherNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.MediaInteractiveOtherNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerStudentNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerPadNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.StudentNum);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.StudentComputerAvg.ToString() + " : 1");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++; 
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerTeacherNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ComputerPortableNum.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.TeacherNum);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.TeacherComputerAvg.ToString() + " : 1");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++; 
                        row.CreateCell(cellIndex).SetCellValue(r.SysNetworkWired == null ? "" : r.SysNetworkWired.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysNetworkWireless == null ? "" : r.SysNetworkWireless.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysCampusBroadcasting == null ? "" : r.SysCampusBroadcasting.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysStandardizedRoom == null ? "" : r.SysStandardizedRoom.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysOutdoorsLEDBig == null ? "" : r.SysOutdoorsLEDBig.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysElectronicClassCard == null ? "" : r.SysElectronicClassCard.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysSecurityMonitor == null ? "" : r.SysSecurityMonitor.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysCampusVisitor == null ? "" : r.SysCampusVisitor.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysOneCard == null ? "" : r.SysOneCard.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysLoT == null ? "" : r.SysLoT.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysCampusTV == null ? "" : r.SysCampusTV.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SysNormalizedBroadcast == null ? "" : r.SysNormalizedBroadcast.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                 
                        cellIndex++; 
                        row.CreateCell(cellIndex).SetCellValue(r.NetworkAdminNum == null ? "" : r.NetworkAdminNum.Value.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.NetworkFulltimeNum == null ? "" : r.NetworkFulltimeNum.Value.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                    }
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }
        #endregion

        #region 实验室与图书馆导出
        /// <summary>
        /// 实验室与图书馆-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> BookExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorInfo.UnitId ?? 0;
                }
                List<ReportDetailEntity> list = (await reportDetailBLL.GetBookPageList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                })).Data;
                if (list.Count > 0)
                {

                    string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), "实验室与图书馆.xls");
                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = BookExportMemoryStream(list))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 实验室与图书馆
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream BookExportMemoryStream(List<ReportDetailEntity> list)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);

            for (int i = 0; i <= 25; i++)
            {
                if (i == 1) sheet.SetColumnWidth(i, 40 * 256);
                else sheet.SetColumnWidth(i, 20 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;

            if (list.Count > 0)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, 20));
                IRow row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(0).SetCellValue("实验室与图书馆");
                row.GetCell(0).CellStyle = topTitleStyle;
                rowIndex++;
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 0, 0));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 1, 1));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 2, 2));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 3, 4));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 5, 6));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 7, 8));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 9, 10));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 11, 20));

                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;

                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位编号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("物理实验室");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("化学实验室");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("生物实验室");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("科学实验室");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("图书馆");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                rowIndex++;

                cellIndex = 0;
                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;

                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("数量（间）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总面积（㎡）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("数量（间）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总面积（㎡）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("数量（间）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总面积（㎡）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("数量（间）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总面积（㎡）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("藏书量（册）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("生均图书（册）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("当年新增图书（册）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("图书馆总面积（平米）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("阅览室座位（个）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("供读者使用的终端（台）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("移动图书柜（组）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("朗读亭（组）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("图书管理员（人）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("其中专职（人）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                rowIndex++;

                int index = 0;
                foreach (ReportDetailEntity r in list)
                {
                    cellIndex = 0;
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 25;
                    index++;

                    if (r.SchoolName.Equals("<b>总计：<b>"))
                    {
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("总计：");
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookTotalNum == null ? "" : r.BookTotalNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookNewYearNum == null ? "" : r.BookNewYearNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookLibraryArea == null ? "" : r.BookLibraryArea.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookRoomSeatNum == null ? "" : r.BookRoomSeatNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookReadersTerminal == null ? "" : r.BookReadersTerminal.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookCabinetNum == null ? "" : r.BookCabinetNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookPavilionNum == null ? "" : r.BookPavilionNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookAdminNum == null ? "" : r.BookAdminNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookFulltimeNum == null ? "" : r.BookFulltimeNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        
                    }
                    else
                    {
                        row.CreateCell(cellIndex).SetCellValue(index.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SchoolCode);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.SchoolName);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.PhysicsFrNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.PhysicsFrArea.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ChemistryFrNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ChemistryFrArea.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BiologyFrNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BiologyFrArea.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ScienceFrNum.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ScienceFrArea.ToString("G0"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookTotalNum == null ? "" : r.BookTotalNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue((double)r.StudentBookAvg);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookNewYearNum == null ? "" : r.BookNewYearNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookLibraryArea == null ? "" : r.BookLibraryArea.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookRoomSeatNum == null ? "" : r.BookRoomSeatNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookReadersTerminal == null ? "" : r.BookReadersTerminal.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookCabinetNum == null ? "" : r.BookCabinetNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookPavilionNum == null ? "" : r.BookPavilionNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                 
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookAdminNum == null ? "" : r.BookAdminNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BookFulltimeNum == null ? "" : r.BookFulltimeNum.Value.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                    }
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        #endregion

        #region 专用室
        /// <summary>
        /// 专用室-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> SportExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorInfo.UnitId ?? 0;
                }
                var  list = (await reportDetailBLL.GetSportPageList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                })).Data;
                List<StaticDictionaryEntity> listDicFunroom = null;
                if (param.DicIds!=null && param.DicIds.Length > 0)
                {
                    var listDic = param.DicIds.Split(",");
                    if (listDic!=null && listDic.Count()> 0)
                    {
                        int[] idArr = TextHelper.SplitToArray<int>(param.DicIds, ',');
                        var  rlistDic = await reportDetailBLL.GetDictionaryList(idArr.ToList());
                        if (rlistDic.Tag==1)
                        {
                            listDicFunroom = rlistDic.Data;
                        }
                    }
                  
                }
                if (list.Count > 0)
                {

                    string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), "专用教室.xls");
                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = SportExportMemoryStream(list, listDicFunroom))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 专用教室
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream SportExportMemoryStream(List<object> list, List<StaticDictionaryEntity> listDicfunroom)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);
            int rowNumTotal = 15;
            if (listDicfunroom != null && listDicfunroom.Count ()> 0)
            {
                rowNumTotal = 15 + listDicfunroom.Count();
            }
            for (int i = 0; i < rowNumTotal; i++)
            {
                if (i == 0) sheet.SetColumnWidth(i, 7 * 256);
                else if(i == 1) sheet.SetColumnWidth(i, 20 * 256);
                else if (i == 2) sheet.SetColumnWidth(i, 25 * 256);
                else sheet.SetColumnWidth(i, 17 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;

            if (list.Count > 0)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, rowNumTotal - 1));
                IRow row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(0).SetCellValue("专用教室");
                row.GetCell(0).CellStyle = topTitleStyle;

                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 0, 0));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 1, 1));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 2, 2));
                int addressIndex = 3;
                if (listDicfunroom != null && listDicfunroom.Count() > 0)
                {
                    addressIndex = addressIndex + listDicfunroom.Count() - 1;
                }
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, 3, addressIndex));
                addressIndex = addressIndex + 1;
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, addressIndex, addressIndex+5));
                addressIndex = addressIndex + 6;
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, addressIndex, addressIndex + 5)); 

                rowIndex++;
                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位编号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("中小学专用教室（间）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                for (int i = 0; i < listDicfunroom.Count - 1; i++)
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue("");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("幼儿园专用教室（间）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                for (int i = 0; i < 5; i++)
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue("");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("体育场所");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                for (int i = 0; i < 5; i++)
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue("");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }

                cellIndex = 0;
                rowIndex++; 
                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;

                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                //循环
                if (listDicfunroom != null && listDicfunroom.Count > 0)
                {
                    foreach (var item in listDicfunroom)
                    {
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(item.DicName);
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                    }
           
                }
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("幼儿园音乐类");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("幼儿园美工类");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("幼儿园科学类");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("幼儿园建构类");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("幼儿园体育类");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("幼儿园其他类");
                row.GetCell(cellIndex).CellStyle = titleStyle;


                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("跑道长度（米）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                 
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("篮球场（个）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("排球场（个）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("足球场（个）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("乒乓球桌（张）");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("室内运动场（个）");
                row.GetCell(cellIndex).CellStyle = titleStyle;
                rowIndex++;

                int index = 0;
                foreach (Dictionary<string,string> r in list)
                {
                    cellIndex = 0;
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 25;
                    index++;

                    if (r["SchoolName"].Equals("<b>总计：<b>"))
                    {
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("总计：");
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        foreach (var item in listDicfunroom)
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Dic_" + item.DictionaryId.ToString()]);
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                        }

                        for (int i = 1; i < 7; i++)
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Category_" + i.ToString()]);
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                        }

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportRunwayLength"]);
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportBasketballNum"]);
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportVolleyballNum"]);
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportFootballNum"]);
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportPingPongNum"]);
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportRoomNum"]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                    }
                    else
                    {
                        row.CreateCell(cellIndex).SetCellValue(index.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SchoolCode"]);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;

                        cellIndex++; 
                        row.CreateCell(cellIndex).SetCellValue(r["SchoolName"]);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;

                        foreach (var item in listDicfunroom)
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Dic_" + item.DictionaryId.ToString()]);
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        }

                        for (int i = 1; i < 7; i++)
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Category_" + i.ToString()]);
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        }

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportRunwayLength"]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportBasketballNum"]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportVolleyballNum"]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportFootballNum"]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportPingPongNum"]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SportRoomNum"]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                    }
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        #endregion

        #region 科创设备导出
        /// <summary>
        /// 科创设备导出-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> EquipmentExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorInfo.UnitId ?? 0;
                }
                List<ReportEquipmentEntity> list = (await reportDetailBLL.GetEquipmentPageList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                })).Data;
                if (list.Count > 0)
                {
                    //var columns = await new ExcelColumn().GetExpertcolumn(param.BasePageCode);//101005
                    //string file = new ExcelHelper<ReportEquipmentEntity>().ExportToExcel("科创设备.xls", "科创设备", list, columns);
                    //obj.Data = file;
                    //obj.Tag = 1; 

                    string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), "科创设备.xls");
                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = EquipmentExportMemoryStream(list))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 科创设备
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream EquipmentExportMemoryStream(List<ReportEquipmentEntity> list)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);

            for (int i = 0; i <= 8; i++)
            {
                if (i == 8) sheet.SetColumnWidth(i, 40 * 256);
                else if(i==0) sheet.SetColumnWidth(i, 8 * 256);
                else if (i == 1) sheet.SetColumnWidth(i, 25 * 256);
                else if (i == 2) sheet.SetColumnWidth(i, 30 * 256);
                else sheet.SetColumnWidth(i, 20 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;

            if (list.Count > 0)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, 8));
                IRow row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(0).SetCellValue("科创设备");
                row.GetCell(0).CellStyle = topTitleStyle;
                rowIndex++;

                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;

                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位编号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("设备名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("品牌");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("型号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("数量");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("备注");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                rowIndex++;

                int index = 0;
                foreach (ReportEquipmentEntity r in list)
                {
                    cellIndex = 0;
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 25;
                    index++;

                    if (r.SchoolName.Equals("<b>总计：<b>"))
                    {
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("总计：");
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Num.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                    }
                    else
                    {
                        row.CreateCell(cellIndex).SetCellValue(index.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++; 
                        row.CreateCell(cellIndex).SetCellValue(r.SchoolCode);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++; 
                        row.CreateCell(cellIndex).SetCellValue(r.SchoolName);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Name);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Brand);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Modelz);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Num.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.UnitName);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Remark);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                    }
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        #endregion

        #region 幼儿园专用室导出
        /// <summary>
        /// 幼儿园专用室-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> KindergartenExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                List<ReportSpecialRoomEntity> list = (await reportDetailBLL.GetKindergartenPageList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                })).Data;
                if (list.Count > 0)
                {

                    string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), "幼儿园专用室.xls");
                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = KindergartenExportMemoryStream(list))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 幼儿园专用室
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream KindergartenExportMemoryStream(List<ReportSpecialRoomEntity> list)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);

            for (int i = 0; i <= 7; i++)
            {
                if (i == 1) sheet.SetColumnWidth(i, 40 * 256);
                else sheet.SetColumnWidth(i, 20 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;

            if (list.Count > 0)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, 7));
                IRow row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(0).SetCellValue("幼儿园专用室");
                row.GetCell(0).CellStyle = topTitleStyle;
                rowIndex++;

                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;

                row.CreateCell(0).SetCellValue("序号");
                row.GetCell(0).CellStyle = titleStyle;

                row.CreateCell(1).SetCellValue("单位名称");
                row.GetCell(1).CellStyle = titleStyle;

                row.CreateCell(2).SetCellValue("分类名称");
                row.GetCell(2).CellStyle = titleStyle;

                row.CreateCell(3).SetCellValue("专用室名称");
                row.GetCell(3).CellStyle = titleStyle;

                row.CreateCell(4).SetCellValue("使用面积（平米）");
                row.GetCell(4).CellStyle = titleStyle;

                row.CreateCell(5).SetCellValue("起初建设时间");
                row.GetCell(5).CellStyle = titleStyle;

                row.CreateCell(6).SetCellValue("最新改造时间");
                row.GetCell(6).CellStyle = titleStyle;

                row.CreateCell(7).SetCellValue("备注");
                row.GetCell(7).CellStyle = titleStyle;

                rowIndex++;

                int index = 0;
                foreach (ReportSpecialRoomEntity r in list)
                {
                    cellIndex = 0;
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 25;
                    index++;

                    if (r.SchoolName.Equals("<b>总计：<b>"))
                    {
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("总计：");
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.CategoryName);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Name);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.UseArea.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BuildTime == null ? "" : r.BuildTime.Value.ToString("yyyy-MM-dd"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ReformTime == null ? "" : r.ReformTime.Value.ToString("yyyy-MM-dd"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Remark);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                    }
                    else
                    {
                        row.CreateCell(cellIndex).SetCellValue(index.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.SchoolName);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;
                        cellIndex++;

                        row.CreateCell(cellIndex).SetCellValue(r.CategoryName);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Name);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.UseArea.ToString("0.##"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.BuildTime == null ? "" : r.BuildTime.Value.ToString("yyyy-MM-dd"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.ReformTime == null ? "" : r.ReformTime.Value.ToString("yyyy-MM-dd"));
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r.Remark);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                    }
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        #endregion

        #region 实验室管理员导出
        /// <summary>
        /// 实验室管理员-导出
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        public async Task<IActionResult> LabManagerExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                if (operatorInfo.UnitType == UnitTypeEnum.County.ParseToInt())
                {
                    param.CountyId = operatorInfo.UnitId ?? 0;
                }
                var list = (await reportDetailBLL.GetLabManagerList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                })).Data;
                List<StaticDictionaryEntity> listDicFunroom = null;
                if (param.DicIds != null && param.DicIds.Length > 0)
                {
                    var listDic = param.DicIds.Split(",");
                    if (listDic != null && listDic.Count() > 0)
                    {
                        int[] idArr = TextHelper.SplitToArray<int>(param.DicIds, ',');
                        var rlistDic = await reportDetailBLL.GetDictionaryList(idArr.ToList());
                        if (rlistDic.Tag == 1)
                        {
                            listDicFunroom = rlistDic.Data;
                        }
                    }

                }
                if (list.Count > 0)
                {

                    string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), "实验室管理员.xls");
                    string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                    string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                    string sDirectory = Path.Combine(sRoot, partDirectory);
                    string sFilePath = Path.Combine(sDirectory, sFileName);
                    if (!Directory.Exists(sDirectory))
                    {
                        Directory.CreateDirectory(sDirectory);
                    }
                    using (MemoryStream ms = LabManagerExportMemoryStream(list, listDicFunroom))
                    {
                        using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                        {
                            byte[] data = ms.ToArray();
                            fs.Write(data, 0, data.Length);
                            fs.Flush();
                        }
                    }
                    obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                    obj.Tag = 1;
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "没有可导出的数据！";
                }
            }
            return Json(obj);
        }

        /// <summary>
        /// 实验室管理员
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream LabManagerExportMemoryStream(List<object> list, List<StaticDictionaryEntity> listDicfunroom)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);
            int rowNumTotal = 2;
            if (listDicfunroom != null && listDicfunroom.Count() > 0)
            {
                rowNumTotal = 2 + listDicfunroom.Count * 2;
            }
            for (int i = 0; i <= rowNumTotal; i++)
            {
                if (i == 0) sheet.SetColumnWidth(i, 7 * 256);
                else if (i == 1) sheet.SetColumnWidth(i, 25 * 256);
                else if (i == 2) sheet.SetColumnWidth(i, 35 * 256);
                else sheet.SetColumnWidth(i, 20 * 256);
            }

            int rowIndex = 0;
            int cellIndex = 0;

            if (list.Count > 0)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, rowNumTotal)); 
                IRow row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                row.CreateCell(0).SetCellValue("实验室管理员");
                row.GetCell(0).CellStyle = topTitleStyle;

                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 0, 0));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 1, 1));
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, 2, 2));
                int addressIndex = 3;
                for (int i = 0; i < listDicfunroom.Count; i++)
                {
                    sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, addressIndex, addressIndex + 1));
                    addressIndex += 2;
                }
                rowIndex++;
                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
         
                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位编号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                for (int i = 0; i < listDicfunroom.Count; i++)
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue(listDicfunroom[i].DicName+ "（人）");
                    row.GetCell(cellIndex).CellStyle = titleStyle;

                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue("");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }

                rowIndex++;
                cellIndex = 0;
                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;

                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位编号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("单位名称");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                //循环
                if (listDicfunroom != null && listDicfunroom.Count > 0)
                {
                    foreach (var item in listDicfunroom)
                    {
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("人数");
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("其中专职");
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                    }

                }

                rowIndex++;
                int index = 0;
                foreach (Dictionary<string, string> r in list)
                {
                    cellIndex = 0;
                    row = sheet.CreateRow(rowIndex);
                    rowIndex++;
                    row.HeightInPoints = 25;
                    index++;

                    if (r["SchoolName"].Equals("<b>总计：<b>"))
                    {
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("");
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue("总计：");
                        row.GetCell(cellIndex).CellStyle = titleStyle;

                        foreach (var item in listDicfunroom)
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Dic_" + item.DictionaryId.ToString()]);
                            row.GetCell(cellIndex).CellStyle = titleStyle;

                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Dic_" + item.DictionaryId.ToString() + "_2"]);
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                        }
                    }
                    else
                    {
                        row.CreateCell(cellIndex).SetCellValue(index.ToString());
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SchoolCode"]);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;

                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r["SchoolName"]);
                        row.GetCell(cellIndex).CellStyle = contentLeftStyle;

                        foreach (var item in listDicfunroom)
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Dic_" + item.DictionaryId.ToString()]);
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(r["Dic_" + item.DictionaryId.ToString() + "_2"]);
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        }
                    }
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }

        #endregion

        #region 提交数据
        [HttpPost]
        [AuthorizeFilter("equipmentstatistics:reportdetail:add,equipmentstatistics:reportdetail:edit")]
        public async Task<ActionResult> SaveFormJson(ReportDetailInputModel model)
        {
            TData<string> obj = await reportDetailBLL.SaveForm(model);
            return Json(obj);
        }

        [HttpPost]
        [AuthorizeFilter("equipmentstatistics:reportdetail:delete")]
        public async Task<ActionResult> DeleteFormJson(string ids)
        {
            TData obj = await reportDetailBLL.DeleteForm(ids);
            return Json(obj);
        }
        #endregion

        #region 备份数据
        /// <summary>
        /// 获取标题
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> GetBackupsTitleJson(StatisticsBackupsListParam param)
        {
            var obj = await statisticsBackupsbll.GetList(param);
            return Json(obj);
        }

        /// <summary>
        /// 测试，后续会删除
        /// </summary>
        /// <param name="year"></param>
        /// <returns></returns>
        //[HttpPost]
        //public async Task<ActionResult> SaveBackUpsFormJson(int year)
        //{
        //    var obj = await statisticsBackupsbll.ReportYearBackup(year);
        //    return Json(obj);
        //}


        /// <summary>
        /// 测试，后续会删除
        /// </summary>
        /// <param name="year"></param>
        /// <returns></returns>
        public async Task<IActionResult> BackupExport(ReportEquipmentParam param)
        {
            TData<string> obj = new TData<string>();
            obj.Message = "没有可导出的数据！";
            OperatorInfo operatorInfo = await Operator.Instance.Current();
            if (operatorInfo != null)
            {
                var paramTitle =new  StatisticsBackupsListParam();
                paramTitle.PageCode = param.PageCode;
                paramTitle.BackupYear = param.BackupYear;
                paramTitle.ModuleId = BackupsModuleIdEnum.ZbStatistics.ParseToInt();
                paramTitle.BackupType = BackupTypeEnum.DataTitleJson.ParseToInt();
                var objTitle = await statisticsBackupsbll.GetList(paramTitle);

                param.BackupType = BackupTypeEnum.DataObjectJson.ParseToInt();
                var objBackupData = await statisticsBackupsbll.GetAllList(param, new Pagination
                {
                    Sort = "Sort",
                    SortType = "ASC",
                    PageIndex = 1,
                    PageSize = int.MaxValue,
                });
                if (!(objTitle.Tag == 1 && objTitle.Data != null && objTitle.Data.Count > 0))
                {
                    obj.Message = "没有可导出的数据！";
                    return Json(obj);
                }
                if (!(objBackupData.Tag == 1 && objBackupData.Data != null && objBackupData.Data.Count > 0))
                {
                    obj.Message = "没有可导出的数据！";
                    return Json(obj);
                }
                var list = objBackupData.Data.Select(m => JsonStringHelper.JSON2Object<object>(m.Content)).ToList();
                var titleObj = JsonStringHelper.JSON2Object<List<object>>(objTitle.Data.FirstOrDefault().Content);
                if (list == null || list.Count() == 0 || titleObj == null || titleObj.Count() == 0)
                {
                    obj.Message = "没有可导出的数据！";
                    return Json(obj);
                }
                string sheetName = objTitle.Data[0].Title;
                string sFileName = string.Format("{0}_{1}", SecurityHelper.GetGuid(true), sheetName + ".xls");
                string sRoot = GlobalContext.HostingEnvironment.ContentRootPath;
                string partDirectory = string.Format("Resource{0}Export{0}Excel", Path.DirectorySeparatorChar);
                string sDirectory = Path.Combine(sRoot, partDirectory);
                string sFilePath = Path.Combine(sDirectory, sFileName);
                if (!Directory.Exists(sDirectory))
                {
                    Directory.CreateDirectory(sDirectory);
                }

                using (MemoryStream ms = BackupExportMemoryStream(list, titleObj, sheetName))
                {
                    using (FileStream fs = new FileStream(sFilePath, FileMode.Create, FileAccess.Write))
                    {
                        byte[] data = ms.ToArray();
                        fs.Write(data, 0, data.Length);
                        fs.Flush();
                    }
                }
                obj.Data = partDirectory + Path.DirectorySeparatorChar + sFileName;
                obj.Tag = 1;
                obj.Message = "导出成功！";

            }
            return Json(obj);
        }

        /// <summary>
        /// 备份数据导出数据
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private MemoryStream BackupExportMemoryStream(List<object> list, List<object> listtitle,string titlename)
        {
            HSSFWorkbook workbook = new HSSFWorkbook();
            #region sheet_title

            ISheet sheet = workbook.CreateSheet();

            IFont headFont = workbook.CreateFont();
            headFont.FontName = "宋体";
            headFont.FontHeightInPoints = 12;
            headFont.IsBold = true;

            IFont topFont = workbook.CreateFont();
            topFont.FontName = "宋体";
            topFont.FontHeightInPoints = 20;
            topFont.IsBold = true;
            ICellStyle topTitleStyle = workbook.CreateCellStyle();
            topTitleStyle.Alignment = HorizontalAlignment.Center;
            topTitleStyle.VerticalAlignment = VerticalAlignment.Center;
            topTitleStyle.SetFont(topFont);

            ICellStyle headStyle = workbook.CreateCellStyle();
            headStyle.Alignment = HorizontalAlignment.Center;
            headStyle.VerticalAlignment = VerticalAlignment.Center;
            headStyle.BorderTop = BorderStyle.Thin;
            headStyle.BorderLeft = BorderStyle.Thin;
            headStyle.BorderRight = BorderStyle.Thin;
            headStyle.BorderBottom = BorderStyle.Thin;
            headStyle.SetFont(headFont);

            IFont titleFont = workbook.CreateFont();
            titleFont.FontName = "宋体";
            titleFont.FontHeightInPoints = 10;
            titleFont.IsBold = true;
            ICellStyle titleStyle = workbook.CreateCellStyle();
            titleStyle.Alignment = HorizontalAlignment.Center;
            titleStyle.VerticalAlignment = VerticalAlignment.Center;
            titleStyle.BorderTop = BorderStyle.Thin;
            titleStyle.BorderLeft = BorderStyle.Thin;
            titleStyle.BorderRight = BorderStyle.Thin;
            titleStyle.BorderBottom = BorderStyle.Thin;
            titleStyle.SetFont(titleFont);

            IFont contentFont = workbook.CreateFont();
            contentFont.FontName = "宋体";
            contentFont.FontHeightInPoints = 10;
            contentFont.IsBold = false;

            ICellStyle contentLeftStyle = workbook.CreateCellStyle();
            contentLeftStyle.Alignment = HorizontalAlignment.Left;
            contentLeftStyle.VerticalAlignment = VerticalAlignment.Center;
            contentLeftStyle.BorderTop = BorderStyle.Thin;
            contentLeftStyle.BorderLeft = BorderStyle.Thin;
            contentLeftStyle.BorderRight = BorderStyle.Thin;
            contentLeftStyle.BorderBottom = BorderStyle.Thin;
            contentLeftStyle.SetFont(contentFont);

            ICellStyle contentRightStyle = workbook.CreateCellStyle();
            contentRightStyle.Alignment = HorizontalAlignment.Right;
            contentRightStyle.VerticalAlignment = VerticalAlignment.Center;
            contentRightStyle.BorderTop = BorderStyle.Thin;
            contentRightStyle.BorderLeft = BorderStyle.Thin;
            contentRightStyle.BorderRight = BorderStyle.Thin;
            contentRightStyle.BorderBottom = BorderStyle.Thin;
            contentRightStyle.SetFont(contentFont);

            ICellStyle contentCenterStyle = workbook.CreateCellStyle();
            contentCenterStyle.Alignment = HorizontalAlignment.Center;
            contentCenterStyle.VerticalAlignment = VerticalAlignment.Center;
            contentCenterStyle.BorderTop = BorderStyle.Thin;
            contentCenterStyle.BorderLeft = BorderStyle.Thin;
            contentCenterStyle.BorderRight = BorderStyle.Thin;
            contentCenterStyle.BorderBottom = BorderStyle.Thin;
            contentCenterStyle.SetFont(contentFont);


            #endregion

            #region 解析列头信息。确认标题
            List<GridTableColumn> listTitleGroup = new List<GridTableColumn>();
            List<GridTableColumn> listTitle= new List<GridTableColumn>();
            int rowspanNum = 0;
            if (listtitle!=null && listtitle.Count > 0)
            {
                //存在分组
                List<object> listTitleObject = new List<object>();
                if (listtitle.Count == 2)
                {
                    if (listtitle[0]!=null)
                    {
                        var listGrouptitle = JsonStringHelper.JSON2Object<List<object>>(listtitle[0].ToString());
                        if (listGrouptitle!=null && listGrouptitle.Count > 0)
                        {
                            foreach (var  itemCell in listGrouptitle)
                            {
                                if (itemCell != null)
                                {
                                    GridTableColumn modelColum = JsonStringHelper.JSON2Object<GridTableColumn>(itemCell.ToString());
                                    if (modelColum != null)
                                    {
                                        if (modelColum.rowspan > 1)
                                        {
                                            rowspanNum += (modelColum.rowspan - 1);
                                        }
                                        listTitleGroup.Add(modelColum);
                                    }
                                }
                            }
                        }
                    }
                    if (listtitle[1] != null)
                    {
                        listTitleObject = JsonStringHelper.JSON2Object<List<object>>(listtitle[1].ToString()); 
                    }
                }
                else
                {
                    if (listtitle[0] != null)
                    {
                        listTitleObject = JsonStringHelper.JSON2Object<List<object>>(listtitle[0].ToString());
                    }
                }
                if (listTitleObject!=null && listTitleObject.Count > 0)
                {
                    foreach (var itemCell in listTitleObject)
                    {
                        if (itemCell != null)
                        {
                            GridTableColumn modelColum = JsonStringHelper.JSON2Object<GridTableColumn>(itemCell.ToString());
                            if (modelColum != null)
                            {
                                listTitle.Add(modelColum);
                            }
                        }
                    }

                }
            }
            #endregion

            #region sheet_title_width

            int columnNum = listTitle.Count;
            if (rowspanNum > 0)
            {
                columnNum = columnNum + rowspanNum;
            }
            for (int i = 0; i < columnNum; i++)
            {
                if (i == 0) sheet.SetColumnWidth(i, 7 * 256);
                else if (i == 1) sheet.SetColumnWidth(i, 25 * 256);
                else if (i == 2) sheet.SetColumnWidth(i, 35 * 256);
                else sheet.SetColumnWidth(i, 20 * 256);
            }

            #endregion

            #region sheet_title_cell_name

            int rowIndex = 0;
            int cellIndex = 0;

            sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(rowIndex, rowIndex, 0, columnNum - 1));
            IRow row = sheet.CreateRow(rowIndex);
            row.HeightInPoints = 35;
            row.CreateCell(0).SetCellValue(titlename);
            row.GetCell(0).CellStyle = topTitleStyle;

            int addressIndex = 0;
            for (int i = 0; i < listTitleGroup.Count; i++)
            {
                if (listTitleGroup[i].colspan <= 1)
                {
                    //默认只合并2列，不再做扩展
                    sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 2, addressIndex, addressIndex));
                    addressIndex++;
                }
                else
                {
                    sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(1, 1, addressIndex, addressIndex + listTitleGroup[i].colspan - 1));
                    addressIndex += listTitleGroup[i].colspan;
                }
            }
            //第二列，存在合并列
            if (listTitleGroup.Count > 0)
            {
                rowIndex++;
                row = sheet.CreateRow(rowIndex);
                row.HeightInPoints = 35;
                //汇总默认都是这
                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                for (int i = 0; i < listTitleGroup.Count; i++)
                {
                    if (!string.IsNullOrEmpty(listTitleGroup[i].title))
                    {
                        if (listTitleGroup[i].field != "index")
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(listTitleGroup[i].title);
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                            for (int j = 1; j < listTitleGroup[i].colspan; j++)
                            {
                                cellIndex++;
                                row.CreateCell(cellIndex).SetCellValue("");
                                row.GetCell(cellIndex).CellStyle = titleStyle;
                            }
                        }
                    }
                }
            }
            rowIndex++;
            row = sheet.CreateRow(rowIndex);
            row.HeightInPoints = 35;
            cellIndex = 0;
            //row.CreateCell(cellIndex).SetCellValue("序号");
            //row.GetCell(cellIndex).CellStyle = titleStyle;
            //第三列
            //循环
            if (listTitle != null && listTitle.Count > 0)
            {
                row.CreateCell(cellIndex).SetCellValue("序号");
                row.GetCell(cellIndex).CellStyle = titleStyle;
 
                foreach (var item in listTitleGroup)
                {
                    if (item.title != "序号")
                    {
                        if (item.rowspan > 1)
                        {
                            cellIndex++;
                            row.CreateCell(cellIndex).SetCellValue(item.title.Replace("<br/>", ""));
                            row.GetCell(cellIndex).CellStyle = titleStyle;
                        }
                    }
                }

                foreach (var item in listTitle)
                {
                    if (item.title != "序号")
                    {
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(item.title.Replace("<br/>", ""));
                        row.GetCell(cellIndex).CellStyle = titleStyle;
                    }
                }
            }

            #endregion

            Dictionary<string, decimal> totalObj = new Dictionary<string, decimal>();//值为-1则当前列不统计

            rowIndex++;
            int index = 0;
            foreach (var itemdata in list)
            {
                //var r = (Dictionary<string, string>)itemdata;
                var r = JsonStringHelper.JSON2Object<Dictionary<string, string>>(itemdata.ToString());
                cellIndex = 0;
                row = sheet.CreateRow(rowIndex);
                rowIndex++;
                row.HeightInPoints = 25;
                index++;

                row.CreateCell(cellIndex).SetCellValue(index.ToString());
                row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                foreach (var item in listTitleGroup)
                {
                    if (item.field != "index")
                    {
                        if (item.rowspan > 1)
                        {
                            cellIndex++;
                            if (item.field == "StudentComputerAvg" || item.field == "TeacherComputerAvg") //生均、师均 计算机比加 : 1
                            {
                                row.CreateCell(cellIndex).SetCellValue(r[item.field] + " : 1");                              
                            }
                            else
                                row.CreateCell(cellIndex).SetCellValue(r[item.field]);
                            row.GetCell(cellIndex).CellStyle = contentCenterStyle;

                        }
                    }
                }
                foreach (var item in listTitle)
                {
                    if (item.field != "index")
                    {
                        cellIndex++;
                        row.CreateCell(cellIndex).SetCellValue(r[item.field]);
                        row.GetCell(cellIndex).CellStyle = contentCenterStyle;
                        decimal numTemp = 0;
                        if (item.formatter == 4)
                        {
                            decimal.TryParse(r[item.field].ToString(), out numTemp);
                            if (totalObj.ContainsKey(item.field))
                            {
                                totalObj[item.field] = totalObj[item.field] + numTemp;
                            }
                            else
                            {
                                totalObj.Add(item.field, numTemp);
                            }
                        }
                        else
                        {
                            if (!totalObj.ContainsKey(item.field))
                            {
                                totalObj.Add(item.field, -1);
                            }
                        }
                    }

                }
            }

            row = sheet.CreateRow(rowIndex); 
            row.HeightInPoints = 25;
            index++;
            cellIndex = 0;
            row.CreateCell(cellIndex).SetCellValue(index.ToString());
            row.GetCell(cellIndex).CellStyle = contentCenterStyle;
            if (listTitleGroup.Count > 0)
            {
                //编码
                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("");
                row.GetCell(cellIndex).CellStyle = titleStyle;

                cellIndex++;
                row.CreateCell(cellIndex).SetCellValue("总计：");
                row.GetCell(cellIndex).CellStyle = titleStyle;
            }
            foreach (var item in totalObj)
            {
                if (item.Key == "SchoolName")
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue("总计：");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }
                else if (item.Value == -1)
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue("");
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }
                else
                {
                    cellIndex++;
                    row.CreateCell(cellIndex).SetCellValue(item.Value.ToString("G0"));
                    row.GetCell(cellIndex).CellStyle = titleStyle;
                }
            }

            using (MemoryStream ms = new MemoryStream())
            {
                workbook.Write(ms);
                workbook.Close();
                ms.Flush();
                ms.Position = 0;
                return ms;
            }
        }
        #endregion
    }
}
