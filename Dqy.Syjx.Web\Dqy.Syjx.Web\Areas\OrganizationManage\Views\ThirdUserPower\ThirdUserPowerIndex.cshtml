﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }
</style>

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="unitTypeId" col="UnitTypeId" style="display: inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="key" col="Key" type="text" placeholder="单位名称、人员名称、手机号码、角色名称" style="display: inline-block;width:250px;" />
                    </li>
                    <li class="select-time">
                        <label>创建时间： </label>
                        <input id="startTime" col="StartTime" type="text" class="time-input" placeholder="开始时间" />
                        <span>-</span>
                        <input id="endTime" col="EndTime" type="text" class="time-input" placeholder="结束时间" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
            <a id="btnImport" class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();

        @*$("#unitName").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetListJson?PageSize=10000")',
            key: "Id",
            value: "Name",
            defaultName: '单位名称',
        });*@

        laydate.render({ elem: '#startTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });


        $("#unitTypeId").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(UnitTypeNameEnum).EnumToDictionaryString())),
            defaultName: '单位类型',
        });
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/ThirdUserPower/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: "id",
            sortOrder:"asc",
            columns: [
                { checkbox: true, visible: true },
                { field: 'UnitName', title: '单位名称', width: 180, sortable: true, halign: 'center', align: 'left' },
                { field: 'UserName', title: '人员姓名', width: 120, sortable: true, halign: 'center', align: 'center' },
                { field: 'Mobile', title: '手机号码', width: 100, sortable: true, halign: 'center', align: 'center' },
                { field: 'UnitTypeName', title: '单位类型', width: 80, sortable: true, halign: 'center', align: 'center' },
                { field: 'RoleNames', title: '角色', width: 200, sortable: true, halign: 'center', align: 'left' },
                {
                    field: 'BaseCreateTime', title: '创建时间', halign: 'center', align: 'center', width: 120, sortable: true, formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd HH:mm:ss");
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function clearGrid() {
        $('#unitTypeId').ysComboBox('setValue', -1);
        $("#key").val("");
        $("#startTime").val("");
        $("#endTime").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/OrganizationManage/ThirdUserPower/ThirdUserPowerForm")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/ThirdUserPower/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

   function importForm() {
      ys.openDialog({
            title: "导入第三方授权数据",
            content: '@Url.Content("~/OrganizationManage/ThirdUserPower/ThirdUserPowerImport")',
            height: "280px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
