﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>填报日期： </label><input id="startDate" col="StartDate" type="text" class="time-input" placeholder="开始时间" style="width:100px" />
                        <span>-</span>
                        <input id="endDate" col="EndDate" type="text" class="time-input" placeholder="结束时间" style="width:100px" />
                    </li>
                    @await Html.PartialAsync("/Areas/InstrumentManage/Shared/DeclarationSearchPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    <li>
                        <span id="userId" col="UserId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>

                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="batchAudit();"><i class="fa fa-edit"></i> 批量审批</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        loadUser();

        initGrid();

        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseAudit/GetAuidtListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            /*showFooter: true,*/
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'opt', title: '操作', align: 'center', halign: 'center', width: commonWidth.Instrument.Opt1,
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-success btn-xs" href="#" onclick="audit(this)" value="{0}"><i class="fa fa-edit"></i>审批</a> ', row.Id);
                    }
                },
                //{ field: 'PurchaseYear', title: '采购年度', align: 'center', halign: 'center', sortable: true, width: 50  },
                //{ field: 'InstrumentClassName', title: '仪器类别', align: 'left', halign: 'center', sortable: true, width: 140},
                { field: 'Course', title: '适用学科', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Course },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格属性', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'Num', title: '数量', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'Price', title: '单价', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        return ComBox.ToLocaleString(value);
                    }
                },
                {
                    field: 'AmountSum', title: '金额', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        return ComBox.ToLocaleString(value);
                    }
                },
                { field: 'UserName', title: '填报人', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.UserName },
                {
                    field: 'BaseCreateTime', title: '填报日期', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                }
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function audit(obj) {
        var id = $(obj).attr('value');
        //var url = '@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseAuditForm")' + '?id=' + id;
        //createMenuItem(url, "学校审批");
        ys.openDialog({
            title: '采购审批',
            content: '@Url.Content("~/InstrumentManage/PurchaseAudit/BatchAuditForm")' + '?ids=' + id,
            width: '768px',
            height: '300px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function loadUser() {
        $('#userId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '填报人'
        });
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#purchaseYear').ysComboBox('setValue', -1);
        $('#instrumentClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#userId').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    /**
     *批量审批
     */
    function batchAudit() {
        var ids = '';
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ids = ys.getIds(selectedRow);
        } else {
            ys.msgError("请选择需要审批的数据。");
            return false;
        }
        ys.openDialog({
            title: '批量审批',
            content: '@Url.Content("~/InstrumentManage/PurchaseAudit/BatchAuditForm")' + '?ids=' + ids,
            width: '768px',
            height: '300px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
