﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .li_width{width:160px;}
</style>
<div class="container-div">
 @*   <div class="row" style="height:auto;">
        <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-down"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            注：是指区县或市级需要统计的对象，请根据教育局要求进行添加
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        性别：<div id="Gender" col="Gender" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        职称：<div id="JobTitle" col="JobTitle" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        任职学科：<div id="SubjectId" col="SubjectId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        是否实验员：<div id="IsExperimenter" col="IsExperimenter" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        实验员性质：<div id="ExperimenterNature" col="ExperimenterNature" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <input id="RealName" col="RealName" type="text" placeholder="姓名" style="width: 200px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-success btn-sm" onclick="resetGrid()"><i class="fa fa-remove"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        $("#Gender").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(GenderTypeEnum).EnumToDictionaryString()))});
        $("#IsExperimenter").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())) });
        $("#ExperimenterNature").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimenterNatureEnum).EnumToDictionaryString())) });
        $("#JobTitle").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(JobTiitleEnum).EnumToDictionaryString())),
        });
        $("#SubjectId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1005&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',

        });
        $(".select2-container").width("100%");
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/BusinessManage/UserReport/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'RealName', title: '姓名' },
                {
                    field: 'Gender', title: '性别', formatter: function (value, row, index) {
                        if (row.Gender == "@GenderTypeEnum.Female.ParseToInt()") {
                            return '<span>' + "@GenderTypeEnum.Female.GetDescription()" + '</span>';
                        } else if (row.Gender == "@GenderTypeEnum.Male.ParseToInt()") {
                            return '<span>' + "@GenderTypeEnum.Male.GetDescription()" + '</span>';
                        } else {
                            return '<span>' + "@GenderTypeEnum.Unknown.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'Birthday', title: '出生日期', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                },
                {
                    field: 'JobTitleName', title: '职称', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = value;
                        }
                        return html;
                    }
                },
                {
                    field: 'SubjectName', title: '任职学科', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = value;
                        }
                        return html;
                    }
                },
                {
                    field: 'IsExperimenter', title: '是否实验员', formatter: function (value, row, index) {
                        if (row.IsExperimenter == "@IsEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@IsEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@IsEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'ExperimenterNature', title: '实验员性质', formatter: function (value, row, index) {
                        if (row.ExperimenterNature == "@ExperimenterNatureEnum.FullTime.ParseToInt()") {
                            return '<span>' + "@ExperimenterNatureEnum.FullTime.GetDescription()" + '</span>';
                        } else {
                            return '<span>' + "@ExperimenterNatureEnum.PartTime.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'TotalSchoolHour', title: '培训课时<br/>（小时）', formatter: function (value, row, index) {
                        var html = '--';
                        if (isNaN(value) && value.length > 0) {
                            html = value;
                        }
                        return html;
                    }
                },
                {
                    field: 'ExperimenterType', title: '类别', formatter: function (value, row, index) {
                        if (row.ExperimenterType == "@ExperimenterTypeEnum.City.ParseToInt()") {
                            return '<span>' + "@ExperimenterTypeEnum.City.GetDescription()" + '</span>';
                        } else if (row.ExperimenterType == "@ExperimenterTypeEnum.County.ParseToInt()") {
                            return '<span>' + "@ExperimenterTypeEnum.County.GetDescription()" + '</span>';
                        } else {
                            return '<span>' + "@ExperimenterTypeEnum.All.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-secondary btn-xs" href="#" onclick="showDetailForm(\'' + row.Id + '\')"><i class="badge badge-warning"></i>详情</a>&nbsp;');
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#Gender").ysComboBox('setValue', -1);
        $("#IsExperimenter").ysComboBox('setValue', -1);
        $("#ExperimenterNature").ysComboBox('setValue', -1);
        $("#JobTitle").ysComboBox('setValue', -1);
        $("#SubjectId").ysComboBox('setValue', -1);
        $("#RealName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd, id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError('每次只能修改一条数据，请选择一条数据！');
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        var url = '@Url.Content("~/BusinessManage/UserReport/Form")' + '?id=' + id;
        createMenuItem(url, bAdd ? "统计人员添加" : "统计人员修改");
    }
    function showDetailForm(id) {
        ys.openDialog({
            title: "人员信息详情",
            content: '@Url.Content("~/BusinessManage/UserReport/DetailForm")' + '?id=' + id,
            callback: function (index, layero) {
                layer.close(index);
            }
        });
    }
    function deleteForm(id) {
        var ids = "";
        var msg = "";
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                msg = '确认要删除选中的' + selectedRow.length + '条数据吗？';
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgError("请选择需要删除的数据。");
                return;
            }
        } else {
            msg = "确认要删除当前这条数据吗？";
            ids = id;
        }
        ys.confirm(msg, function () {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserReport/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    function removeConfirm() {
        $(".layui-layer-btn0").hide();
    }
</script>
