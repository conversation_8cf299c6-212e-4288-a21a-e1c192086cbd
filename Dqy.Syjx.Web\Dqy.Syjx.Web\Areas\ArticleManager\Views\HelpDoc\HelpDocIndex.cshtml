﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{



    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.layout/1.4.4/jquery.layout-latest.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
  
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/ckfinder/ckfinder.js"),true)
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/ckeditor/ckeditor.js"),true)
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/ckeditor/adapters/jquery.js"),true)
}


<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

</style>
<div class="ui-layout-west">
    <div class="main-content">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    页面菜单
                </div>
                <div class="box-tools pull-right">
                    <a type="button" class="btn btn-box-tool menuItem" href="#" onclick="downloadHelp()" title="导出">导出<i class="fa fa-mail-forward"></i></a>
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新菜单"><i class="fa fa-refresh"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div id="departmentTree" class="ztree"></div>
            </div>
        </div>
    </div>
</div>

<div class="container-div ui-layout-center">

        <div id="divMain" class="wrapper animated fadeInRight" style="display:none;">
            <form id="form" class="form-horizontal m">
                <div class="form-group row">
                    <input type="hidden" id="Id" col="Id" value="0" />
                   
                    <input type="hidden" id="MenuUrl" col="MenuUrl" value="" />
                <label class="col-sm-2 control-label ">菜单名称<font class="red"> *</font></label>
                    <div class="col-sm-10">
                        <input id="MenuName" col="MenuName" type="text" class="form-control" />
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">帮助内容</label>
                    <div class="col-sm-10">
                        <textarea id="Remark" col="Remark" class="form-control" autocomplete="off"></textarea>
                   
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-2 control-label ">状态</label>
                    <div class="col-sm-10">
                        <div class="col-sm-4" id="Status" col="Status"></div>
                    </div>
                </div>
            <div class="form-group row">
                <div class="col-sm-12" style="text-align:center;">
                    <a id="btnAdd" class="btn btn-success" onclick="saveForm()"><i class="fa fa-save"></i> 保存</a>
                </div>
            </div>
            </form>


        <div id="searchDiv" class="col-sm-12 search-collapse" style="display:none;">
            <input type="hidden" id="SysMenuId" col="SysMenuId" value="0">
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="showSaveForm(true);"><i class="fa fa-plus"></i> 新增</a>
        </div>
        

            <div class="col-sm-12 select-table table-striped" style="margin-top:20px;">
                <table id="gridTable" data-mobile-responsive="true"></table>
            </div>

        </div>
</div>


<script type="text/javascript">

    var editor;

    $(function () {

        createEditor();

        showAddImpByThird();
        $("#Status").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())) });
      
        initTree();

        $('body').layout({ west__size:260 });

        $('#btnExpand').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(true);
            $(this).hide();
            $('#btnCollapse').show();
        });

        $('#btnCollapse').click(function () {
            var tree = $.fn.zTree.getZTreeObj("departmentTree");
            tree.expandAll(false);
            $(this).hide();
            $('#btnExpand').show();
        });


        $('#btnRefresh').click(function () {
            initTree();
        });

        initGrid();
    });


    function createEditor() {
        if (editor){
            editor.destroy();
        }
        editor = CKEDITOR.replace('Remark');
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/ArticleManager/HelpItem/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'ShowOrder asc',
            columns: [
               
                { field: 'ItemContent', title: '内容', width: 200, halign: 'center', align: 'center', sortable: true, },
                { 
                    field: 'ItemPath', title: '图片/视频查看', width: 100, halign: 'center', align: 'center', sortable: true,
                    formatter: function (value, row, index) {
                        var html="";
                        var filePath = value;
                        var fileExt = filePath.substring(filePath.lastIndexOf('.')).toUpperCase();
                        var fileTitle = filePath.substring(filePath.lastIndexOf('/')+1);
                        if (fileExt == ".JPG" || fileExt == ".BMP" || fileExt == ".JPEG" || fileExt == ".PNG" || fileExt == ".GIF") {
                                html='<span class="keywords" > <a  modal="zoomImg"  href="javascript:void(0);" src="' + filePath + '" >查看</a></span>';
                        } else {
                                html='<span class="keywords"> <a target="_blank" href="' + filePath + '" >查看</a></span>';
                        }
                        return html;
                    }
                },
                { 
                    field: 'FileType', title: '类型', halign: 'center', align: 'center', width: 60, sortable: true,
                    formatter: function (value, row, index){
                        var html = "图片";
                        if(value == 2){
                            html = "视频";
                        }
                        return html;
                    }
                },
                { field: 'ShowOrder', title: '排序', halign: 'center', align: 'center', width: 60, sortable: true, },
                {
                    field: 'Status', title: '状态', halign: 'center', align: 'center', width: 60, sortable: true, formatter: function (value, row, index) {
                        if (row.Status == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    halign: 'center', align: 'center', width: 150,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.Status == "@StatusEnum.Yes.ParseToInt()") {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'禁用\')"><i class="fa fa-check"></i>禁用</a>&nbsp;&nbsp;');
                        } else {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="ChangeStatuz(\'' + row.Id + '\',\'启用\')"><i class="fa fa-check"></i>启用</a>&nbsp;&nbsp;');
                        }
                        actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="DelItem(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;');
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $("#searchDiv").getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function (data) {
                imgages.showextalink();
            }
        });
    }

    function searchGrid() {
       $('#gridTable').ysTable('search');
    }

    function initTree() {
        $('#departmentTree').ysTree({
            url: '@Url.Content("~/SystemManage/Menu/GetDocMenuTreeListJson")',
            async: true,
            expandLevel: 2,
            maxHeight: "700px",
            callback: {
                onClick: function (event, treeId, treeNode) {
                    var listChildren = treeNode.children;
                    if (listChildren == undefined){
                        $("#divMain").show();
                        var id = treeNode.id;
                        var menuName = treeNode.name;
                        var menuUrl = treeNode.menuUrl;
                        //console.log("treeId:" + treeId);
                        //console.log("treeNode:" + JSON.stringify(treeNode));
                        loadHelpDocInfo(id, menuName, menuUrl);
                        //initGrid(id);
                        $("#SysMenuId").val(id);
                        searchGrid();
                    }else{
                        
                        $("#divMain").hide();

                    }
                }
            }
        });
    }

    function showSaveForm(){
        var sysMenuId = $("#SysMenuId").val();
        var id = $("#Id").val();
        if (id == 0) {
            //ys.msgError("请先保存帮助内容");
            //return;
            var postData = $('#form').getWebControls({ Id: id });
            if (postData.MenuName == "") {
                ys.msgError("菜单名称不能为空");
                return;
            }
            var remark = editor.getData();
            postData.SysMenuId = sysMenuId;
            postData.Remark = remark;

            ys.ajax({
                url: '@Url.Content("~/ArticleManager/HelpDoc/SaveFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#Id").val(obj.Data);
                        id = $("#Id").val();

                        ys.openDialog({
                            title: "添加帮助条目",
                            content: '@Url.Content("~/ArticleManager/HelpDoc/HelpDocForm")' + '?id=' + id + '&sysMenuId=' + sysMenuId,
                            callback: function (index, layero) {
                                var iframeWin = window[layero.find('iframe')[0]['name']];
                                iframeWin.saveForm(index);
                            }
                        });
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }else{
            ys.openDialog({
                title: "添加帮助条目",
                //height: "600px",
                content: '@Url.Content("~/ArticleManager/HelpDoc/HelpDocForm")' + '?id=' + id + '&sysMenuId=' + sysMenuId,
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }
       
    }


    function showAddImpByThird() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/IsThirdUser")',
            type: "get",
            error: ys.ajaxError,
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data == 1) {
                        $("#btnAdd").remove();
                        $("#btnImport").remove();
                    }
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function loadHelpDocInfo(menuId, menuName, menuUrl) {
        ys.ajax({
            url: '@Url.Content("~/ArticleManager/HelpDoc/GetListJson")' + '?SysMenuId=' + menuId,
            type: "get",
            success: function (obj) {
                if (obj.Total > 0) {
                    var result = obj.Data[0];
                    //console.log("result:" + JSON.stringify(result));
                    $("#form").setWebControls(result);
                    //设置富文本框值
                    editor.setData(result.Remark);
                    
                }else{
                    var defaultData = {};
                    defaultData.Id = 0;
                    defaultData.SysMenuId = menuId;
                    defaultData.MenuName = menuName;
                    defaultData.MenuUrl = menuUrl;
                    //defaultData.Remark="";
                    defaultData.Status = "@StatusEnum.Yes.ParseToInt()";
                    $("#form").setWebControls(defaultData);
                    editor.setData("");
                }
            }

        });
        $("#form").validate({
            rules: {
                MenuName: { required: true },
                Remark: { required: true },
            }
        });
    }


    function saveForm(index) {

        var id = $("#Id").val();
        var postData = $('#form').getWebControls({ Id: id });
        if (postData.MenuName == ""){
            ys.msgError("菜单名称不能为空");
            return;
        }
        var SysMenuId = $("#SysMenuId").val();
        var remark =editor.getData();
        //if (remark==""){
        //    ys.msgError("帮助内容不能为空");
        //    return;
        //}
        //console.log("remark:" + remark);
        postData.SysMenuId = SysMenuId;
        //SetCKEditor("Remark",remark);
        postData.Remark = remark;
        //console.log("postData:" + JSON.stringify(postData));

        ys.ajax({
            url: '@Url.Content("~/ArticleManager/HelpDoc/SaveFormJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                //console.log("obj:"+JSON.stringify(obj));
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    $("#Id").val(obj.Data);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }


    //function SetCKEditor(ckname, data) {
    //    CKEDITOR.instances[ckname].on('instanceReady', function (event) {
    //        var _data = (data || "");
    //        if (_data != "") {
    //            this.setData(HTMLEncode(_data));
    //        }
    //        this.document.on("paste", function (e) {//重写该ckeditor实例的粘贴事件
    //            var items = e.data.$.clipboardData.items;//获取该ckeditor实例的所有剪切板数据
    //            for (var i = 0; i < items.length; ++i) {//循环该数据并只获取类型为image/png格式的数据
    //                var item = items[i];
    //                if (item.kind == 'file' && item.type == 'image/png') {
    //                    var imgFile = item.getAsFile();
    //                    if (!imgFile) {
    //                        return true;
    //                    }
    //                    var reader = new FileReader();
    //                    reader.readAsDataURL(imgFile);//转化为base64格式
    //                    reader.onload = function (e) {//在控件中插入该图片
    //                        CKEDITOR.instances["description"].insertHtml('<img src="' + this.result + '" alt="" />');
    //                    }
    //                    return false;
    //                }
    //            }
    //        });
    //    });
    //}


    function DelItem(id){
        ys.confirm("确定要删除吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/HelpItem/DeleteFormJson")' + '?ids=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }, function () {
        });
    }

    function ChangeStatuz(id,str){
        ys.confirm("确定要"+str+"吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/ArticleManager/HelpItem/EditStatuzFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }, function () {
        });
    }

    function downloadHelp() {
        var url = '@Url.Content("~/ArticleManager/HelpDoc/ExportHelp")';
        location.href = url;
    }
</script>
