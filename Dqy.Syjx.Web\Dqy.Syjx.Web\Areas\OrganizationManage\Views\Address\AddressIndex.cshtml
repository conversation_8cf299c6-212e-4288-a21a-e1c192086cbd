﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.js"))

<div class="container-div">
    <div class="row" style="height:auto;">
       @* <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-down"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、排序号是用于地点排列前后次序，值越小越靠前；<br />
                            2、导入后，请设置所属部门和排序号；<br />
                            3、所属部门是用于部门资产统计。
                        </div>
                    </div>
                </div>
            </div>
        </div>*@
    </div>
    <div class="row">
        @* <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="HourseId" col="HourseId" style="display: inline-block;"></div>
                    </li>
                    <li>
                        <div id="DepartmentIds" col="DepartmentIds" style="display: inline-block;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchTreeGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a id="btnSearch" class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>*@
        <div class="btn-group d-flex" role="group" id="toolbar">
      
            <span class="btn btn-success" onclick="showSaveHouseForm(true,0)"><i class="fa fa-plus"></i> 添加楼宇（场地）</span>
            <span class="btn btn-success" onclick="showSaveRoomForm(true,0)"><i class="fa fa-plus"></i> 添加场所（室）</span>
            @* <a class="btn btn-primary" onclick="showEditHourseForm()"><i class="fa fa-edit"></i> 修改楼宇</a>
            <a class="btn btn-primary" onclick="showEditForm(false)"><i class="fa fa-edit"></i> 修改场所</a>*@
            <span class="btn btn-danger" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 批量删除</span>
            <span class="btn btn-primary" onclick="showDepartmentForm()"><i class="fa fa-edit"></i>批量设置所属部门</span>
            <span class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i>批量导入</span>
            <!--帮助文档需要内容,id值必须为“helpBtn”-->
            <div class="fa fa-question-circle" id="helpBtn" ></div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {

        initTreeGrid();
        
        
    });

    function initTreeGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/Address/GetAddressListJson")';
         var options = {
            code: "Id",
            parentCode: "Pid",
            uniqueId: "Id",
            expandAll: false,
            expandFirst: true,
            toolbar: '#toolbar',
            expandColumn: '2',
            url: queryUrl,
            columns: [
                { field: 'selectItem', checkbox: true},
                {
                    field: 'opt1', title: '操作', align: 'left', width: '200px',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Depth == 0) {
                            html += $.Format('<a class="btn btn-link btn-xs" style="color:#537BF0;" href="#"onclick="showAddForm(\'{0}\',\'{1}\');"><i class="fa fa-plus"></i>添加场所（室）</a> ', row.Id, row.Name);
                            html += $.Format('<a class="btn btn-link btn-xs" style="color:#537BF0;" href="#"onclick="showRoomForm(\'{0}\');"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                        } else {
                            html += $.Format('<a class="btn btn-link btn-xs" style="color:#537BF0;" href="#"onclick="showSaveRoomForm(true,\'{0}\');"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                        }
                        html += $.Format('<a class="btn btn-link btn-xs" style="color:#537BF0;" href="#" onclick="deleteForm(\'{0}\')"><i class="fa fa-remove"></i>删除</a> ', row.Id);

                        return html;
                    }
                },
                { 
                    field: 'Name', title: '地点信息', width:'260px',align: 'center',
                    formatter: function (value, row, index) {
                        var html = value;
                        if (row.AliasName != null && row.AliasName != ""){
                            html = $.Format("{0}【{1}】", value, row.AliasName);
                        }
                        return html;
                    }
                },
                { field: 'Sort', title: '排序号', align: 'left', width:'80px'},
                { 
                    field: 'DepartmentName', title: '所属部门',align: 'left', width:'150px',
                    formatter: function (value, row, index) {
                        var html = value;
                        if (row.Depth == 0){
                            html = "";
                        }
                        return html;
                    }
                },
               
            ],
            onCheck: function (row, el) {
                //var datas = $table.bootstrapTable('getData');
                console.log(1111)
                //$(el).attr("checked", "checked");

                // 勾选子类
                //selectChilds(datas, row, "id", "pid", true);
                // 勾选父类
                //selectParentChecked(datas, row, "id", "pid", true);
                // 刷新数据
                //$table.bootstrapTable('load', datas);
            },
            onLoadSuccess: function () {

            }
        };

        $('#gridTable').ysTreeTable(options);
    }



    function searchTreeGrid() {
        var param = $("#searchDiv").getWebControls();
        $('#gridTable').ysTreeTable('search', param);
    }

    function resetGrid() {
        $("#HourseId").ysComboBox('setValue', -1);
        $("#DepartmentIds").ysComboBoxTree('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showEditForm(bAdd) {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (!ys.checkRowEdit(selectedRow)) {
            //每次只能选择一个数据修改
            ys.msgError("请选择一条修改，不可批量操作。");
            return;
        }
        else {
            if (selectedRow[0].Pid != undefined && selectedRow[0].Pid > 0) {
                showSaveRoomForm(bAdd, 0);
            } else {
                showSaveHouseForm(bAdd,0);
            }
        }
    }


    function showEditHourseForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (!ys.checkRowEdit(selectedRow)) {
            //每次只能选择一个数据修改
            ys.msgError("请选择一条修改，不可批量操作。");
            return;
        }
        else {
            var id = 0;
            if (selectedRow[0].Pid != undefined && selectedRow[0].Pid > 0) {
                id = selectedRow[0].Pid;
            } else {
                id = id = selectedRow[0].Id;
            }
            showSaveHouseForm(true, id);
        }
    }


    function showSaveHouseForm (bAdd,id) {
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError("请选择一条修改，不可批量操作。");
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑楼宇（场所）' : '添加楼宇（场所）',
            content: '@Url.Content("~/OrganizationManage/Address/HouseForm")' + '?id=' + id,
            width: '768px',
            height: '240px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }


    function showSaveRoomForm(bAdd, id) {
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError("请选择一条修改，不可批量操作。");
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑场所（室）' : '添加场所（室）',
            content: '@Url.Content("~/OrganizationManage/Address/RoomForm")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(id) {
        if(id == 0){
            var selectedRow = $("#gridTable").bootstrapTreeTable("getSelections");
            if (selectedRow.length == 0) {
                ys.msgError("请至少选择一项删除。");
                return;
            }
             ys.confirm("确认要删除选中的" + selectedRow.length + "条数据吗？", function () {
            var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/Address/DeleteByIds")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchTreeGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }else{
             ys.confirm("确认要删除吗？", function () {
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/Address/DeleteByIds")' + '?ids=' + id,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchTreeGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
        

       
    }
     

    function showDepartmentForm() {

        var selectedRow = $("#gridTable").bootstrapTreeTable("getSelections");
        if (selectedRow.length == 0) {
            ys.msgError("请至少选择一项设置。");
            return;
        }
        var ids = ys.getIds(selectedRow);
        ys.openDialog({
            title: '设置部门',
            content: '@Url.Content("~/OrganizationManage/Address/SetDepartmentForm")' + '?ids=' + ids,
            width: '768px',
            height: '240px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showAddForm(id,name){
         ys.openDialog({
            title: '添加场所室',
            content: '@Url.Content("~/OrganizationManage/Address/AddRoom")' + '?id=' + id+'&name='+name,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showRoomForm(id) {
        ys.openDialog({
            title: '修改楼宇场地',
            content: '@Url.Content("~/OrganizationManage/Address/HouseForm")' + '?id=' + id,
            width: '768px',
            height: '240px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function importForm() {
        ys.openDialog({
            title: "导入地址数据",
            content: '@Url.Content("~/OrganizationManage/Address/AddressImport")',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

</script>
