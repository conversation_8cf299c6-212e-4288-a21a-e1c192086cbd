﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="purchaseYear" col="PurchaseYear" style="display:inline-block;width:90px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="StageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                   
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
       @*  <div class="btn-group d-flex" role="group" id="toolbar">
            <span id="schoolAvgTip"></span>
            <a id="btnLookBySchool" class="btn btn-success btn-sm" onclick="switchLookType()"><i class="fa fa-navicon"></i>&nbsp;按学校查看</a>
            <a id="btnLookByDetail" class="btn btn-success btn-sm" onclick="lookByDetail()"><i class="fa fa-navicon"></i>&nbsp;按明细查看</a>
            <a id="btnLookBySchoolHz" class="btn btn-success btn-sm" onclick="lookBySchoolHz()"><i class="fa fa-navicon"></i>&nbsp;按学校汇总查看</a>
        </div> *@
        <div class="col-sm-12 select-table table-striped">            
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>


<script type="text/javascript">

    var listLookType = 1; //列表查看类型，1：按学校查看，2：按区县查看
    var SortName = '';  //列表默认排序
    var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetPurchaseGroupListJson")'; //查询方法

    $(function () {

        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            loadSchool(0);
            $('#liSchoolId').show();
            SortName = 'SchoolSort asc ,SchoolId asc';
        }
        else if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            listLookType = 2;
            loadCounty();
            $('#liCountyId').show();
            SortName = 'CountySort asc ,CountyId asc';
            queryUrl  = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetPurchaseCityGroupListJson")'; //查询方法
        }
        else {
            SortName = 'StageId asc ,CourseId asc';
        }

        getYear();
        loadSchoolStage();
        loadSubject();

        initGrid();
        
        
        loadStudentAvgAmountMsg($('#purchaseYear').ysComboBox('getValue'));
    });

    function initGrid() {
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: SortName,
            columns: [
                {
                    field: 'opt', title: '仪器明细', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt1, visible: (listLookType == 1),
                    formatter: function (value, row, index) {
                        return row.CourseId ? $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(this)" purchaseyear="{0}" schoolid="{1}" countyid="{2}" stageid="{3}" courseid="{4}"><i class="fa fa-eye"></i> 查看</a> '
                            , row.PurchaseYear, row.SchoolId, row.CountyId, row.StageId, row.CourseId) : '';
                    }
                },
                {
                    field: 'AreaName', title: '区县名称', sortable: true, halign: 'center', align: 'center', visible: (@UnitType == @UnitTypeEnum.City.ParseToInt()), width: 100,
                    formatter: function (value, row, index) {
                        if (@UnitType == @UnitTypeEnum.City.ParseToInt() && !row.Course) {
                            return '<b>总计（元）：</b>';
                        }
                        else {
                            return value || '';
                        }
                    }
                },
                {
                    field: 'SchoolName', title: '单位名称', sortable: true, halign: 'center', align: 'left', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt() && listLookType == 1), width: 150,
                    formatter: function (value, row, index) {
                        if (@UnitType == @UnitTypeEnum.County.ParseToInt() && !row.Course) {
                            return '<b>总计（元）：</b>';
                        }
                        else {
                            return value || '';
                        }
                    }
                },
                {
                    field: 'PurchaseYear', title: '采购年度', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName,
                    formatter: function (value, row, index) {
                        if (@UnitType == @UnitTypeEnum.School.ParseToInt() && !row.Course) {
                            return '<b>总计（元）：</b>';
                        }
                        else {
                            return value || '';
                        }
                    }
                },
                {
                    field: 'Stage', title: '学段', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.StageName,
                    formatter: function (value, row, index) {
                        return value || '';
                    }
                },
                {
                    field: 'Course', title: '学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course,
                    formatter: function (value, row, index) {
                        return value || '';
                    }
                },
                {
                    field: 'AmountSum', title: '采购金额', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        return row.CourseId ? ComBox.ToLocaleString(value) : '<b>' + ComBox.ToLocaleString(value); + '</b>';
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
        loadStudentAvgAmountMsg($('#purchaseYear').ysComboBox('getValue'));
    }

    function resetGrid() {
        //清空条件
        if (@UnitType != @UnitTypeEnum.School.ParseToInt()) {
            $('#schoolId').ysComboBox('setValue', -1);
            if (@UnitType == @UnitTypeEnum.County.ParseToInt()) 
            {
                loadSchool(0);
            }
        }
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', -1);
        }
        $("#schoolStageId").ysComboBox('setValue', -1);
        $("#courseId").ysComboBox('setValue', -1);
        $("#purchaseYear").ysComboBox('setValue', new Date().getFullYear());
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
        loadStudentAvgAmountMsg($('#purchaseYear').ysComboBox('getValue'));
    }

    function loadSchool(schoolStageId) {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId="+schoolStageId,
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });
                }
            }
        });
    }

    function loadCounty() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#countyId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '区县名称',
                        onChange: function () {
                            if (listLookType == 1)
                                loadSchoolByCountyId($('#countyId').ysComboBox('getValue'));
                        }
                    });
                }
            }
        });
    }

    function loadSchoolByCountyId(countyId) {
        if (countyId > 0) {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                data: null,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data) {
                        $('#schoolId').ysComboBox({
                            data: obj.Data,
                            key: 'Id',
                            value: 'Name',
                            defaultName: '单位名称'
                        });
                    }
                }
            });
        }
        else {
            $('#schoolId').ysComboBox({ defaultName: '单位名称' });
        }
    }

    function getYear() {
        var currDate = new Date();
        var currYear = currDate.getFullYear();
        var yearData = [];
        for (i = 0; i < 10; i++) {
            if (currYear + 1 - i < 2021)
                continue;
            yearData.push({ "id": currYear + 1 - i, "text": currYear + 1 - i });
        }
        $("#purchaseYear").ysComboBox({
            data: yearData,
            key: "id",
            value: "text",
            defaultName: '采购年度'
        }).ysComboBox('setValue', currYear);
    }

    function loadStudentAvgAmountMsg(purchaseYear) {
        if (@UnitType == @UnitTypeEnum.School.ParseToInt()) {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetStudentAvgAmountMsgJson?")' + 'purchaseYear=' + purchaseYear,
                data: null,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $('#schoolAvgTip').html(obj.Data);
                    }
                    else {
                        ys.alertWarning(obj.Message);
                    }
                }
            });
        }
    }

    //查看达标明细
    function lookForm(obj){
        var schoolId = $(obj).attr('schoolid');
        var countyId = $(obj).attr('countyid');
        var stageId = $(obj).attr('stageid');
        var courseId = $(obj).attr('courseid');
        var purchaseYear = $(obj).attr('purchaseyear');

        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseStatisticsIndex")' + '?purchaseYear='+ purchaseYear +'&schoolId=' + schoolId + '&countyId=' + countyId + '&stageId=' + stageId + '&courseId=' + courseId;
        createMenuItem(url, "查看仪器明细");
    }

    //市级查询时，切换按学校/区县查看
    function switchLookType() {
        //列表查看类型，1：按学校查看，2：按区县查看
        if (listLookType == 1) {
            listLookType = 2;
            SortName = 'CountySort asc ,CountyId asc';
            queryUrl = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetPurchaseCityGroupListJson")'; //查询方法
            $('#liSchoolId').hide();
            $('#btnLookBySchool').html('<i class="fa fa-navicon"></i>&nbsp;按学校查看');
        }
        else {
            listLookType = 1;
            SortName = 'CountySort asc ,CountyId asc ,SchoolSort asc ,SchoolId asc';
            queryUrl = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetPurchaseGroupListJson")'; //查询方法
            $('#liSchoolId').show();
            loadSchoolByCountyId($('#countyId').ysComboBox('getValue'));
            $('#btnLookBySchool').html('<i class="fa fa-navicon"></i>&nbsp;按区县查看');
        }
        $('#gridTable').bootstrapTable('destroy');
        initGrid();
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                    var schoolStageId = $('#schoolStageId').ysComboBox("getValue");
                    if (schoolStageId > 0) {
                        loadSchool(schoolStageId);
                    }
                }
            }
        });
    }

    function loadSubject() {
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function lookByDetail() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseStatisticsIndex")' + '?purchaseYear=' + $('#purchaseYear').ysComboBox('getValue');
        createMenuItem(url, "计划明细查看");
    }

    function lookBySchoolHz() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/PurchaseSchoolGroupStatisticsIndex")';
        createMenuItem(url, "按学校汇总查看");
    }
</script>
