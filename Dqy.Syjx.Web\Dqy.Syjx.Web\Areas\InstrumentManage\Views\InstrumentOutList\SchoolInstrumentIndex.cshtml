﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <div id="storagePlace" col="StoragePlace" style="display:inline-block;"></div>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="display:inline-block;width:150px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        loadCourse();
        loadStoragePlace();

        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/InstrumentOutList/GetOutListPageJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                //{ field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80 },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt2,
                    formatter: function (value, row, index) {
                        var html = '';
                        html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="outForm(this)" value="{0}"><i class="fa fa-edit"></i>出库</a> ', row.Id);
                        return html;
                    }
                },

                { field: 'Course', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        if (row.IsSelfMade == 1) {
                            html += Syjx.GetSelfMadeHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'StockNum', title: '数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },  //取剩余库存
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'Price', title: '单价', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    }
                },
                
                {
                    field: 'FunRoom', title: '存放地', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.FunRoom,
                    formatter: function (value, row, index) {
                        if (row.Cupboard) {
                            value += '>' + row.Cupboard;
                        }
                        if (row.Floor) {
                            value += '>' + row.Floor;
                        }
                        return value;
                    }
                },
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function outForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '出库',
            height:"500px",
            content: '@Url.Content("~/InstrumentManage/InstrumentOutList/InstrumentOutListForm")' + '?schoolInstrumentId=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });
    }

    function loadStoragePlace() {
        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListAllJson")',
            class: "form-control",
            key: 'id',
            value: 'name',
            defaultName: '存放地'
        });
        $('#storagePlace').ysComboBoxTree('setValue', -1);
    }

    function resetGrid() {
        //清空条件
        $('#courseId').ysComboBox('setValue', -1);
        $('#storagePlace').ysComboBoxTree('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>