﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container { width: 100% !important; }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    @await Html.PartialAsync("/Areas/OrganizationManage/Shared/SchoolStageGradeIndexPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li> 
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>
        <div id="toolbar" class="btn-group d-flex" role="group">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 创建班级</a>
            <a id="btnEdit" class="btn btn-primary" onclick="showEditForm(0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>
            <a class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'UnitCode', title: '单位编码', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'UnitName', title: '单位名称', width: 200, sortable: true, halign: 'center', valign: 'middle'},
                { field: 'SchoolStageName', title: '学段', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'StartYear', title: '入学年份', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'GradeName', title: '年级', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'ClassDesc', title: '班级', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center'},
                { field: 'StudentNum', title: '学生数', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'opt1', title: '操作', width: 120, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="showEditForm(\'{0}\');"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'{0}\');"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                        }
                        return html;
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#SchoolStage").ysComboBox("setValue", -1);
        $("#GradeId").ysComboBox("setValue", -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showEditForm(id) {
        if (id==0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError("请选择需要修改的数据。");
                return;
            }
            else {
                if (selectedRow.length > 1) {
                    ys.msgError("请选择一条修改，不可批量操作。");
                    return;
                }
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: '编辑',
            content: '@Url.Content("~/OrganizationManage/SchoolGradeClass/EditClassForm")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSaveForm() {
        ys.openDialog({
            title: '创建班级',
            content: '@Url.Content("~/OrganizationManage/SchoolGradeClass/CreateClassSysForm")',
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(id) {
        var ids = id + '';
        var message = "确认要删除当前数据吗？";
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                message = '确认要删除选中的' + selectedRow.length + '条数据吗？';
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgSuccess('请选择需要删除的数据！');
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    function loadClassInfo() {

    }
    function importForm() {
      ys.openDialog({
            title: "导入班级数据",
            content: '@Url.Content("~/OrganizationManage/SchoolGradeClass/ClassImport")',
            height: "280px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
