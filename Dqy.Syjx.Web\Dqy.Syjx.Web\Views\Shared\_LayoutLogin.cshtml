﻿<!--登录页和首页模板-->
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@{ var loginCfg = await Dqy.Syjx.Web.CommonLib.Configs.GetWebTitle(); }
<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />

    <meta name="keywords" content="@loginCfg">
    <meta name="description" content="@loginCfg">

    <link rel="bookmark" href='@Url.Content("~/favicon.ico")' />
    <link rel="shortcut icon" href="@Url.Content("~/favicon.ico")" type="image/x-icon" />

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap/css/bootstrap.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fontawesome/4.7.0/css/fontawesome.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery/3.7.1/jquery.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery/3.7.1/jquery-migrate-3.4.0.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap/js/bootstrap.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/layer/3.1.1/layer.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/style.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha.min.js"))

    <!--首页需要用到的js开始-->
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha-index.min.js"))
    <script type="text/javascript" src='@Url.Content("~/lib/jquery.metisMenu/1.1.3/metisMenu.js")'></script>
    <script type="text/javascript" src='@Url.Content("~/lib/jquery.slimscroll/1.3.8/jquery.slimscroll.min.js")'></script>
    <!--首页需要用到的js结束-->
    @RenderSection("header", false)
<title>@loginCfg</title>
    <style type="text/css">
        .nav > li:hover .dropdown-menu {
            display: block;
        }
    </style>
    <script type="text/javascript">
        var ctx = '@Url.Content("~/")';
    </script>
</head>
<body>
    @RenderBody()
    @RenderSection("footer", false)

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/yisha.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha-init.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.string.format/jquery.string.format.min.js"))
</body>
</html>
