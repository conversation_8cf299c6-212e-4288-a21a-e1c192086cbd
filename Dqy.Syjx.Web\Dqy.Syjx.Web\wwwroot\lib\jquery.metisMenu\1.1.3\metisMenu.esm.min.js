/*!
* metismenu https://github.com/onokumus/metismenu#readme
* A collapsible jQuery menu plugin
* @version 3.0.7
* <AUTHOR> <<EMAIL>> (https://github.com/onokumus)
* @license: MIT 
*/
import $ from"jquery";const Util=(e=>{const t="transitionend",s={TRANSITION_END:"mmTransitionEnd",triggerTransitionEnd(s){e(s).trigger(t)},supportsTransitionEnd:()=><PERSON>olean(t)};function n(t){let n=!1;return e(this).one(s.TRANSITION_END,(()=>{n=!0})),setTimeout((()=>{n||s.triggerTransitionEnd(this)}),t),this}return e.fn.mmEmulateTransitionEnd=n,e.event.special[s.TRANSITION_END]={bindType:t,delegateType:t,handle(t){if(e(t.target).is(this))return t.handleObj.handler.apply(this,arguments)}},s})($),NAME="metisMenu",DATA_KEY="metisMenu",EVENT_KEY=".metisMenu",DATA_API_KEY=".data-api",JQUERY_NO_CONFLICT=$.fn[NAME],TRANSITION_DURATION=350,Default={toggle:!0,preventDefault:!0,triggerElement:"a",parentTrigger:"li",subMenu:"ul"},Event={SHOW:"show.metisMenu",SHOWN:"shown.metisMenu",HIDE:"hide.metisMenu",HIDDEN:"hidden.metisMenu",CLICK_DATA_API:"click.metisMenu.data-api"},ClassName={METIS:"metismenu",ACTIVE:"mm-active",SHOW:"mm-show",COLLAPSE:"mm-collapse",COLLAPSING:"mm-collapsing",COLLAPSED:"mm-collapsed"};class MetisMenu{constructor(e,t){this.element=e,this.config={...Default,...t},this.transitioning=null,this.init()}init(){const e=this,t=this.config,s=$(this.element);s.addClass(ClassName.METIS),s.find(`${t.parentTrigger}.${ClassName.ACTIVE}`).children(t.triggerElement).attr("aria-expanded","true"),s.find(`${t.parentTrigger}.${ClassName.ACTIVE}`).parents(t.parentTrigger).addClass(ClassName.ACTIVE),s.find(`${t.parentTrigger}.${ClassName.ACTIVE}`).parents(t.parentTrigger).children(t.triggerElement).attr("aria-expanded","true"),s.find(`${t.parentTrigger}.${ClassName.ACTIVE}`).has(t.subMenu).children(t.subMenu).addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`),s.find(t.parentTrigger).not(`.${ClassName.ACTIVE}`).has(t.subMenu).children(t.subMenu).addClass(ClassName.COLLAPSE),s.find(t.parentTrigger).children(t.triggerElement).on(Event.CLICK_DATA_API,(function(s){const n=$(this);if("true"===n.attr("aria-disabled"))return;t.preventDefault&&"#"===n.attr("href")&&s.preventDefault();const i=n.parent(t.parentTrigger),a=i.siblings(t.parentTrigger),r=a.children(t.triggerElement);i.hasClass(ClassName.ACTIVE)?(n.attr("aria-expanded","false"),e.removeActive(i)):(n.attr("aria-expanded","true"),e.setActive(i),t.toggle&&(e.removeActive(a),r.attr("aria-expanded","false"))),t.onTransitionStart&&t.onTransitionStart(s)}))}setActive(e){$(e).addClass(ClassName.ACTIVE);const t=$(e).children(this.config.subMenu);t.length>0&&!t.hasClass(ClassName.SHOW)&&this.show(t)}removeActive(e){$(e).removeClass(ClassName.ACTIVE);const t=$(e).children(`${this.config.subMenu}.${ClassName.SHOW}`);t.length>0&&this.hide(t)}show(e){if(this.transitioning||$(e).hasClass(ClassName.COLLAPSING))return;const t=$(e),s=$.Event(Event.SHOW);if(t.trigger(s),s.isDefaultPrevented())return;if(t.parent(this.config.parentTrigger).addClass(ClassName.ACTIVE),this.config.toggle){const e=t.parent(this.config.parentTrigger).siblings().children(`${this.config.subMenu}.${ClassName.SHOW}`);this.hide(e)}t.removeClass(ClassName.COLLAPSE).addClass(ClassName.COLLAPSING).height(0),this.setTransitioning(!0);t.height(e[0].scrollHeight).one(Util.TRANSITION_END,(()=>{this.config&&this.element&&(t.removeClass(ClassName.COLLAPSING).addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`).height(""),this.setTransitioning(!1),t.trigger(Event.SHOWN))})).mmEmulateTransitionEnd(350)}hide(e){if(this.transitioning||!$(e).hasClass(ClassName.SHOW))return;const t=$(e),s=$.Event(Event.HIDE);if(t.trigger(s),s.isDefaultPrevented())return;t.parent(this.config.parentTrigger).removeClass(ClassName.ACTIVE),t.height(t.height())[0].offsetHeight,t.addClass(ClassName.COLLAPSING).removeClass(ClassName.COLLAPSE).removeClass(ClassName.SHOW),this.setTransitioning(!0);const n=()=>{this.config&&this.element&&(this.transitioning&&this.config.onTransitionEnd&&this.config.onTransitionEnd(),this.setTransitioning(!1),t.trigger(Event.HIDDEN),t.removeClass(ClassName.COLLAPSING).addClass(ClassName.COLLAPSE))};0===t.height()||"none"===t.css("display")?n():t.height(0).one(Util.TRANSITION_END,n).mmEmulateTransitionEnd(350)}setTransitioning(e){this.transitioning=e}dispose(){$.removeData(this.element,DATA_KEY),$(this.element).find(this.config.parentTrigger).children(this.config.triggerElement).off(Event.CLICK_DATA_API),this.transitioning=null,this.config=null,this.element=null}static jQueryInterface(e){return this.each((function(){const t=$(this);let s=t.data(DATA_KEY);const n={...Default,...t.data(),..."object"==typeof e&&e?e:{}};if(s||(s=new MetisMenu(this,n),t.data(DATA_KEY,s)),"string"==typeof e){if(void 0===s[e])throw new Error(`No method named "${e}"`);s[e]()}}))}}$.fn[NAME]=MetisMenu.jQueryInterface,$.fn[NAME].Constructor=MetisMenu,$.fn[NAME].noConflict=()=>($.fn[NAME]=JQUERY_NO_CONFLICT,MetisMenu.jQueryInterface);export default MetisMenu;
//# sourceMappingURL=metisMenu.esm.min.js.map