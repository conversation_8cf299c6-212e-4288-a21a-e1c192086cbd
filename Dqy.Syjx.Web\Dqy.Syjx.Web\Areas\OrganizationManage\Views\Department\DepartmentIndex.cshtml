﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@section header{
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap.treetable/1.0/bootstrap-treetable.min.js"))
}

<div class="container-div">
   @* <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、组织结构是指单位内的构成部门；<br />
                            2、建立部门是用于人员的归属、设备仪器的归属及按部门查询统计。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        组织名称：<input id="departmentName" col="DepartmentName" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchTreeGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" role="group">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
            <a id="btnExpandAll" class="btn btn-info"><i class="fa fa-exchange"></i> 展开/折叠</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var id = 0;
    $(function () {
        initTreeGrid();
        
        
    });

    function initTreeGrid() {
        var options = {
            code: "Id",
            parentCode: "ParentId",
            uniqueId: "Id",
            expandAll: false,
            expandFirst: true,
            toolbar: '#toolbar',
            expandColumn: '1',
            url: '@Url.Content("~/OrganizationManage/Department/GetListJson")',
            modalName: "部门",
            columns: [
                { field: 'selectItem', radio: true },
                { field: 'DepartmentName', title: '组织名称', width: '20%' },
                {
                    field: 'Statuz', title: '状态', width: '10%', align: "left",
                    formatter: function (value, row, index) {
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'BaseModifyTime', title: '创建时间', width: '60%', align: "left",
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                }
            ],
            onLoadSuccess: function () {
                if (id != 0) {
                    $('#gridTable').ysTreeTable('expandRowById', id);
                }
            }
        };
        $('#gridTable').ysTreeTable(options);
    }

    function searchTreeGrid(callBackId) {
        var param = $("#searchDiv").getWebControls();
        $('#gridTable').ysTreeTable('search', param);
         if (callBackId) {
            id = callBackId;
        }
    }
    function resetGrid() {
        //清空条件
        $('#departmentName').val('');
        var param = $("#searchDiv").getWebControls();
        $('#gridTable').ysTreeTable('search', param);
        if (callBackId) {
            id = callBackId;
        }
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $("#gridTable").bootstrapTreeTable("getSelections");
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? "编辑组织架构" : "添加组织架构",
            content: '@Url.Content("~/OrganizationManage/Department/DepartmentForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $("#gridTable").bootstrapTreeTable("getSelections");
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm("确认要删除选中的" + selectedRow.length + "条数据吗？", function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/OrganizationManage/Department/DeleteFormJson")' + '?ids=' + ids,
                    type: "post",
                    error: ys.ajaxError,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchTreeGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }
</script>