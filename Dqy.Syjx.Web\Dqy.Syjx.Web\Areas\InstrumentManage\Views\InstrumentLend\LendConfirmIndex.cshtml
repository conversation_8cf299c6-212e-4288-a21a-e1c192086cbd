﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>借出时间： </label><input id="startDate" col="StartDate" type="text" class="time-input" placeholder="开始时间" style="width:100px" />
                        <span>-</span>
                        <input id="endDate" col="EndDate" type="text" class="time-input" placeholder="结束时间" style="width:100px" />
                    </li>
                    <li>
                        <span id="statuz" col="Statuz" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="width:150px" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>


<script type="text/javascript">
    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        $("#statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(InstrumentLendStatuzEnum).EnumToDictionaryString())) ,defaultName: '状态' });

        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/InstrumentLend/GetConfirmListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                //{ field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80 },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt2,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Statuz == @InstrumentLendStatuzEnum.WaitConfirm.ParseToInt()) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="confirm(this)" value="{0}" t="1"><i class="fa fa-edit"></i>确认</a> ', row.Id);
                        }
                        html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        return html;
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        if (row.IsSelfMade == 1) {
                            html += Syjx.GetSelfMadeHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'LendNum', title: '借用数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                { field: 'LendDay', title: '借用天数', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                {
                    field: 'BaseCreateTime', title: '借出时间', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (value) {
                            return value.substring(0, 10);
                        }
                    }
                },
                {
                    field: 'RevertTime', title: '归还时间', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (row.Statuz == @InstrumentLendStatuzEnum.AlreadyRevert.ParseToInt()) {
                            return value.substring(0, 10);
                        }
                        else {
                            return '--';
                        }
                    }
                },
                {
                    field: 'Statuz', title: '状态', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Statuz,
                    formatter: function (value, row, index) {
                        return row.StatuzDesc;
                    }
                },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function lookForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '查看',
            content: '@Url.Content("~/InstrumentManage/InstrumentLend/LendForm")' + '?id=' + id + '&isLook=1',
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function confirm(obj) {
        var id = $(obj).attr('value');
        ys.confirm('您确认借用吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/InstrumentLend/ConfirmForm")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#statuz').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function hideDialogButton() {
        $('.layui-layer-btn0').hide();
    }
</script>
