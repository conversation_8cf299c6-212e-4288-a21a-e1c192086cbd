﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="addTabDetailForm()"><i class="fa fa-street-view"></i>&nbsp;按区县查看</a>
                    </li>
                    <li>
                        <span id="funRoomEvaluateProjectId" col="FunRoomEvaluateProjectId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block; width:120px;"></span>
                    </li>
                    <li>
                        <span id="schoolId" col="SchoolId" style="display:inline-block; width:200px;"></span>
                    </li>
                   
                    <li>
                        <span id="level" col="Level" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="oneClassId" col="OneClassId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="指标名称" style="width:150px" />
                    </li>
                    <li>
                        <div id="isOnlyShowNoStandard" col="IsOnlyShowNoStandard" style="display:inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
         if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
             $('#liCountyId').show();
             loadCounty();
             loadSchool(0);
        }


        $('#isOnlyShowNoStandard').ysCheckBox({
            data: [{ Key: 1, Value: '只显示不达标' }]
        });

        loadFunRoomEvaluateProjectIdId();
        loadSchoolStage();
        loadSubject();
        loadOneClass();
        loadLevel();
        
        
    });

    function loadCounty() {
         $("#countyId").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            key: "Id",
            value: "Name",
            defaultName: '区县名称',
            onChange: function () {
                var countyId = $("#countyId").ysComboBox("getValue");
                loadSchool(countyId);
            }
         });
    }

    function loadSchool(countyId) {
            if (parseInt(countyId) == -1) {
                $("#schoolId").ysComboBox({
                    url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList?PageSize=10000")',
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称',
                });
                $(".select2-container").width("100%");
            } else if (parseInt(countyId) == 0) {
                $("#schoolId").ysComboBox({
                    data: [],
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称',
                });
                $(".select2-container").width("100%");
            } else if (parseInt(countyId) > 0)  {
                $("#schoolId").ysComboBox({
                    url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                    key: 'Id',
                    value: 'Name',
                    defaultName: '单位名称',
                });
                $(".select2-container").width("100%");
            }
    }

    function initGrid() {
        if (!$('#funRoomEvaluateProjectId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        var queryUrl = '@Url.Content("~/EvaluateManage/FunRoomAttendStatic/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: "CountyId ASC,StageId ASC, CourseId ASC",
            columns: [
                { field: 'CountyName', title: '区县名称', sortable: true, halign: 'center', align: 'left', width: 100  },
                { field: 'SchoolName', title: '单位名称', sortable: true, halign: 'center', align: 'left', width: 150  },
                { field: 'StageName', title: '学段', halign: 'center', align: 'center', sortable: true, width: 50  },
                { field: 'OneClassName', title: '一级分类', halign: 'center', align: 'center', sortable: true, width: 100  },
                { field: 'TargetName', title: '指标名称', halign: 'center', align: 'left', sortable: true, width: 140  },
                { field: 'CourseName', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: 50  },
                { field: 'StandardNum', title: '指标数量（间）', halign: 'center', align: 'center', sortable: true, width: 50  },
                { field: 'FunRoomNum', title: '实际数量（间）', halign: 'center', align: 'center', sortable: true, width: 50  },
                {
                    field: 'RoomNumDifference', title: '数量差额（间）', halign: 'center', align: 'center', sortable: true, width: 50 ,
                    formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                },
                { field: 'StandardArea', title: '指标面积（㎡）', halign: 'center', align: 'center', sortable: true, width: 80  },
                { field: 'FunRoomArea', title: '实际面积（㎡）', halign: 'center', align: 'center', sortable: true, width: 80  },
                {
                    field: 'AreaDifference', title: '面积差额（㎡）', halign: 'center', align: 'center', sortable: true, width: 80  ,
                    formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function loadFunRoomEvaluateProjectIdId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/GetProjectComboJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#funRoomEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        $('#funRoomEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    initGrid();
                }
            }
        });
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }

    function loadSubject() {
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadOneClass() {
        $("#oneClassId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.FunRoomClass.ParseToInt()&OptType=7&Pid=0',
            defaultName: '一级分类',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadLevel() {
        $('#level').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=@DicTypeCodeEnum.StandardLevel.ParseToInt()",
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '达标级别'
        });
    }

    function searchGrid() {
        if ($('#funRoomEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }

    function resetGrid() {
        //清空条件
        $('#schoolStageId').ysComboBox('setValue', -1);
        $('#level').ysComboBox('setValue', -1);
        $('#oneClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', -1);
            $('#schoolId').ysComboBox('setValue', -1);
        }
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function addTabDetailForm() {
        var url = '/EvaluateManage/FunRoomAttendStatic/CityFunRoomAttendStatic';
        createMenuItem(url, "按区县查看");
    }

</script>
