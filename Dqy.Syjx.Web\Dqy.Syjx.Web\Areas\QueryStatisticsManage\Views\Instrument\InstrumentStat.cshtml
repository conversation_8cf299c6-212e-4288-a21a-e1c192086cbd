﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))

<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>采购日期： </label>
                        <input id="startTime" col="StartTime" type="text" class="time-input" placeholder="开始时间" style="width:90px;" />
                        <span>至</span>
                        <input id="endTime" col="EndTime" type="text" class="time-input" placeholder="结束时间" style="width:90px;" />
                    </li>
                    <li>
                        <span id="attribute" col="Attribute" style="display: inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display: inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <div id="storagePlace" col="StoragePlace" style="display:inline-block;"></div>
                    </li>
                    <li>
                        <div id="SysDepartmentId" col="SysDepartmentId" style="display: inline-block;"></div>
                    </li>
                    <li>
                        <div id="SysUserId" col="SysUserId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <span id="isSelfMade" col="IsSelfMade" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input type="text" placeholder="仪器代码、名称" id="Name" col="Name" style="display: inline-block;width:100px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">
    var BasePageCode = 101036;//增加仪器统计(101036)
    $(function () {
        laydate.render({ elem: '#startTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        $("#isSelfMade").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), defaultName: '自制教具' });
        loadCombox();
        loadSysDepartment();
        loadSysUser(0);
        initGrid();
    });

    function loadCombox() {

        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });

        $('#attribute').ysComboBox({ url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1060", key: 'DictionaryId', value: 'DicName', defaultName: '仪器属性' });

        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListAllJson")',
            class: "form-control",
            key: 'id',
            value: 'name',
            defaultName: '存放地'
        });
        $('#storagePlace').ysComboBoxTree('setValue', -1);

        $(".select2-container").width("100%");
    }

    function loadSysDepartment() {
        $('#SysDepartmentId').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    if (typeof (treeNode) == "string") {
                        treeNode = JSON.parse(treeNode);
                    }
                    //loadSysUser(treeNode.id);
                }
            },
            defaultName: '管理部门'
        });
        $('#SysDepartmentId').ysComboBoxTree('setValue', -1);
    }

    function loadSysUser(departmentid) {
        /*根据部门获取部门下的人员。
         *
         */
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/GetUserListAll")' + '?DepartmentId=' + departmentid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#SysUserId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'RealName',
                        defaultName: '管理人'
                    });
                    if (obj.Data.length == 1) {
                        $("#SysUserId").ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                    $(".select2-container").width("100%");
                } else {
                    $('#SysUserId').ysComboBox({ data: [], key: 'Id', value: 'RealName' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/QueryStatisticsManage/Instrument/GetUserStatListJson")' + '?IsShowTotalRow=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                {
                    title: '操作',
                    align: 'center', width: commonWidth.Instrument.Opt2,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id)
                            html = '<a class="btn btn-info btn-xs" href="#" onclick="showView(this)" value="' + row.Id + '"><i class="fa fa-eye"></i>查看</a>';
                        return html;
                    }
                },
                {
                    field: 'Course', title: '适用学科', sortable: true, halign: 'center', align: 'center', width: commonWidth.Instrument.Course,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '<b>总计：</b>';
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', sortable: true, halign: 'center', align: 'left', width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            var html = "";
                            if (row.IsDangerChemical == 1) {
                                html += Syjx.GetDangerHtml();
                            }
                            if (row.IsSelfMade == 1) {
                                html += Syjx.GetSelfMadeHtml();
                            }
                            html += value;
                            return html;
                        }
                        else return '';
                    }
                },
                {
                    field: 'Model', title: '规格属性', sortable: true, halign: 'center', align: 'left', width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        if (html.length > 15) {
                            html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        }
                        return html;
                    }
                },
                {
                    field: 'Num', title: '入库数量', sortable: true, halign: 'center', align: 'center', width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '<b>' + value + '</b>';
                    }
                },
                {
                    field: 'UnitName', title: '单位', sortable: true, halign: 'center', align: 'center', width: commonWidth.Instrument.UnitName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Price', title: '单价', sortable: true, halign: 'center', align: 'right', width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        if (row.Id) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return '';
                    }
                },
                {
                    field: 'SumMoney', title: '金额', sortable: true, halign: 'center', align: 'right', width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        if (row.Id) return row.Price > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                    }
                },
                {
                    field: 'VarietyAttributeName', title: '仪器属性', sortable: true, halign: 'center', align: 'center', width: commonWidth.Instrument.VarietyAttributeName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },

                {
                    field: 'FunRoom', title: '存放地', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.FunRoom,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            if (row.Cupboard) {
                                value += '>' + row.Cupboard;
                            }
                            if (row.Floor) {
                                value += '>' + row.Floor;
                            }
                            return value;
                        } else {
                            return '';
                        }
                    }
                },

                {
                    field: 'DepartmentName', title: '管理部门', sortable: true, halign: 'center', align: 'center', width: commonWidth.Instrument.DepartmentName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'RealName', title: '管理人', sortable: true, halign: 'center', align: 'center', width: commonWidth.Instrument.UserName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'PurchaseDate', title: '采购日期', sortable: true, halign: 'center', align: 'center', width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (row.Id) return ys.formatDate(value, "yyyy-MM-dd");
                        else return '';
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function clearGrid() {
        $('#attribute').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $("#storagePlace").ysComboBoxTree('setValue', -1);
        $('#SysDepartmentId').ysComboBoxTree('setValue', -1);
        $("#isSelfMade").ysComboBox('setValue', -1);
        $("#Name").val("");
        $("#startTime").val("");
        $("#endTime").val("");
        $("#SysUserId").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
        $(".select2-container").width("100%");
    }


    function showView(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/QueryStatisticsManage/Instrument/Detail")' + '?id=' + id + '&opt=2';
        //createMenuItem(url, "查看");
        ys.openDialog({
            title: "查看",
            width: "800px",
            content: url,
            btn: ['关闭'],
            yes: function (index) {
                $.modal.close(index);
            },
        });
    }

    function exportForm() {
        var url = '@Url.Content("~/QueryStatisticsManage/Instrument/ExportStatDetailList")';//增加仪器统计(101036)
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "Id", "order": "desc", "offset": 0, "limit": 10 });
        var postData = $('#searchDiv').getWebControls(pagination);
        postData.BasePageCode = BasePageCode;//增加仪器统计(101036)
        ys.exportExcel(url, postData);
    }
</script>
