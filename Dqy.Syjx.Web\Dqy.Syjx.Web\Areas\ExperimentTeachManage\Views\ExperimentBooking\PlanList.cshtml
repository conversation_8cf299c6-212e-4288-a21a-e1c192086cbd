﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
        
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li id="liSchoolYear">
                        <span id="SchoolYearStart" col="SchoolYearStart" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liSchoolTerm">
                        <span id="SchoolTerm" col="SchoolTerm" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="GradeId" col="GradeId" style="display:inline-block;width:110px;"></span>
                    </li>
                    <li id="liExperimentVersionId">
                        <span id="ExperimentVersionId" col="ExperimentVersionId" style="display:inline-block;width:260px;"></span>
                    </li>
                    <li>
                        <span id="ExperimentType" col="ExperimentType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="IsNeedDo" col="IsNeedDo" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <div id="IsExam" col="IsExam" style="display: inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <input id="ExperimentName" col="ExperimentName" style="display:inline-block;width:180px;" placeholder="实验名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var optName = "预约";
    var RecordMode = ys.request("t"); //1:预约  2：简易登记
    var SourceType = ys.request("s"); //1:计划  2：目录
    var Com_SchoolTermYear = 0;
    var Com_SchoolTerm = 1;
    $(function () {
        if (RecordMode == 2) {
            optName = "登记";
        } else {
            RecordMode = 1;
        }
        if (RecordMode == 1 || SourceType == 2) {
            $("#liSchoolYear").hide();
            $("#liSchoolTerm").hide();
        }
        if (SourceType == 2) {
         
            //实验目录
        } else {
            $("#liExperimentVersionId").hide();
            SourceType = 1;
        }
        loadSearchCombo();
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetPlanListJson")';
        var sortName = " AllNum DESC, WeekNum ASC ";
        if(SourceType==2){
            sortName = " AllNum DESC, Chapter ASC ";
       }
        $('#gridTable').ysTable({
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            pagination: false,
            sortName: sortName,
            pageSize:10000,
            url: queryUrl,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            if (row.IsSelect != undefined && parseInt(row.IsSelect) == 1) {
                                html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="selectClass(this,\'{0}\',{1},{2})"><i class="fa fa-edit"></i>{3}</a> ', row.Id,0,0,optName);
                                if(SourceType==2){
                                    html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="selectClass(this,\'{0}\',\'{1}\',\'{2}\')"><i class="fa fa-edit"></i>{3}</a> ', row.Id, row.TextbookVersionCurrentId, row.SourcePath, optName);
                                }
                            } else {
                                html = '--';
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'ExperimentName', title: '实验名称', sortable: false, halign: 'center', align: 'left', width: 360,
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.Id) {
                            switch (row.ExperimentType) {
                                case @ExperimentTypeEnum.Demo.ParseToInt():
                                    html = Syjx.GetCircleDemoHtml(); 
                                    break;
                                case @ExperimentTypeEnum.Group.ParseToInt():
                                    html = Syjx.GetCircleGroupHtml();
                                    break;
                                default:
                                    html += '';
                                    break;
                            }
                            if (row.SourcePath==@SourcePathEnum.School.ParseToInt()) {
                                html += '<span class="font-circle font-circleViolet">校</span>'
                            }
                            if (row.IsExam == 1) {
                                html += Syjx.GetCircleShenRoomHtml();
                            }
                            if (value && value.length > 0) {
                                html += value;
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'IsNeedDo', title: '实验要求', sortable: false, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        var html = '@IsNeedEnum.MustDo.GetDescription()';
                        if (@IsNeedEnum.SelectToDo.ParseToInt()== value) {
                            html = '@IsNeedEnum.SelectToDo.GetDescription()';
                        }
                        return html;
                    }
                },
                {
                    field: 'WeekNum', title: '周次', sortable: false, halign: 'center', align: 'center', width: 80, visible: (SourceType == 1),
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.Id) {
                            html = value;
                        }
                        return html;
                    }
                },
                {
                    field: 'ClassInfo', title: '预约信息', halign: 'center', align: 'left',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (row.ClassInfoList!=null  && row.ClassInfoList.length>0) {
                                html='';
                                for (var i = 0; i < row.ClassInfoList.length; i++) {
                                    var classItem = row.ClassInfoList[i];
                                    if (classItem.Statuz == @ExperimentBookStatuzEnum.WaitArrange.ParseToInt()) {
                                        html += $.Format('<span idbooking="{1}" class="classExperiment" onclick="lookView(this);">{0}<span style="color:#1C84C6;">(已预约)</span></span>', classItem.ClassDesc, classItem.Id);
                                    } else if (classItem.Statuz == @ExperimentBookStatuzEnum.WaitRecord.ParseToInt()) {
                                        html += $.Format('<span idbooking="{1}" class="classExperiment" onclick="lookView(this);">{0}<span style="color:#E6A23C;">(已安排)</span></span>', classItem.ClassDesc, classItem.Id);
                                    } else if (classItem.Statuz == @ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt()) {
                                        html += $.Format('<span idbooking="{1}" class="classExperiment" onclick="lookView(this);">{0}<span style="color:#67C23A;">(已登记)</span></span>', classItem.ClassDesc, classItem.Id);
                                    }else{
                                        html += $.Format('<span idbooking="{1}" style="display:inline-block;width: 140px;">{0}<span></span></span>', classItem.ClassDesc, classItem.Id);
                                    }
                                }
                            }
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                pagination.pageSize= 10000;
                pagination.pageIndex = 1;
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.RecordMode = RecordMode;
                queryString.SourceType = SourceType;
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
    }
    function resetGrid() {
        //清空条件
       
        $('#GradeId').ysComboBox('setValue', -1);
        $('#ExperimentType').ysComboBox('setValue', -1);
        $('#IsNeedDo').ysComboBox('setValue', -1);
        $('#IsExam').ysComboBox('setValue', -1);
        $('#ExperimentName').val('');
        if (SourceType == 1) {
            //等于1，计划的时候才重置这个
            $('#SchoolYearStart').ysComboBox('setValue', Com_SchoolTermYear);
            $('#SchoolTerm').ysComboBox('setValue', Com_SchoolTerm);
        }
        $('#ExperimentVersionId').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
    }
    /**加载搜索条件*/
    function loadSearchCombo() {
        ComBox.SchoolTermYear($('#SchoolYearStart'), undefined, '学年');
        $("#SchoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        $("#ExperimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
        $("#IsNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });
        $("#IsExam").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())), defaultName: '按主管部门要求' });
        loadGrade();
        loadVersionBase();
    }
     
    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolGradeListJson")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级',
        });
    }

    function loadVersionBase() {
        var schoolterm = 0;
        if (parseInt($('#SchoolTerm').ysComboBox("getValue")) > 0) {
            schoolterm = parseInt($('#SchoolTerm').ysComboBox("getValue"));
        }
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetCatalogVersionListJson")' + '?Id=' + 0 + '&SchoolTerm=' + schoolterm,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var arrData = [{ VersionName: "校本实验", Id: 99 }];
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        arrData = arrData.concat(obj.Data);
                    }
                    //arrData.unshift();
                    $('#ExperimentVersionId').ysComboBox({
                        data: arrData,
                        key: 'Id',
                        value: 'VersionName',
                        defaultName: '实验教材版本'
                    });

                }
            }
        });
    }

    function selectClass(obj, id, textbookversioncurrentid, sourcepath) {
        var dialogTitle = $.Format("选择{0}班级", optName);
        ys.openDialog({
            title: dialogTitle,
            content: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/SelectClassList")' + '?id=' + id + '&textbookversioncurrentid=' + textbookversioncurrentid + '&SourceType=' + SourceType + '&SourcePath=' + sourcepath + "&RecordMode=" + RecordMode,
            width: '668px',
            height: '400px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function bookingEdit(id) {
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/EditForm")' + '?id=' + id;
        var tagName = '预约填报';
        if (RecordMode == 2) {
            url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/RecordEasy")' + '?id=' + id;
            tagName = '简易登记填报';
        }
        createMenuAndCloseCurrent(url, tagName);
    }

    function lookView(obj) {
        var id = $(obj).attr("idbooking");
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/Detail")' + '?id=' + id + '&pid=0&statuz=@ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt()';
        createMenuItem(url, "实验查看");
    }
</script>
