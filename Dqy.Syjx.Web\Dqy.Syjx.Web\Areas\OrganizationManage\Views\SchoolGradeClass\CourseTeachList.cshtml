﻿@using Dqy.Syjx.Enum.SystemManage
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }

    td {
        position: relative;
    }

    .hide {
        display: none;
    }

    .show {
        display: block;
    }

    .iconDown {
        position: absolute;
        top: 8px;
        right: 20px;
    }

    
    .iconUp {
        position: absolute;
        top: 13px;
        right: 20px;
    }

    .iconClose {
        position: absolute;
        top: 10px;
        right: 5px;
        display:none;
    }

    td:hover .iconClose {
        display: inline-block;
        position: absolute;
        top: 10px;
        right: 5px;
        cursor: pointer;
    }

    

    #sapnSelect2 .select2-results {
        max-height: 160px;
        overflow-y: auto;
    }

    .select2-results__option {
        text-align: left;
        padding: 3px 5px;
        white-space: pre-wrap;
        cursor: pointer;
    }

        .select2-results__option:hover {
            background-color: #90bafb;
            color: #ffffff;
        }

    #searchInputSelect2 {
        border: none;
    }

        #searchInputSelect2:focus {
            outline-style: none;
            /*border: 1px solid #999999;*/
        }

    .select2-search {
        position: relative;
    }

    #searchLeft {
        position: absolute;
        padding-left: 8px;
        padding-top: 6px;
        color: #C0C4CC;
    }
    .select-userteach{
        height: 100%;
        width: 100%;
        line-height: 36px;
        padding-left:10px;
        padding-right:15px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="GradeId" col="GradeId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="Key" col="Key" placeholder="班级关键字" />
                    </li>
                    <li>
                        <input id="UserName" col="UserName" placeholder="任课老师关键字" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <li style="padding:5px;color:#999;">友情提示：<span style="color:red;">只需设置中学理化生和小学科学</span>其他科目由教师自行设置</li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<div id="sapnSelect2" class="select2-container--bootstrap select2-container--open select2-dropdown select2-dropdown--below" dir="ltr" style="width: 500px;position: absolute;">
    <span class="select2-search select2-search--dropdown">
        <i id="searchLeft" class="fa fa-search"></i>
        <input id="searchInputSelect2" class="select2-search__field" type="text" autocomplete="off">
    </span>
    <div class="select2-results">
        <ul class="select2-results__options" role="listbox" id="searchUlSelect2" aria-expanded="true" aria-hidden="false">
        </ul>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_TeachListData;
    var Com_Table_Data;
    $(function () {
        loadGrade();
        var queryUrl = '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetCourseTeachListJson")' + '?IsGraduate=0';
        var columnsArr = [];
        columnsArr.push({ field: 'index', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } }, {
            field: 'GradeName',
            title: '年级',
            width: 100,
            halign: 'center',
            valign: 'middle',
            align: 'center',
            formatter: function (value, row, index) {
                if (row.Id) return value;
                else return '';
            }
        }, {
            field: 'ClassDesc',
            title: '班级',
            width: 160,
            halign: 'center',
            valign: 'middle',
            align: 'center',
            formatter: function (value, row, index) {
                if (row.Id) return value;
                else return "<b>" + value + "</b>";
            }
        });
        $.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetTeachListJson")',
            type: 'get',
            async: false,
            dataType: 'json',
            success: function (obj) {
                if (obj.Tag == 1) {
                    Com_TeachListData = obj.Data;
                    if (obj.Data.isphysics) {

                        columnsArr.push({
                            field: 'Physics',
                            title: '物理',
                            width: 100,
                            halign: 'center',
                            valign: 'middle',
                            align: 'center',
                            formatter: function (value, row, index) {
                                var html = '';
                                if (row.Id) {
                                    if (row.isPhysics != undefined && parseInt(row.isPhysics) == 1) {
                                        let username = "";
                                        let userid = 0;
                                        if (value != undefined) {
                                            if (value.UserId != undefined && parseInt(value.UserId) > 0) {
                                                userid = value.UserId;
                                            }
                                        }
                                        if (value != undefined) {
                                            if (value.UserName && value.UserName.length > 0) {
                                                username = value.UserName;
                                            }
                                        }
                                        html += '<div class="select-userteach"  classid="{0}" courseid="@StaticDictionaryEnum.Physics.ParseToInt()" value="{1}" onclick="userSelectModel(this,{0})">{2}</div>'
                                        html += '<i class="fa fa-sort-down iconDown"></i>'
                                        html += '<i class="fa fa-sort-up iconUp hide"></i>'
                                        html += '<i class="fa fa-times-circle iconClose"  classid="{0}"  courseid="@StaticDictionaryEnum.Physics.ParseToInt()" value="{1}" onclick="delIcon(this)"></i>';
                                        html = $.Format(html, row.Id, userid, username);
                                    }
                                }
                                return html;
                            }, cellStyle: function (value, row, index) {
                                return { css: { 'padding': '0px', 'height': '36px' } };
                            }
                        });
                    }
                    if (obj.Data.ischemistry) {
                        columnsArr.push({
                            field: 'Chemistry',
                            title: '化学',
                            width: 100,
                            halign: 'center',
                            valign: 'middle',
                            align: 'center',
                            formatter: function (value, row, index) {
                                var html = '';
                                if (row.Id) {
                                    if (row.isChemistry != undefined && parseInt(row.isChemistry) == 1) {
                                        let username = "";
                                        let userid = 0;
                                        if (value != undefined) {
                                            if (value.UserId != undefined && parseInt(value.UserId) > 0) {
                                                userid = value.UserId;
                                            }
                                        }
                                        if (value != undefined) {
                                            if (value.UserName && value.UserName.length > 0) {
                                                username = value.UserName;
                                            }
                                        }

                                        html += '<div class="select-userteach"  classid="{0}" courseid="@StaticDictionaryEnum.Chemistry.ParseToInt()" value="{1}" onclick="userSelectModel(this,{0})">{2}</div>'
                                        html += '<i class="fa fa-sort-down iconDown"></i>'
                                        html += '<i class="fa fa-sort-up iconUp hide"></i>'
                                        html += '<i class="fa fa-times-circle iconClose"  classid="{0}"  courseid="@StaticDictionaryEnum.Chemistry.ParseToInt()" value="{1}" onclick="delIcon(this)"></i>';
                                        html = $.Format(html, row.Id, userid, username);
                                    }
                                }
                                return html;
                            }, cellStyle: function (value, row, index) {
                                return { css: { 'padding': '0px', 'height': '36px' } };
                            }
                        });
                    }
                    if (obj.Data.isbiology) {
                        columnsArr.push({
                            field: 'Biology',
                            title: '生物',
                            width: 100,
                            halign: 'center',
                            valign: 'middle',
                            align: 'center',
                            formatter: function (value, row, index) {
                                var html = '';
                                if (row.Id) {
                                    if (row.isBiology != undefined && parseInt(row.isBiology) == 1) {
                                        let username = "";
                                        let userid = 0;
                                        if (value != undefined) {
                                            if (value.UserId != undefined && parseInt(value.UserId) > 0) {
                                                userid = value.UserId;
                                            }
                                        }
                                        if (value != undefined) {
                                            if (value.UserName && value.UserName.length > 0) {
                                                username = value.UserName;
                                            }
                                        }
                                        html += '<div class="select-userteach"  classid="{0}" courseid="@StaticDictionaryEnum.Biology.ParseToInt()" value="{1}" onclick="userSelectModel(this,{0})">{2}</div>'
                                        html += '<i class="fa fa-sort-down iconDown"></i>'
                                        html += '<i class="fa fa-sort-up iconUp hide"></i>'
                                        html += '<i class="fa fa-times-circle iconClose"  classid="{0}"  courseid="@StaticDictionaryEnum.Biology.ParseToInt()" value="{1}" onclick="delIcon(this)"></i>';
                                        html = $.Format(html, row.Id, userid, username);
                                    }
                                }
                                return html;
                            }, cellStyle: function (value, row, index) {
                                return { css: { 'padding': '0px', 'height': '36px' } };
                            }
                        });
                    }
                    if (obj.Data.isscience) {
                        columnsArr.push({
                            field: 'Science',
                            title: '科学',
                            width: 100,
                            halign: 'center',
                            valign: 'middle',
                            align: 'center',
                            hidden: true,
                            formatter: function (value, row, index) {
                                var html = '';
                                if (row.Id) {
                                    if (row.isScience != undefined && parseInt(row.isScience) == 1) {
                                        let username = "";
                                        let userid = 0;
                                        if (value != undefined) {
                                            if (value.UserId != undefined && parseInt(value.UserId) > 0) {
                                                userid = value.UserId;
                                            }
                                        }
                                        if (value != undefined) {
                                            if (value.UserName && value.UserName.length > 0) {
                                                username = value.UserName;
                                            }
                                        }
                                        html += '<div class="select-userteach"  classid="{0}" courseid="@StaticDictionaryEnum.Science.ParseToInt()" value="{1}" onclick="userSelectModel(this,{0})">{2}</div>';
                                        html += '<i class="fa fa-sort-down iconDown"></i>';
                                        html += '<i class="fa fa-sort-up iconUp hide"></i>';
                                        html += '<i class="fa fa-times-circle iconClose"  classid="{0}"  courseid="@StaticDictionaryEnum.Science.ParseToInt()" value="{1}" onclick="delIcon(this)"></i>';
                                        html = $.Format(html, row.Id, userid, username);
                                    }
                                }
                                return html;
                            }, cellStyle: function (value, row, index) {
                                return { css: { 'padding': '0px', 'height': '36px' } };
                            }
                        });
                    }
                }
            }
        });
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sortName: 'GradeId ASC,ClassId ASC',
            sortOrder: 'ASC',
            pageSize: 100000,
            columns: columnsArr,
            queryParams: function (params) {
                console.log(111)
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function (data) {
                Com_Table_Data = data;
            }
        });

        loadUserSelectModel();


    });

    function searchGrid() {
        $('#gridTable').ysTable('search');
        //resetToolbarStatus();
    }

    function resetGrid() {
        //清空条件
        $('#GradeId').ysComboBox('setValue', -1);
        $('#Key').val('');
        $('#UserName').val('');
        $('#gridTable').ysTable('search');
    }

    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级'
        });
    }

    function loadUserSelectModel() {
        //搜索条件绑定事件
        var inputSearch = document.getElementById("searchInputSelect2");
        inputSearch.addEventListener('input', function (event) {
            if ($("#searchInputSelect2").val()) {
                $("#searchLeft").hide()
            } else {
                $("#searchLeft").show()
            }

            loadUserListData();
        });
        inputSearch.addEventListener('change', function (event) {
            loadUserListData();
        });
        $("#searchInputSelect2").blur(function (e) {
            // 点击弹出框内部不隐藏
            $("#sapnSelect2 > .select2-dropdown,.select-userteach").click(function (e) {
                e.stopPropagation()
            });

            // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
            $(document).one("click", function (e) {
                $('#sapnSelect2').hide();
                $(".iconDown").removeClass("hide").addClass("show")
                $(".iconUp").removeClass("show").addClass("hide")
            });
        });
        loadUserListData();
    }


    var COM_Current_SelectObj;
    function userSelectModel(obj, id) {
        $("#searchLeft").show()
        COM_Current_SelectObj = $(obj);

        $(".iconDown").removeClass("hide").addClass("show")
        $(".iconUp").removeClass("show").addClass("hide")
        $(obj).next().removeClass("show").addClass("hide")
        $(obj).next().next().removeClass("hide").addClass("show")
        $("#searchInputSelect2").val('')
        var top = $(obj).offset().top;
        var left = $(obj).offset().left;
        var tdwidth = $(obj).css("width");
        $("#sapnSelect2").css("width", tdwidth);
        var tbheight = $(".container-div").height();//元素高度
        var minDiv = 240;
        var thisbottom = tbheight - top;
        var offsetBottom = tbheight - top;
        loadUserListData();
        $("#sapnSelect2").show();
        if (offsetBottom > (minDiv + 30)) {
            top = top + 30;//加上文本框高度
            $("#sapnSelect2").css({ top: top + 'px', bottom: 'auto', left: left + 'px', position: 'absolute' });
        } else {
            offsetBottom = (offsetBottom + 18);
            $("#sapnSelect2").css({ top: 'auto', bottom: offsetBottom + 'px', left: left + 'px', position: 'absolute' });
        }

        $("#searchInputSelect2").focus();

    }

    function loadUserListData() {
        $(".select2-results__options").html("");
        var thisValu = $("#searchInputSelect2").val();
        if (Com_TeachListData != undefined && Com_TeachListData.teachList != undefined && Com_TeachListData.teachList.length > 0) {
            var userNum = 0;
            for (let i = 0; i < Com_TeachListData.teachList.length; i++) {
                const element = Com_TeachListData.teachList[i];
                if (thisValu != undefined && thisValu.length > 0) {
                    if (element.RealName.indexOf(thisValu) == -1) {
                        continue;
                    }
                }
                userNum += 1;
                var strOption = $.Format('<li class="select2-results__option select2-results__option--selectable" aria-selected="false" role="option" onclick="updateGradeTeach(this,\'{0}\',\'{1}\')">{1}</li>', element.Id, element.RealName);
                $(".select2-results__options").append(strOption);
            }
            if (userNum == 0) {
                $(".select2-results__options").append('<div style="text-align: center;">无匹配数据</div>');
            }
        }
    }

    function updateGradeTeach(obj, teachid, teachname) {
        var courseid = COM_Current_SelectObj.attr("courseid");
        var classid = COM_Current_SelectObj.attr("classid");
        var jsondata = { GradeClassId: classid, CourseId: courseid, UserId: teachid };
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/SaveTeachCourseClassJson")',
            type: 'POST',
            data: jsondata,
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (Com_Table_Data != undefined && Com_Table_Data.Tag == 1 && Com_Table_Data.Data != undefined && Com_Table_Data.Data.length > 0) {
                        for (let i = 0; i < Com_Table_Data.Data.length; i++) {
                            if (Com_Table_Data.Data[i] != undefined && Com_Table_Data.Data[i].Id == classid) {
                                switch (parseInt(courseid)) {
                                    case @StaticDictionaryEnum.Physics.ParseToInt():
                                        Com_Table_Data.Data[i].Physics = { "UserId": teachid, "UserName": teachname };
                                        break;
                                    case @StaticDictionaryEnum.Chemistry.ParseToInt():
                                        Com_Table_Data.Data[i].Chemistry = { "UserId": teachid, "UserName": teachname };
                                        break;
                                    case @StaticDictionaryEnum.Biology.ParseToInt():
                                        Com_Table_Data.Data[i].Biology = { "UserId": teachid, "UserName": teachname };
                                        break;
                                    case @StaticDictionaryEnum.Science.ParseToInt():
                                        Com_Table_Data.Data[i].Science = { "UserId": teachid, "UserName": teachname };
                                        break;
                                }
                            }
                        }
                        $('#gridTable').bootstrapTable('load', Com_Table_Data);
                    }
                    layer.msg(obj.Message, { icon: 1, time: 3000, area: ['400px'], shade: false, offset: [15,] });
                    $("#sapnSelect2").hide();
                } else {
                    var layerid = layer.msg(obj.Message, {
                        icon: 2, time: false, btn: ['关闭'], yes: function () {
                            layer.close(layerid); 
                        }, area: ['400px'], shade: false, offset: [15,]
                    });
                }
            }
        });
        $(".iconDown").removeClass("hide").addClass("show")
        $(".iconUp").removeClass("show").addClass("hide")
    }

    //点击x图标删除教师
    function delIcon(obj) {
        var delObj = $(obj);
        var delCourseid = delObj.attr("courseid");
        var delClassid = delObj.attr("classid");
        var userid = delObj.attr("value");
        if (!(userid && parseInt(userid)) > 0) {
            ys.msgError('没有要删除的任课老师');
            return false;
        }
        var jsondata = { GradeClassId: delClassid, CourseId: delCourseid, UserId: userid };
        ys.confirm('确认要删除当前任课教师吗？', function () {
            //delObj.parent().children('div').text('');
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/DelTeachCourseClassJson")',
                type: 'POST',
                data: jsondata,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        delObj.siblings('div').text('');
                        delObj.attr("value", '0');
                        ys.msgSuccess(obj.Message); 
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

</script>