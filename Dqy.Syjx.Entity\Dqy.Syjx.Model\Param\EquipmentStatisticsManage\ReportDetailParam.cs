﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Dqy.Syjx.Util;

namespace Dqy.Syjx.Model.Param.EquipmentStatisticsManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2024-01-29 10:52
    /// 描 述：学校上报详情实体查询类
    /// </summary>
    public class ReportDetailListParam
    {
        private string _ids;
        public string Ids
        {
            get { return StringFilter.SearchSql(_ids); }
            set { _ids = value; }
        }
        /// <summary>
        /// 填报主表Id
        /// </summary>
        /// <returns></returns>
        public long ReportId { get; set; }
    }

    /// <summary>
    /// 查询条件
    /// </summary>
    public class ReportEquipmentParam : BaseParam
    {
        /// <summary>
        /// 学校Id
        /// </summary>
        public long SchoolId { get; set; }
        /// <summary>
        /// 区县id
        /// </summary>
        public long CountyId { get; set; }

        /// <summary>
        /// 学校名称
        /// </summary>
        private string _schoolName;
        public string SchoolName
        {
            get { return StringFilter.SearchSql(_schoolName); }
            set { _schoolName = value; }
        }
        /// <summary>
        /// 字典表DicId集合
        /// </summary>
        private string _dicids;
        public string DicIds
        {
            get { return StringFilter.SearchSql(_dicids); }
            set { _dicids = value; }
        }

        /// <summary>
        /// 字典表DicId集合
        /// </summary>
        private string _labmanagerdicids;
        public string LabManagerDicIds
        {
            get { return StringFilter.SearchSql(_labmanagerdicids); }
            set { _labmanagerdicids = value; }
        }

        /// <summary>
        /// 数据类型
        /// </summary>
        public int BackupType { get; set; }
        private string _pagecode;
        /// <summary>
        /// 页面编码
        /// </summary>
        public string PageCode
        {
            get { return StringFilter.SearchSql(_pagecode); }
            set { _pagecode = value; }
        }
        /// <summary>
        /// 年度
        /// </summary>
        public int BackupYear { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        public int ModuleId { get; set; }
    }
}
