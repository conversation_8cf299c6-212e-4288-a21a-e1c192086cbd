﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="instrumentEvaluateProjectId" col="InstrumentEvaluateProjectId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                    
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="allocateType" col="AllocateType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="width:150px" />
                    </li>
                    <li>
                        <div id="isOnlyShowNoStandard" col="IsOnlyShowNoStandard" style="display:inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var instrumentEvaluateProjectId = ys.request("instrumentEvaluateProjectId");
    var allocateType = ys.request("allocateType");
    var stageId = ys.request("stageId");
    var courseId = ys.request("courseId");
    var schoolId = ys.request("schoolId");
    var countyId = ys.request("countyId");
    var showCode = false;

    var SortName = 'Id asc';
    $(function () {
        if (@UnitType != @UnitTypeEnum.School.ParseToInt()) {
            loadSchool(0);
            $('#liSchoolId').show();
            SortName = 'Sort asc ,SchoolId asc ,Id asc';
            showCode = true;
        }

        $('#isOnlyShowNoStandard').ysCheckBox({
            data: [{ Key: 1, Value: '只显示不达标' }]
        });
        $("#allocateType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(AllocateTypeEnum).EnumToDictionaryString())), defaultName: '配备要求' });

        loadSchoolStage();
        loadSubject();
        if (allocateType > 0) {
            $("#allocateType").ysComboBox('setValue', allocateType);
        }

        loadInstrumentEvaluateProjectVersionId();
        
        

    });

    function initGrid() {
        if (!$('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        var queryUrl = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: SortName,
            columns: [
                { field: 'SchoolName', title: '单位名称', sortable: true, halign: 'center', align: 'left', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt()), width: 150 },
                { field: 'StageName', title: '学段', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.StageName },
                { field: 'CourseName', title: '学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course },
                //{ field: 'InstrumentCode', title: '仪器代码', halign: 'center', align: 'center', sortable: true, width: 80 },
                {
                    field: 'InstrumentCode', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: showCode,
                },
                { field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name },
                {
                    field: 'Model', title: '规格型号', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'StandardNum', title: '指标量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'StockNum', title: '存量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'AllocateType', title: '配备要求', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.AllocateType,
                    formatter: function (value, row, index) {
                        return value == 1 ? '必配' : '选配';
                    }
                },
                {
                    field: 'DifferenceNum', title: '差额', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function loadInstrumentEvaluateProjectVersionId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectBySchool")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#instrumentEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (instrumentEvaluateProjectId > 0) {
                        $('#instrumentEvaluateProjectId').ysComboBox('setValue', instrumentEvaluateProjectId);
                    }
                    else {
                        if (obj.Data != undefined && obj.Data.length > 0) {
                            $('#instrumentEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                        }
                    }
                    initGrid();
                }
            }
        });
    }

    function loadSchoolStage() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            data: null,
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolStageId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        defaultName: '学段',
                        onChange: function () {
                            if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                                var schoolStageId = $('#schoolStageId').ysComboBox("getValue");
                                if (schoolStageId > 0) {
                                    loadSchool(schoolStageId);
                                }
                            }
                        }
                    });
                    if (stageId > 0) {
                        $('#schoolStageId').ysComboBox('setValue', stageId);
                    }
                }
            }
        });
    }

    function loadSubject() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()',
            data: null,
            type: 'get',
            async: false ,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#courseId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        defaultName: '适用学科'
                    });
                    if (courseId > 0) {
                        $('#courseId').ysComboBox('setValue', courseId);
                    }
                }
            }
        });
    }

    function searchGrid() {
        if ($('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }
    function loadSchool(schoolStageId) {
        var url = '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=" + schoolStageId;
        if (@UnitType == @UnitTypeEnum.City.ParseToInt())
            url = '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId;
        ys.ajax({
            url: url,
            data: null,
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });
                    if (schoolId > 0) {
                        $('#schoolId').ysComboBox('setValue', schoolId);
                    }
                }
            }
        });
    }

    function resetGrid() {
        //清空条件
        $('#schoolStageId').ysComboBox('setValue', -1);
        if (stageId > 0) {
            $('#schoolStageId').ysComboBox('setValue', stageId);
        }
        loadSchool(0);
        $('#courseId').ysComboBox('setValue', -1);
        $('#instrumentEvaluateProjectId').ysComboBox('setValue', instrumentEvaluateProjectId);
        $('#keyWord').val('');
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            if (schoolId > 0)
                $('#schoolId').ysComboBox('setValue', schoolId);
            else
                $('#schoolId').ysComboBox('setValue', -1);
        }
        $("#allocateType").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>
