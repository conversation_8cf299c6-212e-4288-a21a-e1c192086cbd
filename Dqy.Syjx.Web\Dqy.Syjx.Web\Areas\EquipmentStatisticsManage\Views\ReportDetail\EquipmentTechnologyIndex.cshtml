﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";

}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="BackupYear" col="BackupYear" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolId" col="SchoolId" style="display: inline-block; width: 180px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var columnArr = [];
    var Current_QueryYear = new Date().getFullYear();
    $(function () {
        $('#SchoolId').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenHasKindergartenPageList")' + "?PageSize=10000", key: 'Id', value: 'Name',
            defaultName: '单位名称',
            minimumResultsForSearch: 0
        });
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        initGrid();

    });

    function initGrid() {

        var columns = [
            {
                field: 'index', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return index + 1;
                    else return '';
                }
            },
            {
                field: 'SchoolCode', title: '单位编号', sortable: true, halign: 'center', align: 'left', width: 80, cellStyle: "width:200px;",
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    else return '';
                }

            },
            {
                field: 'SchoolName', title: '单位名称', sortable: true,halign: 'center', align: 'left', width: 120,
                formatter: function (value, row, index) {
                    var html = value;
                    if (html == "<b>总计：<b>") {
                        html = "<div style='text-align:center;'><b>总计：<b></div>";
                    }
                    return html;
                }
            },
            { field: 'Name', title: '设备名称', sortable: true, halign: 'center', align: 'left', width: 150,
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.Name;
                    else return '';
                }
            },
            { field: 'Brand', title: '品牌', sortable: true, halign: 'center', align: 'left', width: 120,
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.Brand;
                    else return '';
                }
            },
            { field: 'Modelz', title: '型号', sortable: true, halign: 'center', align: 'left', width: 120,
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.Modelz;
                    else return '';
                }
            },
            { field: 'Num', title: '数量', sortable: true, halign: 'center', align: 'center', width: 80,
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.Num > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                }
            },
            { field: 'UnitName', title: '单位', sortable: true, halign: 'center', align: 'center', width: 60,
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.UnitName;
                    else return '';
                }
            },
            { field: 'Remark', title: '备注', sortable: true, halign: 'center', align: 'left', width: 200,
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return row.Remark;
                    else return '';
                }
            },
        ];
        columnArr = [columns];
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            var postData = {
                ModuleId: @BackupsModuleIdEnum.ZbStatistics.ParseToInt(),
                PageCode: "@BackupsPageCodeEnum.StiEquipment.ParseToInt().ToString()",
                BackupType: @BackupTypeEnum.DataTitleJson.ParseToInt(),
                BackupYear: thisYear
            };
            $.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetBackupsTitleJson")',
                async: false,
                type: 'get',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var colArr = obj.Data[0].Content;
                        if (typeof (colArr) == "string") {
                            colArr = JSON.parse(colArr);
                        }
                        if (colArr && colArr.length > 0) {
                            columnArr = [];
                            for (var i = 0; i < colArr.length; i++) {
                                columnArr.push(Syjx.GetGridTableColumns(colArr[i]));
                            }
                        }
                    } else {

                    }
                }
            });
        }
        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetEquipmentPageListJson")';

        $('#gridTable').ysTable({
            url: queryUrl,
            sortName:'Sort ASC ,SchoolId DESC ',
            columns: columnArr,
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        if (Current_QueryYear != thisYear && parseInt(thisYear) > 0) {
            Current_QueryYear = thisYear;
            $('#gridTable').bootstrapTable('destroy');
            initGrid();//重新表头。
        } else {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
    }


    function resetGrid() {
        $("#SchoolId").ysComboBox('setValue', -1);
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function exportForm() {
        var url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/EquipmentExport")';
        var postData = $("#searchDiv").getWebControls();
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/BackupExport")';
            postData.PageCode = '@BackupsPageCodeEnum.StiEquipment.ParseToInt().ToString()';
        }
        ys.exportExcel(url, postData);
    }
</script>
