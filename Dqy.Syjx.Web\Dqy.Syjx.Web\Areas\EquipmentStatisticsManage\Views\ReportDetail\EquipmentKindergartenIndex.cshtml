﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";

}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>

                    <li>
                        <div id="SchoolId" col="SchoolId" style="display: inline-block; width: 180px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">


    $(function () {
        $('#SchoolId').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenHasKindergartenPageList")' + "?PageSize=10000", key: 'Id', value: 'Name',
            defaultName: '单位名称',
            minimumResultsForSearch: 0
        });
        initGrid();

    });

    function initGrid() {

        var columns = [
            {
                field: 'SchoolName', title: '单位名称', sortable: true, halign: 'center', align: 'left', width: 150,
                formatter: function (value, row, index) {
                    var html = value;
                    if (html == "<b>总计：<b>") {
                        html = "<div style='text-align:center;'><b>总计：<b></div>";
                    }
                    return html;
                }
            },
            { field: 'CategoryName', title: '分类名称', sortable: true, halign: 'center', align: 'left', width: 150,
                formatter: function (value, row, index) {
                    if (row.Id) return row.CategoryName;
                    else return '';
                }
            },
            { field: 'Name', title: '专用室名称', sortable: true, halign: 'center', align: 'left', width: 120,
                formatter: function (value, row, index) {
                    if (row.Id) return row.Name;
                    else return '';
                }
            },
            { field: 'UseArea', title: '使用面积（平米）', sortable: true, halign: 'center', align: 'center', width: 120,
                formatter: function (value, row, index) {
                    if (row.Id) return row.UseArea > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                }
            },
            {
                field: 'BuildTime', title: '起初建设时间', halign: 'center', align: 'center', width: 120, sortable: true, 
                formatter: function (value, row, index) {
                    return ys.formatDate(value, "yyyy-MM-dd");
                }
            },

            {
                field: 'ReformTime', title: '最新改造时间', halign: 'center', align: 'center', width: 120, sortable: true, 
                formatter: function (value, row, index) {
                    return ys.formatDate(value, "yyyy-MM-dd");
                }
            },
            { field: 'Remark', title: '备注', sortable: true, halign: 'center', align: 'left', width: 200,
                formatter: function (value, row, index) {
                    if (row.Id) return row.Remark;
                    else return '';
                }
            },
        ];

        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetKindergartenPageListJson")';

        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Sort ASC',
            columns: [
                columns
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#sea rchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function resetGrid() {
        $("#SchoolId").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function exportForm() {
        var url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/KindergartenExport")';
        var postData = $("#searchDiv").getWebControls();
        ys.exportExcel(url, postData);
    }
</script>
