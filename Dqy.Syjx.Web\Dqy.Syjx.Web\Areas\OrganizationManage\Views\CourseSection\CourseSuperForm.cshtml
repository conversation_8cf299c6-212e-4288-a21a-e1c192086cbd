﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/laydate/5.1/laydate.js"),true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsrender.js"),true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsviews.js"),true)

<style>
    /* 设置只展示时分，隐藏秒那一列 */
    .laydate-time-list {
        padding-bottom: 0;
        overflow: hidden;
    }

        .laydate-time-list > li {
            width: 50% !important;
        }

            .laydate-time-list > li:last-child {
                display: none;
            }

        .laydate-time-list ol li {
            width: 100% !important;
            padding-left: 0 !important;
            text-align: center !important;
        }
</style>

<div id="divCourseInfo" class="wrapper animated fadeInRight">
   
</div>

<script id="dateList" type="text/x-jsrender">
    <div class="col-sm-12 select-table table-striped">
        <div class="bootstrap-table">
            <div class="fixed-table-toolbar">
                <div class="bs-bars pull-left">
                    <div id="toolbar" class="btn-group d-flex" role="group">
                        <a id="btnAdd" class="btn btn-success" data-link="{on addRow}"><i class="fa fa-plus"></i> 添加</a>
                        <a id="btnDelete" class="btn btn-danger" data-link="{on deleteRow}"><i class="fa fa-remove"></i> 删除</a>
                    </div>
                </div>
            </div><div class="fixed-table-container" style="padding-bottom: 0px;">
                <div class="fixed-table-header" style="display: none;">
                    <table></table>
                </div>
                <div class="fixed-table-body">
                    <table id="gridTable" data-mobile-responsive="true" class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th style="text-align: center; vertical-align: middle;" colspan="4">
                                    <div class="th-inner sortable both">夏季时间</div><div class="fht-cell"></div>
                                </th>
                                <th style="text-align: center; vertical-align: middle;" colspan="4">
                                    <div class="th-inner sortable both">冬季时间</div><div class="fht-cell"></div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="list">
                             {^{for data}}
                                 <tr>
                                    <td>第{^{:#index + 1}}节</td>
                                    <td><input name="sectionDate" id="xj_beginTime_{{>RIndex}}" col="xjBeginTime" type="text" value="{{>BeginTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>
                                    <td>至</td>
                                    <td><input name="sectionDate" id="xj_endTime_{{>RIndex}}" col="xjEndTime" type="text" value="{{>EndTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>

                                    <td>&nbsp;&nbsp;</td>
                                    <td><input name="sectionDate" id="dg_beginTime_{{>RIndex}}" col="djBeginTime" type="text" value="{{>DjBeginTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>
                                    <td>至</td>
                                    <td><input name="sectionDate" id="dg_endTime_{{>RIndex}}" col="djEndTime" type="text" value="{{>DjEndTime}}" rowIndex="{{>RIndex}}" oldValue="" IdValue="" class="form-control time-picker" readonly /></td>
                                </tr>
                             {{/for}}
                        </tbody>
                    </table>
                </div>
            </div>
        </div><div class="clearfix"></div>
    </div>
</script>

<script type="text/javascript">
    var course_info = null;

    $(function () {

            course_info = {
            data:[],
            addRow:function(){
                var rowIndex = parseInt(this.data[this.data.length - 1].RIndex) + 1;
                $.observable(this.data).insert({ RIndex: rowIndex, BeginTime: '', EndTime: '', DjBeginTime: '', DjEndTime: '', });
                // 时间插件
                laydate.render({
                    elem: '#xj_beginTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
                laydate.render({
                    elem: '#xj_endTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
                laydate.render({
                    elem: '#dg_beginTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
                laydate.render({
                    elem: '#dg_endTime_' + rowIndex,
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
            },
            deleteRow:function(){
                $.observable(this.data).remove(this.data.length - 1);
                if (this.data.length == 0) {

                    $.observable(this.data).insert({ RIndex: 1, BeginTime: '', EndTime: '', DjBeginTime: '', DjEndTime: '', });
                }
            },
            bindDate:function(){
                // 时间插件
                laydate.render({
                    elem: '.time-picker',
                    trigger: 'click',
                    type: 'time',
                    format: 'HH:mm',
                    min: '06:00:00',
                    max: '23:00:00',
                });
            }
        
        };
        $.templates("#dateList").link("#divCourseInfo", course_info);
        getForm();
        function getForm() {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/CourseSection/GetCourseSuperSectionList")',
                type: 'get',
                success: function (obj) {

                    if (obj.Tag == 1) {

                        $.observable(course_info).setProperty("data", obj.Data);
                        course_info.bindDate();
                    }

                }
            });
        }

       
    });

    function saveForm(index) {

        //遍历
        var arrLength = course_info.data.length;
        if (arrLength == 0) {
            ys.msgError("无数据可供保存");
            return;
        }

        var listArray = [];
        for (var i = 0; i < arrLength; i++) {

            var RowIndex = $("#xj_beginTime_" + (i + 1)).attr("rowIndex");
            var BeginTime = $("#xj_beginTime_" + (i + 1)).val();
            var EndTime = $("#xj_endTime_" + (i + 1)).val();
            var DjBeginTime = $("#dg_beginTime_" + (i + 1)).val();
            var DjEndTime = $("#dg_endTime_" + (i + 1)).val();

            if (BeginTime == "" || EndTime == "" || DjBeginTime == "" || DjEndTime == "") {
                ys.msgError("请完成所有课程节次设置不能留空，不需要的节次可以点击删除!");
                return;
            }

            if (EndTime <= BeginTime) {
                ys.msgError("夏季时间：第" + RowIndex + "节，结束时间不能小于等于开始时间!");
                return;
            }

            if (DjEndTime <= DjBeginTime) {
                ys.msgError("冬季时间：第" + RowIndex + "节，结束时间不能小于等于开始时间!");
                return;
            }

            //var obj = { RowIndex: RowIndex, BeginTime: BeginTime, EndTime: EndTime, DjBeginTime: DjBeginTime, DjEndTime: DjEndTime };
            course_info.data[i].RowIndex = RowIndex;
            course_info.data[i].BeginTime = BeginTime;
            course_info.data[i].EndTime = EndTime;
            course_info.data[i].DjBeginTime = DjBeginTime;
            course_info.data[i].DjEndTime = DjEndTime;
        }

        for (var i = 0; i < arrLength; i++) {
            if (i > 0) {
                if (course_info.data[i].BeginTime < course_info.data[i - 1].EndTime) {
                    ys.msgError("夏季时间：第" + course_info.data[i].RowIndex + "节，开始时间不能小于上一节课结束时间!");
                    return;
                }
                if (course_info.data[i].DjBeginTime < course_info.data[i - 1].DjEndTime) {
                    ys.msgError("冬季时间：第" + course_info.data[i].RowIndex + "节，开始时间不能小于上一节课结束时间!");
                    return;
                }
            }
        }

        var postData = {
            list: course_info.data
        };

        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/CourseSection/SaveSuperBatchForm")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                    parent.layer.close(index);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

  

</script>
