﻿
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))

<div class="wrapper animated fadeInRight">
    <div class="btn-group d-flex" role="group" id="toolbar">
    </div>
    <div class="col-sm-12 select-table table-striped">

        <div class="ibox-title">
            
        </div>
        <div class="card-body">
            <form id="form" class="form-horizontal m">
                <div class="divform divImport">
                    <div class="form-group row">
                        <label class="col-sm-2 control-label ">选择文件</label>
                        <div class="col-sm-6">
                            <input id="importFile" type="file">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label "></label>
                        <div class="col-sm-5">
                            <div class="control-label" style="text-align:left !important;">
                                <a href='@Url.Content("~/template/仪器导入模板.xlsx")' class="btn btn-secondary btn-xs"><i class="fa fa-file-excel-o"></i> 下载模板</a>
                            </div>
                        </div>

                    </div>
                    <div class="form-group row">
                        <label class="col-sm-2 control-label "></label>
                        <div class="col-sm-10 text-danger">
                            提示：请先下载模板，必须严格按照模板格式导入数据！
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar" style="text-align: center;">
            <a id="btnAdd" class="btn btn-info" onclick="saveForm();"><i class="fa fa-save"></i> 保存</a>
        </div>
    </div>
</div>

<script type="text/javascript">
    var filePath = undefined; //批量导入文件

    $(function(){
        $("#importFile").fileinput({
            language: 'zh',
            'uploadUrl': '@Url.Content("~/File/UploadExcel")' + '?fileModule=@UploadFileType.Import.ParseToInt()',
            showPreview: false,
            allowedFileExtensions: ['xls', 'xlsx']
        }).on("fileuploaded", function (event, data) {
            var obj = data.response;
            if (obj.Tag == 1) {
                filePath = obj.Data;
            }
            else {
                filePath = '';
            }
        });
    
    });

    function saveForm(index) {
        if (!filePath) {
            ys.alertError('文件未上传或者上传失败');
            return;
        }
        var postData = $("#form").getWebControls();
        postData.FilePath = filePath;
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/SchoolInstrument/ImportInstrumentJson")',
            type: "post",
            data: postData,
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                    var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentIndex")';
                    createMenuAndCloseCurrent(url, "仪器审核");
                }
                else {
                    ys.alertError(obj.Message)

                }
            }
        });
    }
</script>