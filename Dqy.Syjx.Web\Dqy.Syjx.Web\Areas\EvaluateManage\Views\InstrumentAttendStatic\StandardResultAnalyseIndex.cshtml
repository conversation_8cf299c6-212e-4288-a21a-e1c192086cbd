﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<script src="~/lib/report/echarts/echarts.min.js"></script>
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="instrumentEvaluateProjectId" col="InstrumentEvaluateProjectId" style="display:inline-block;width:300px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="allocateType" col="AllocateType" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">

        </div>
        <div class="col-sm-12 select-table table-striped">
            <div id="container">
                <div id="container_0" style="height:500px;width:550px;float:left;"></div>
                <div id="container_1" style="height:500px;width:550px;float:left;"></div>
                <div id="container_2" style="height:500px;width:550px;float:left;"></div>
            </div>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#liCountyId').show();
            loadCounty();
        }

        loadInstrumentEvaluateProjectVersionId();

        $("#allocateType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(AllocateTypeEnum).EnumToDictionaryString())), defaultName: '配备要求' });
        
        
    });

    function loadInstrumentEvaluateProjectVersionId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectBySchool")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#instrumentEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length> 0) {
                        $('#instrumentEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    loadData();
                }
            }
        });
    }

    function searchGrid() {
        loadData();
    }

    function resetGrid() {
        $('#allocateType').ysComboBox('setValue', -1);
        $('#countyId').ysComboBox('setValue', -1);
        loadInstrumentEvaluateProjectVersionId();
    }

    function loadCounty() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#countyId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '区县名称'
                    });
                }
            }
        });
    }

    function loadData() {
        var instrumentEvaluateProjectId = $('#instrumentEvaluateProjectId').ysComboBox('getValue');
        if (!(instrumentEvaluateProjectId > 0)) {
            ys.msgError('必须指定评估项目再查询！');
            return false;
        }
        $('#container').html('<div id="container"><div id = "container_0" style = "height:500px;width:550px;float:left;" ></div ><div id="container_1" style="height:500px;width:550px;float:left;"></div><div id="container_2" style="height:500px;width:550px;float:left;"></div></div >');

        var queryUrl = '';
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            queryUrl = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/GetCityStandardResultAnalyseListJson")' + '?InstrumentEvaluateProjectId=' + instrumentEvaluateProjectId + '&CountyId=' + $('#countyId').ysComboBox('getValue') + '&allocateType=' + $('#allocateType').ysComboBox('getValue')
        }
        else {
            queryUrl = '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/GetCountyStandardResultAnalyseListJson")' + '?InstrumentEvaluateProjectId=' + instrumentEvaluateProjectId + '&allocateType=' + $('#allocateType').ysComboBox('getValue')
        }
        ys.ajax({
            url: queryUrl,
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    //console.log(obj.Data);
                    $.each(obj.Data, function (index, v) {
                        let courseNameList = []; //学科名称数组
                        let courseValueList = []; //学科达标值数组
                        $.each(v.CourseList, function (k, course) {
                            courseNameList.push(course.CourseName);
                            courseValueList.push(course.StandardRate);
                        });

                        var dom = document.getElementById('container_' + index);
                        var myChart = echarts.init(dom, null, {
                            renderer: 'canvas',
                            useDirtyRect: false
                        });
                        var app = {};

                        var option;

                        option = {
                            title: {
                                text: v.StageName,
                                x: 'center',
                                textStyle: {
                                    color: '#8D8D8D',
                                    fontSize: 14
                                },
                                textAlign: 'left'
                            },
                            tooltip: {
                                trigger: 'axis',
                                formatter: function (params) {
                                    return params[0].name + '：' + params[0].value + '%';
                                }
                            },
                            grid: {
                                bottom: 70
                            },
                            xAxis: {
                                type: 'category',
                                data: courseNameList,//['科学', '音乐', '美术', '书法', '舞蹈', '体育', '心理健康', '信息技术', '劳动技术', '语文', '数学', '外语', '道德与法治'],
                                axisLabel: {
                                    formatter: function (value) {
                                        return value.split("").join('\n');
                                    }
                                }
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: {
                                    show: true,
                                    interval: 'auto',
                                    formatter: '{value} %'
                                },
                                show: true
                            },
                            series: [
                                {
                                    data: courseValueList,//[20, 40, 60, 80, 70, 110, 130, 40, 60, 80, 70, 110, 130],
                                    type: 'bar',
                                    barWidth: 20,//柱图宽度
                                    color: '#5C9BD1',
                                    itemStyle: {
                                        normal: {
                                            label: {
                                                show: true,		//开启显示
                                                position: 'top',	//在上方显示
                                                textStyle: {	    //数值样式
                                                    color: '#333',
                                                    fontSize: 12
                                                },
                                                formatter: function (value) {
                                                    return value.data + "%";
                                                }
                                            }
                                        }
                                    }
                                }
                            ]
                        };

                        if (option && typeof option === 'object') {
                            myChart.setOption(option);
                        }
                    });
                }
            }
        });
    }
</script>
