﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="schoolStage" col="SchoolStage" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="gradeId" col="GradeId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="versionName" col="VersionName" placeholder="实验教材版本" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm()"><i class="fa fa-plus"></i> 新增</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var versionName = '';
    if (ys.request("versionName")) {
        versionName = decodeURIComponent(ys.request("versionName"));
    }
    $(function () {
        loadSchoolStage();
        loadGrade();
        loadCourse();
        loadSchoolTerm();
                
        $('#versionName').val(versionName);
        
        
        initGrid();
    });

    function loadSchoolStage() {
        $('#schoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }
    function loadGrade(schoolStage) {
        $('#gradeId').ysComboBox({
            //url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()&Pid=' + schoolStage,
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级'
        });
    }
    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()' + '&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学科'
        });
    }
    function loadSchoolTerm() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { field: 'VersionName', title: '实验教材版本', halign: 'center', align: 'center', sortable: true },
                { field: 'SchoolStageName', title: '学段', halign: 'center', align: 'center', sortable: true },
                {
                    field: 'GradeName', title: '年级', halign: 'center', align: 'center', sortable: true,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.SchoolStage) {
                            html = '--';
                            if (row.SchoolStage != @SchoolStageEnum.GaoZhong.ParseToInt()) {
                                html = value;
                            }
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '学科', halign: 'center', align: 'center', sortable: true },
                {
                    field: 'SchoolTerm', title: '学期', halign: 'center', align: 'center', sortable: true,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.SchoolStage) {
                            html = '--';
                            if (row.SchoolStage != @SchoolStageEnum.GaoZhong.ParseToInt()) {
                                   html = row.SchoolTermName;
                            }
                        }
                        return html;
                    }
                }, 
                {
                    field: 'CompulsoryType', title: '高中教材类型', halign: 'center', align: 'center', sortable: true,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.SchoolStage){
                            html = '--';
                            if (row.SchoolStage == @SchoolStageEnum.GaoZhong.ParseToInt()) {
                                if (row.CompulsoryType == @TextbookCompulsoryTypeEnum.Must.ParseToInt()){
                                    html = " @TextbookCompulsoryTypeEnum.Must.GetDescription()";
                                }else{
                                    html = " @TextbookCompulsoryTypeEnum.NonSelectMust.GetDescription()";
                                }
                            } 
                        }
                        return html;
                    }
                },
                {
                    field: 'detail', title: '实验清单', halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                    }
                },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="edit(this)" value="{0}"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                        html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="del(this)" value="{0}"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                        return html;
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm() {
        ys.openDialog({
            title:  '添加',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/TextbookVersionBaseForm")' + '?id=0',
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function edit(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '编辑',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/TextbookVersionBaseForm")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function del(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要删除该数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionBase/DeleteFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function look(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/TextbookVersionDetailIndex")' + '?textbookVersionBaseId=' + id;
        createMenuItem(url, "实验清单");
    }

    function resetGrid() {
        //清空条件
        $('#schoolStage').ysComboBox('setValue', -1);
        $('#gradeId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#schoolTerm').ysComboBox('setValue', -1);
        $('#versionName').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>
