﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .span-left-tag {
        margin-left: 82px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="VersionId" col="VersionId" style="display: inline-block;width:180px;"></div>
                    </li>
                    <li>
                        <div id="ExperimentType" col="ExperimentType" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="IsNeedDo" col="IsNeedDo" style="display: inline-block;width:100px;"></div>
                    </li>
               @*      <li>
                        <div id="IsEvaluate" col="IsEvaluate" style="display: inline-block;width:100px;"></div>
                    </li> *@
                    <li>
                        <input id="Name" col="Name" placeholder="实验名称、章节" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm()"><i class="fa fa-plus"></i> 添加实验</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    var startyear = ys.request("startyear");
    var schoolterm = ys.request("schoolterm");
    var gradeid = ys.request("gradeid");
    var schoolstage = ys.request("schoolstage");
    $(function () {
        $("#IsNeedDo").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())),
            defaultName: '实验要求'
        });
        $("#ExperimentType").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())),
            defaultName: '实验类型'
        });
        // $("#IsEvaluate").ysComboBox({
        //     data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())),
        //     defaultName: '是否考核'
        // });
        loadVersionBase();

        initGrid();
    });
    function loadVersionBase() {
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/PlanExamParameter/GetVersionJson")' + "?id=" + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var arrData = [];
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        arrData = arrData.concat(obj.Data);
                    }
                    $('#VersionId').ysComboBox({
                        data: arrData,
                        key: 'Id',
                        value: 'VersionName',
                        defaultName: '实验教材版本'
                    });
                    $('#VersionId').ysComboBox('setValue', -1);
                }
            }
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/PlanExamParameter/GetExperimentPageListJson")';
        $('#gridTable').ysTable({
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sortName: " BaseCreateTime ASC ",
            pageSize: 30,
            url: queryUrl,
            columns: [
                { field: 'index', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                { field: 'ExperimentName', title: '实验名称', sortable: true, width: 300, halign: 'center', valign: 'middle' },
                {
                    field: 'VersionBaseId', title: '实验教材', sortable: true, width: 180, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        return row.VersionBaseName;
                    }
                },
                { field: 'Chapter', title: '章节', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'IsNeedDo',
                    title: '实验要求',
                    sortable: true,
                    width: 100,
                    halign: 'center',
                    valign: 'middle',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var html = '@IsNeedEnum.MustDo.GetDescription()';
                        if (@IsNeedEnum.SelectToDo.ParseToInt() == value) {
                            html = '@IsNeedEnum.SelectToDo.GetDescription()';
                        }
                        return html;
                    }
                },
                {
                    field: 'ExperimentType',
                    title: '实验类型',
                    sortable: true,
                    width: 100,
                    halign: 'center',
                    valign: 'middle',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return value == @ExperimentTypeEnum.Demo.ParseToInt() ? "@ExperimentTypeEnum.Demo.GetDescription()" : "@ExperimentTypeEnum.Group.GetDescription()";
                    }
                },
                // {
                //     field: 'IsEvaluate', title: '是否考核', sortable: true, halign: 'center', align: 'center', width: 70,
                //     formatter: function (value, row, index) {
                //         return value == @IsStatusEnum.Yes.ParseToInt() ? "@IsStatusEnum.Yes.GetDescription()" : "<span style='color:red'>@IsStatusEnum.No.GetDescription()</span>";
                //     }
                // },
                {
                    field: 'opt2', title: '操作', width: 160, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\',)"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.PlanParameterSetId = id;
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $('#VersionId').ysComboBox('setValue', -1);
        $('#IsNeedDo').ysComboBox('setValue', -1);
        $('#ExperimentType').ysComboBox('setValue', -1);
        // $('#IsEvaluate').ysComboBox('setValue', -1);
        $('#Name').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function deleteForm(objid) {
        ys.confirm('确认要删除当前的数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanExamParameter/DeleteFormJson")' + '?ids=' + objid,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function showSaveForm() {
        ys.openDialog({
            title: '添加实验',
            content: '@Url.Content("~/ExperimentTeachManage/PlanExamParameter/ExperimentForm")',
            width: '1000px',
            btn: ['添加', '取消'],
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
