﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="Level" col="Level"></div>
                    </li>
                    <li>
                        <div id="IsEvaluate" col="IsEvaluate"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a id="btnSearch" class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a class="btn btn-primary" onclick="showSetStatuzForm()"><i class="fa fa-edit"></i> 设置状态</a>
            <a class="btn btn-primary" onclick="showSeIsEvaluateForm()"><i class="fa fa-edit"></i> 设置是否评估</a>
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var standardid = ys.request("id");
    $(function () {
        $('#Level').ysComboBox({ url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1011", key: 'DictionaryId', value: 'DicName', defaultName: '达标级别'});
        $("#IsEvaluate").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), defaultName:'是否评估'});
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/GetPageListJson")' + '?StandardId=' + standardid;
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                {
                    title: '操作', width: 140, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { checkbox: true, visible: true },
                {
                    field: 'Statuz', title: '状态', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' ,formatter: function (value, row, index) {
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                { field: 'RailStart', title: '轨数起', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center', },
                { field: 'RailEnd', title: '轨数止', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'LevelName', title: '达标级别', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'RoomNum', title: '数量（间）', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'right'},
                { field: 'RoomArea', title: '单室面积（㎡）', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'right' },
                { field: 'TotalRoomArea', title: '面积（㎡）', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'right' },
                {
                    field: 'IsEvaluate', title: '是否评估', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' ,formatter: function (value, row, index) {
                        if (row.IsEvaluate == "@IsEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@IsEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@IsEnum.No.GetDescription()" + '</span>';
                        }
                }
                },
                { field: 'Remark', title: '备注' },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#Level").ysComboBox('setValue', -1);
        $("#IsEvaluate").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/Form")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function showSetStatuzForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length == 0) {
            ys.msgError("请选择需要设置状态的数据！");
            return;
        }
        ys.openDialog({
            title: '设置状态',
            content: '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/SetStateForm")',
            width: '768px',
            height: '250px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function showSeIsEvaluateForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length == 0) {
            ys.msgError("请选择需要设置是否评估的数据！");
            return;
        }
        ys.openDialog({
            title: '设置是否评估',
            content: '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/SetEvaluateForm")',
            width: '768px',
            height: '250px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm(id) {
        var ids = '';
        var tagMsg = '';
        if (id > 0) {
            ids = id;
            tagMsg = '确定要删除当前数据吗？';
        } else {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (selectedRow.length == 0) {
                ys.msgError('请选择需要删除的数据!');
                return '';
            }
            ids = ys.getIds(selectedRow);
            tagMsg = '确认要删除选中的' + selectedRow.length + '条数据吗？';
        }
        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateEnorm/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
    /**设置状态、设置是否评估页面获取 */
    function getSelectIds() {
        var ids = '';
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            ids = ys.getIds(selectedRow);
        }
        return ids;
    }
</script>
