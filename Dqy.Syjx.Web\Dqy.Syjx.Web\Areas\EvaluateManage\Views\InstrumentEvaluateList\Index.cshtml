﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container{width:100% !important;}
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div  id="AllocateType" col="AllocateType"  style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div  id="IsEvaluate" col="IsEvaluate"  style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input type="text" id="Name" col="Name"  style="display:inline-block;width:160px;" placeholder="仪器代码、名称"/>
                    </li>
                    <li>
                        <input id="EvaluateStandardId" col="EvaluateStandardId" type="hidden" />
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a class="btn btn-success" onclick="showImportForm(true)"><i class="fa fa-uploa"></i> 导入</a>
            <a class="btn btn-success" onclick="showSetStatuzForm(true)"><i class="fa fa-edit"></i> 设置状态</a>
            <a class="btn btn-success" onclick="showSetEvaluateForm(true)"><i class="fa fa-edit"></i> 设置是否评估</a>
            <a class="btn btn-success" onclick="showSetRatioForm(true)"><i class="fa fa-edit"></i> 设置调控系数</a>
            <a class="btn btn-success" onclick="showSmallNumForm(true)"><i class="fa fa-edit"></i> 设置去小数的值</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        $("#EvaluateStandardId").val(id);
        $("#AllocateType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(AllocateTypeEnum).EnumToDictionaryString())), defaultName: '配备要求'});
        $("#IsEvaluate").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())),defaultName:'是否评估' });
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sortName: 'Id',
            sortOrder: 'ASC',
            columns: [
                {
                    title: '操作', width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { checkbox: true, visible: true },
                {
                    field: 'Statuz', title: '状态', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                { field: 'SourceCode', title: '分类代码', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'ContrastCode', title: '比对代码', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'EvaluateCode', title: '评估代码', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'InstrumentName', title: '仪器名称', sortable: true, halign: 'center', valign: 'middle' },
                {
                    field: 'AllocateType', title: '配备要求', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.AllocateType == "@AllocateTypeEnum.MustMatch.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@AllocateTypeEnum.MustMatch.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@AllocateTypeEnum.SelectMatch.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'Num', title: '数量<br/>（N）', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'right'
                },
                { field: 'UnitName', title: '单位', sortable: true, width: 60, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'IsEvaluate', title: '是否评估<br/>（W）', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.IsEvaluate == "@IsEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@IsEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@IsEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'RegulationRatio', title: '调控系数<br/>（Y）', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'right'
                },
                { field: 'ParamDemand', title: '参数要求', halign: 'center', valign: 'middle' },
                { field: 'StandardCodename', title: '执行标准代号', halign: 'center', valign: 'middle' },
                { field: 'Remark', title: '备注', sortable: true, halign: 'center', valign: 'middle', },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }
    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $('#AllocateType').ysComboBox('setValue', '-1');
        $('#IsEvaluate').ysComboBox('setValue', '-1');
        $("#Name").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/Form")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }
    function showImportForm() {
         ys.openDialog({
            title: '导入清单',
            content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/Import")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function showSetStatuzForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length >0) {
            ys.openDialog({
            title: '设置状态',
            content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SetStatuzForm")',
            width: '768px',
            height: '200px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
        } else {
            ys.msgError("请选择需要设置状态的数据。");
        }
    }
    function showSetEvaluateForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            ys.openDialog({
                title: '设置是否评估',
                content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SetEvaluateForm")',
                width: '768px',
                height: '200px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        } else {
            ys.msgError("请选择需要设置是否评估的数据。");
        }
    }
    function showSetRatioForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            ys.openDialog({
                title: '设置调控系数',
                content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SetRatioForm")',
                width: '768px',
                height: '200px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        } else {
            ys.msgError("请选择需要设置调控系数的数据。");
        }
    }
    function showSmallNumForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            ys.openDialog({
                title: '设置去小数的值',
                content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateList/SetSmallNumForm")',
                width: '768px',
                height: '200px',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        } else {
            ys.msgError("请选择需要设置去小数的值的数据。");
        }
    }
    function getSelectIds() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        var ids = '';
        if (selectedRow.length > 0) {
            ids = ys.getIds(selectedRow);
        }
        return ids;
    }
</script>
