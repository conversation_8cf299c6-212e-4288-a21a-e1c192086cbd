﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time" id="liApprovalDate" style="display:none;">
                        <label>审批日期： </label><input id="auditStartDate" col="AuditStartDate" type="text" class="time-input" placeholder="开始时间" style="width:100px;" />
                        <span>-</span>
                        <input id="auditEndDate" col="AuditEndDate" type="text" class="time-input" placeholder="结束时间" style="width:100px;" />
                    </li>
                    <li>
                        <span id="purchaseYear" col="PurchaseYear" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="stageId" col="StageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="instrumentClassId" col="InstrumentClassId" style="display:inline-block;width:150px;"></span>
                    </li>
                   
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:90px;"></span>
                    </li>
                    <li id="liUserId" style="display:none;">
                        <span id="userId" col="UserId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="purchaseType" col="PurchaseType" style="display:inline-block;width:90px;"></span>
                    </li>
                    <li>
                        <span id="allocateType" col="allocateType" style="display:inline-block;width:90px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" type="text" placeholder="仪器名称" style="width:120px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
            <a id="btnSet" class="btn btn-success disabled" onclick="setForm()"><i class="fa fa-asterisk"></i> 设置采购方式</a>
            <a id="btnExportMx" class="btn btn-warning" onclick="exportmxForm()"><i class="fa fa-download"></i> 按明细导出</a>
            <a id="btnExportHz" class="btn btn-warning" onclick="exporthzForm()"><i class="fa fa-download"></i> 按汇总导出</a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">

    var showCode = false;
    var purchaseYear = ys.request("purchaseYear");
    var stageId = ys.request("stageId");
    var courseId = ys.request("courseId");
    var schoolId = ys.request("schoolId");
    var countyId = ys.request("countyId");

    var SELECTED_IDS = '';
    var BasePageCode = 101016;//年度采购计划101016
    var ExportSetMultipleCode = [];
    $(function () {
         if (@UnitType == @UnitTypeEnum.School.ParseToInt()) {
             $('#liApprovalDate').show();
             $('#liUserId').show();
             $('#liSchoolId').hide();
        }
        else {
             $('#liApprovalDate').hide();
             $('#liUserId').hide();
             $('#liSchoolId').show();
             loadSchool(0);
            showCode = true;
            ExportSetMultipleCode=[{BasePageCode:"101017",Title:"按明细导出"},{BasePageCode:"101018",Title:"按汇总导出"}];
        }
        laydate.render({ elem: '#auditStartDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#auditEndDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        getYear();
        getInstrumentClass();
        loadSchoolStage();
        getCourse();
        loadUser();
        $("#purchaseType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(PurchaseTypeEnum).EnumToDictionaryString())), defaultName: '采购方式' });
        $("#allocateType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(AllocateTypeConditionEnum).EnumToDictionaryString())), defaultName: '配置要求' }); 

        if (purchaseYear > 0) $('#purchaseYear').ysComboBox('setValue', purchaseYear);
        if (stageId > 0) $('#stageId').ysComboBox('setValue', stageId);
        if (courseId > 0) $('#courseId').ysComboBox('setValue', courseId);

        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnSet')) {
                $('#btnSet').toggleClass('disabled', !ids.length);
            }
        });

        initGrid();
        
        

    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseStatistics/GetPurchaseYearStatisticsPageListJson")' + '?IsShowTotalRow=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            exportSetMultipleCode:ExportSetMultipleCode,
            columns: [
                { checkbox: true },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt1,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id)
                            html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-eye"></i>查看</a>', row.Id);
                        return html;
                    }
                },
                //{
                //    field: 'SchoolName', title: '单位名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.SchoolName,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value;
                //        else {
                //            if (@UnitType != @UnitTypeEnum.School.ParseToInt()) return '<b>总计（元）：</b>';
                //            else return '';
                //        }
                //    }
                //},
                //{
                //    field: 'Stage', title: '学段', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value;
                //        return '';
                //    }
                //},
                {
                    field: 'Course', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        return '<b>总计（元）：</b>';
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: showCode,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                //{
                //    field: 'PurchaseYear', title: '采购年度', halign: 'center', align: 'center', sortable: true, width: 50,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value;
                //        else return '';
                //    }
                //},
                //{
                //    field: 'InstrumentClassName', title: '仪器类别', halign: 'center', align: 'left', sortable: true, width: 140,
                //    formatter: function (value, row, index) {
                //         if (row.Id) return value;
                //        else {
                //            if (@UnitType == @UnitTypeEnum.School.ParseToInt()) return '<b>总计（元）：</b>';
                //            else return '';
                //        }
                //    }
                //},
                //{
                //    field: 'LastCode', title: '仪器分类代码', halign: 'center', align: 'center', sortable: true, width: 100,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value;
                //        else return '';
                //    }
                //},
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            var html = "";
                            if (row.IsDangerChemical == 1) {
                                html += Syjx.GetDangerHtml();
                            }
                            html += value;
                            return html;
                        }
                        else return '';
                    }
                },
                {
                    field: 'AllocateType', title: '配备要求', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.AllocateType,
                    formatter: function (value, row, index) {
                         if (row.Id){
                             if (row.AllocateType == '1') {
                                return '必配';
                            }
                            else if(row.AllocateType == '2'){
                                return '选配';
                            }else{
                                return '--';
                            }
                         }else{
                              return '';
                         }
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                {
                    field: 'Num', title: '数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Price', title: '单价', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        if (row.Id) return ComBox.ToLocaleString(value);
                        return '';
                    }
                },
                {
                    field: 'AmountSum', title: '金额', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        if (row.Id) return ComBox.ToLocaleString(value);
                        else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                    }
                },
                
                {
                    field: 'UserName', title: '填报人', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UserName, visible: (@UnitType == @UnitTypeEnum.School.ParseToInt()),
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        return '';
                    }
                },
                {
                    field: 'PurchaseType', title: '采购方式', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.PurchaseType,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            if (value == 1) return '集中采购';
                            else if (value == 2) return '自行采购';
                            else return '-';
                        }
                        else
                            return '';
                    }
                },
                {
                    field: 'ApprovalDate', title: '审批日期', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime, visible: (@UnitType == @UnitTypeEnum.School.ParseToInt()),
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                //去除总计行的checkbox
                let rows = $('#gridTable').bootstrapTable('getData');
                if (rows.length > 0) {
                    if (rows && !rows[rows.length - 1].Id) {
                        //最后一个tr的第一个td数据置空：用数据控制的总计行页面放大缩小会导致失效，应用showFooter控制
                        $('#gridTable tbody').find('tr:last').find('td:first').html('')
                    }
                }

                if ($('#btnSet')) {
                    $('#btnSet').toggleClass('disabled', !$("#gridTable").bootstrapTable("getSelections").length);
                }

                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
                
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function look(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseDetail")' + '?id=' + id;
        //createMenuItem(url, "查看计划");
        ys.openDialog({
            title: "查看计划",
            width: "800px",
            content: url,
            btn: ['关闭'],
            yes: function (index) {
                $.modal.close(index);
            },
        });
    }

    function loadUser() {
        $('#userId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '填报人'
        });
    }

    function resetGrid() {
        //清空条件
        $('#auditStartDate').val('');
        $('#auditEndDate').val('');
        loadSchool(0);
        $('#schoolId').ysComboBox('setValue', -1);
        $('#purchaseYear').ysComboBox('setValue', new Date().getFullYear());
        $('#instrumentClassId').ysComboBox('setValue', -1);
        $('#stageId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#userId').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $("#purchaseType").ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function exportForm() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/ExportPurchaseStatistics")';//年度采购计划101016
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = BasePageCode;//年度采购计划101016
        ys.exportExcel(url, postData);
    }

    function getYear() {
        var currDate = new Date();
        var currYear = currDate.getFullYear();
        var yearData = [];
        var planYear = [];
        for (i = 0; i < 10; i++) {
            if (currYear + 1 - i < 2021)
                continue;
            yearData.push({ "id": currYear + 1 - i, "text": currYear + 1 - i });
        }
        $("#purchaseYear").ysComboBox({
            data: yearData,
            key: "id",
            value: "text",
            defaultName :'采购年度'
        }).ysComboBox('setValue', currYear);

    }

    function getInstrumentClass() {
        var param = { Depth: 1, ClassType: 1 };
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetInstrumentStandard")',
            data: { param: param },
            type: 'post',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#instrumentClassId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '仪器类别'
                    });
                }
            }
        });
    }

    function getCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadSchoolStage() {
        $('#stageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                var schoolStageId = $('#stageId').ysComboBox("getValue");
                if (schoolStageId > 0) {
                    loadSchool(schoolStageId);
                }
            }
        });
    }

    function loadSchool(schoolStageId) {
        var url = '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=" + schoolStageId;
        if (@UnitType == @UnitTypeEnum.City.ParseToInt())
            url = '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId;
        ys.ajax({
            url: url,
            data: null,
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });
                    if (schoolId > 0) {
                        $('#schoolId').ysComboBox('setValue', schoolId);
                    }
                }
            }
        });
    }

    function setForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
            if (ids.indexOf(',null') > -1) {
                ids = ids.substring(0, ids.lastIndexOf(',null'));
            }
            SELECTED_IDS = ids;
            ys.openDialog({
                title: '设置采购方式',
                width: '800px',
                height:'auto',
                content: '@Url.Content("~/InstrumentManage/PurchaseStatistics/SetPurchaseForm")',
                callback: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']];
                    iframeWin.saveForm(index);
                }
            });
        }
    }

    function exportmxForm() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/ExportPurchaseStatisticsByDetail")';//年度采购计划明细表(101017)
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = 101017;
        ys.exportExcel(url, postData);
    }
    function exporthzForm() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseStatistics/ExportPurchaseStatisticsBySummary")';//年度采购计划汇总表(101018)
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = 101018;
        ys.exportExcel(url, postData);
    }
</script>
