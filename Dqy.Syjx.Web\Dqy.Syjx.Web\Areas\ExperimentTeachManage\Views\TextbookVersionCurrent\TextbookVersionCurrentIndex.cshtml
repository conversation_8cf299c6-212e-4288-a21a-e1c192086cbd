﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="schoolStage" col="SchoolStage" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="gradeId" col="GradeId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="versionName" col="VersionName" placeholder="实验教材版本" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 单条新增</a>
            <a id="btnDelete" class="btn btn-primary disabled" onclick="showCopyAddForm()"><i class="fa fa-plus"></i> 复制新增</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSchoolStage();
        loadGrade();
        loadCourse();
        loadSchoolTerm();
        //loadSchoolYearStart();
        
        
        initGrid();
    });

    function loadSchoolStage() {
        $('#schoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }
    function loadGrade(schoolStage) {
        $('#gradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级'
        });
    }
    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()' + '&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学科'
        });
    }
    function loadSchoolTerm() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
    }
    function loadSchoolYearStart() {
        ComBox.SchoolTermYear($('#schoolYearStart'), '', '学年');
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/TextbookVersionCurrent/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'SchoolStageName', title: '学段', sortable: true, halign: 'center', align: 'center' },
                {
                    field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.SchoolStage) {
                            html = '--';
                            if (row.SchoolStage != @SchoolStageEnum.GaoZhong.ParseToInt()) {
                                html = value;
                            }
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center' },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center',  
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.SchoolStage) {
                            html = '--';
                            if (row.SchoolStage != @SchoolStageEnum.GaoZhong.ParseToInt()) {
                                if (value == @SchoolTermEnum.LastSemester.ParseToInt()) {
                                    html = '@SchoolTermEnum.LastSemester.GetDescription()';
                                } else {
                                    html = '@SchoolTermEnum.NextSemester.GetDescription()';
                                }
                            }
                        }
                        return html;
                    }
                },
                { field: 'VersionName', title: '实验教材版本', sortable: true, halign: 'center', align: 'center' },
                {
                    field: 'look', title: '实验目录', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}" versionName="{1}" textbookVersionBaseId="{2}"><i class="fa fa-eye"></i>查看</a> ', row.Id, row.VersionName, row.TextbookVersionBaseId);
                    }
                },
                // { field: 'CountyNames', title: '区县', halign: 'center', align: 'left', width: 500 },
                {
                    field: 'BaseModifyTime', title: '更新时间', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
                {
                    field: '', title: '操作', halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="edit(this)" value="{0}"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                        html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="del(this)" value="{0}"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        //清空条件
        $('#schoolStage').ysComboBox('setValue', -1);
        $('#gradeId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        //$('#schoolYearStart').ysComboBox('setValue', -1);
        $('#schoolTerm').ysComboBox('setValue', -1);
        $('#versionName').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function look(obj) {
        var textbookVersionBaseId = $(obj).attr('textbookVersionBaseId');
        var url = '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/TextbookVersionDetailIndex")' + '?textbookVersionBaseId=' + textbookVersionBaseId + '&islook=1';
        createMenuItem(url, "实验清单");
    }

    function showSaveForm(id) {
        ys.openDialog({
            title: (id > 0 ? '编辑' : '添加') + '<span style="color:red;margin-left:20px;">操作完成后，请务必前往【达标参数设置】页面更新实验数量。</span>',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionCurrent/TextbookVersionCurrentForm")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showCopyAddForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
        }
        ys.openDialog({
            title: '复制新增<span style="color:red;margin-left:20px;">新增完成后，请务必前往【达标参数设置】页面更新实验数量。</span>',
            width: '800px',
            height: '300px',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionCurrent/CopyAddForm")' + '?ids=' + ids,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function del(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要删除该数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionCurrent/DeleteFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function edit(obj) {
        showSaveForm($(obj).attr('value'));
    }

</script>
