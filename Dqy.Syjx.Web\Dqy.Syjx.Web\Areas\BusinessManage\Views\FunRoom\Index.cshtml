﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: 100% !important;
    }
</style>
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/css/metroStyle/metroStyle.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/zTree/v3/js/ztree.min.js"))
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="DictionaryId1006A" col="DictionaryId1006A" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006B" col="DictionaryId1006B" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="RoomAttribute" col="RoomAttribute" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1005" col="DictionaryId1005" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SysDepartment" col="SysDepartment" style="display: inline-block; width: 200px;"></div>
                        <input type="hidden" id="SysDepartmentId" col="SysDepartmentId" value="" />
                    </li>
                    <li>
                        <div id="SysUserId" col="SysUserId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="Address" style="display: inline-block;width:200px;"></div>
                        <input type="hidden" id="AddressId" col="AddressId" value="" />
                    </li>
                    <li>
                        <div id="Statuz" col="Statuz" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input type="text" placeholder="名称" id="Name" col="Name" style="display: inline-block;width:180px;" value="" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnSingleDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var BasePageCode = 101005;
    $(function () {
        $("#DictionaryId1006A").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '一级分类',
            onChange: function () {
                var dicA = $("#DictionaryId1006A").ysComboBox("getValue");
                loadDictionaryId1006B(dicA);
            }
        });
        loadDictionaryId1006B(0);
        loadNature();
        loadSubject();
        loadSysDepartment();
        loadSysUser(0);
        loadAddress();
        $("#Statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), defaultName: '状态' });
        initGrid();
    });
    function loadDictionaryId1006B(dicA) {
        if (parseInt(dicA) > 0) {
            $("#DictionaryId1006B").ysComboBox({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5' + '&Pid=' + dicA,
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类'
            });
        } else {
            $("#DictionaryId1006B").ysComboBox({
                data: [],
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类'
            });
        }
    }
    function loadNature() {
        $("#RoomAttribute").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1009',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '属性'
        });
    }
    function loadSubject() {
        $("#DictionaryId1005").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1005&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '适用学科'
        });
    }
    function loadSysDepartment() {
        $('#SysDepartment').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Department/GetDepartmentTreeListJson")',
            defaultName: '管理部门',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    //if (typeof (treeNode) == "string") {
                    //    treeNode = JSON.parse(treeNode);
                    //}
                    //loadSysUser(treeNode.id);
                    $("#SysDepartmentId").val(treeNode.id);
                }
            }
        });
        $('#SysDepartment').ysComboBoxTree('setValue', -1);
    }
    function loadSysUser(departmentid) {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoom/GetUserListJson")' + '?DepartmentId=' + departmentid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#SysUserId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'RealName',
                        defaultName: '管理人'
                    });
                    if (obj.Data.length == 1) {
                        $("#SysUserId").ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                } else {
                    $('#SysUserId').ysComboBox({ data: [], key: 'Id', value: 'RealName', defaultName: '管理人' });
                    ys.msgError(obj.Message);
                }
            }
        });
    }
    function loadAddress() {
        $('#Address').ysComboBoxTree({
            url: '@Url.Content("~/OrganizationManage/Address/GetZtreeListJson")',
            class: 'form-control',
            defaultName: '地点',
            callback: {
                customOnClick: function (event, treeId, treeNode) {
                    $("#AddressId").val(treeNode.id);
                }
            }
        });
        $('#Address').ysComboBoxTree('setValue', -1);
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/BusinessManage/FunRoom/GetPageByUserListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                { checkbox: true, visible: true },
                {
                    title: '操作', halign: 'center', valign: 'middle', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="statuzOnChange(\'' + row.Id + '\',2,\'禁用\')"><i class="fa fa-remove"></i>禁用</a>&nbsp;&nbsp;');
                        } else {
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="statuzOnChange(\'' + row.Id + '\',1,\'启用\')"><i class="fa fa-check"></i>启用</a>&nbsp;&nbsp;');
                        }
                        return actions.join('');
                    }
                },
                { field: 'SubjectName', title: '适用学科', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Name', title: '实验（专用）室名称', sortable: true, width: 260, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            switch (row.NatureName) {
                                case '@FunNatureTypeEnum.Room.GetDescription()':
                                    html += Syjx.GetCircleRoomHtml();
                                    break;
                                case '@FunNatureTypeEnum.ZhuanRoom.GetDescription()':
                                    html += Syjx.GetCircleZhuanRoomHtml();
                                    break;
                                case '@FunNatureTypeEnum.FuRoom.GetDescription()':
                                    html += Syjx.GetCircleFuRoomHtml();
                                    break;
                            }
                            html += row.Name;

                        }
                        return html;
                    }
                },
                { field: 'UseArea', title: '面积(㎡)', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'right' },
                { field: 'DepartmentName', title: '管理部门', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'AddressId', title: '地点', sortable: true, width: 160, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.HouseName != undefined && row.HouseName.length > 0) {
                            html = (row.HouseName + "(" + row.RoomName + ")");
                        } else if (row.RoomName != undefined && row.RoomName.length > 0) {
                            html = row.RoomName;
                        }
                        return html;
                    }
                },
                {
                    field: 'Statuz', title: '状态', sortable: true, width: 60, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    title: '二维码', halign: 'center', valign: 'middle', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.QRCode != undefined && row.QRCode.length > 0) {
                            actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="queryQRCodeForm(\'' + row.Id + '\')"><i class="fa fa-check"></i>查看</a>&nbsp;');
                        } else {
                            actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="createQRCodeForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>生成</a>&nbsp;');
                        }
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }
    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#DictionaryId1006A").ysComboBox('setValue', -1);
        loadDictionaryId1006B(0);
        $("#RoomAttribute").ysComboBox('setValue', -1);
        $("#DictionaryId1005").ysComboBox('setValue', -1);
        $('#SysDepartment').ysComboBoxTree('setValue', -1);
        $('#SysUserId').ysComboBox('setValue', -1);
        $("#Statuz").ysComboBox('setValue', -1);
        $('#Address').ysComboBoxTree('setValue', -1);
        $('#Name').val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd, id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError('每次只能修改一条数据，请选择一条数据！');
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/BusinessManage/FunRoom/Form")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var id = '';
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (!ys.checkRowEdit(selectedRow)) {
            ys.msgError('每次只能删除一条数据，请选择一条数据！');
            return;
        } else {
            id = selectedRow[0].Id;
        }
        ys.confirm('确认要删除当前选中的实验室数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/DeleteFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function statuzOnChange(Id, statuz, strStatuz) {
        ys.confirm("确认要" + strStatuz + "吗？", function () {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/UpdateStatuz")' + '?id=' + Id + '&statuz=' + statuz,
                type: "post",
                error: ys.ajaxError,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(strStatuz + " 成功");
                        searchGrid();
                    }
                    else {
                        ys.msgError(strStatuz + " 失败!" + obj.Message);
                    }
                }
            });
        });
    }
    /**打开管理员授权页面 */
    function OpenSubjectSet() {
        var url = '@Url.Content("~/BusinessManage/UserSchoolStageSubject/Index")';
        createMenuItem(url, "管理员授权");
    }

    function createQRCodeForm(id) {
        var ids = '';
        var msg = '';
        if (id != 0) {
            ids = id;
            msg = '确认要生成该条数据的二维码吗？';
        } else {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                ids = ys.getIds(selectedRow);
                msg = ('确认要生成选中的' + selectedRow.length + '条数据的二维码吗？');
            } else {
                ys.msgError('请选择需要生成二维码的数据！');
                return;
            }
        }
        ys.confirm(msg, function () {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoom/CreateQRCodeFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function queryQRCodeForm(id) {
        ys.openDialog({
            title: '二维码查看',
            content: '@Url.Content("~/BusinessManage/FunRoom/QRCodeForm")' + '?id=' + id,
            width: '700px',
            height: '600px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function removeConfirm() {
        $(".layui-layer-btn0").hide();
    }

    function exportForm() {
        var url = '@Url.Content("~/BusinessManage/FunRoom/ExportList")';//实验室信息（101005）
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "Id", "order": "desc", "offset": 0, "limit": 10 });
        var postData = $('#searchDiv').getWebControls(pagination);
        postData.BasePageCode = BasePageCode;//实验室信息
        ys.exportExcel(url, postData);
    }
</script>
