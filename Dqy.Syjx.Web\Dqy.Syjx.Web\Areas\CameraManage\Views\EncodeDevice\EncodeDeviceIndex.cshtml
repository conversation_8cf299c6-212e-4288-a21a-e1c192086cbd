﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input id="CameraName" col="CameraName" type="text" placeholder="摄像头名称"/>
                        &nbsp;
                        <input id="DeviceCode" col="DeviceCode" type="text" placeholder="设备编号"/>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
            <div class="btn-group d-flex" role="group" id="toolbar">
                <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
                <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
                <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
            </div>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/CameraManage/EncodeDevice/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'Id', title: 'Id', visible: false },
                { field: 'DeviceCode', title: '设备编号', sortable: true, width: 380, halign: 'center', align: 'center', },
                { field: 'CameraName', title: '摄像头名称', sortable: true, width: 300, halign: 'center', align: 'center', },
                { field: 'ChannelSeq', title: '通道号', sortable: true, halign: 'center', align: 'center',},
                {
                    field: 'StreamType', title: '码流类型', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.StreamType == '1') {
                            return '主码流';
                        } else if (row.StreamType == '2') {
                            return '辅码流';
                        } else {
                            return '';
                        }
                    }
                },
                {
                    field: 'DeviceBrand', title: '设备品牌', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.DeviceBrand == '1') {
                            return '海康';
                        } else if (row.DeviceBrand == '2') {
                            return '大华';
                        } else {
                            return '其他';
                        }
                    }
                },
                { field: 'IP', title: 'IP地址', sortable: true, halign: 'center', align: 'center' },
                { field: 'Port', title: '端口号', sortable: true, halign: 'center', align: 'center' },
                { field: 'UnitName', title: '所属单位', sortable: true, halign: 'center', align: 'center' }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        $('#CameraName').val("");
        $('#DeviceCode').val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/CameraManage/EncodeDevice/EncodeDeviceForm")' + '?id=' + id,
            width: '768px',
            height: '500px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/CameraManage/EncodeDevice/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }
</script>
