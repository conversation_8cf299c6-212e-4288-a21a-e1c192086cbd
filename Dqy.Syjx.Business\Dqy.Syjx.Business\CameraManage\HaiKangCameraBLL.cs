﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dqy.Syjx.Util;
using Dqy.Syjx.Util.Extension;
using Dqy.Syjx.Util.Model;
using Dqy.Syjx.Model.Input.CameraManage;
using Dqy.Syjx.Entity.CameraManage;
using Dqy.Syjx.Model.Param.CameraManage;
using Dqy.Syjx.Service.CameraManage;
using Dqy.Syjx.Data.Repository;
using Dqy.Syjx.Model.Result;
using Dqy.Syjx.Service.OrganizationManage;
using Dqy.Syjx.Service.SystemManage;
using Dqy.Syjx.Entity.QueryStatisticsManage;
using Dqy.Syjx.Entity.SystemManage;
using Dqy.Syjx.Model.Param.OrganizationManage;
using Dqy.Syjx.Model.Param.SystemManage;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using NetTopologySuite.Index.HPRtree;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using Newtonsoft.Json;
using NPOI.SS.Formula.Functions;
using System.Threading;
using System.Drawing.Drawing2D;
using Quartz.Impl.Triggers;
using Dqy.Syjx.Service.QueryStatisticsManage;
using Dqy.Syjx.Model.Param.QueryStatisticsManage;

namespace Dqy.Syjx.Business.CameraManage
{
    /// <summary>
    /// 创 建：admin
    /// 日 期：2023-03-28 13:45
    /// 描 述：业务类
    /// </summary>
    public class HaiKangCameraBLL
    {
        int isValidPicPeople = 1; //采集的摄像头图片人头数是否有效
        private HaiKangCameraService ******************** = new HaiKangCameraService();
        private UnitScheduleService scheduleService = new UnitScheduleService();
        private ConfigSetService configSetService = new ConfigSetService();
     
        #region 获取数据
        public async Task<TData<List<CameraPointEntity>>> GetList(HaiKangCameraListParam param)
        {
            TData<List<CameraPointEntity>> obj = new TData<List<CameraPointEntity>>();
            obj.Data = await ********************.GetList(param);
            obj.Total = obj.Data.Count;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<List<CameraPointEntity>>> GetPageList(HaiKangCameraListParam param, Pagination pagination)
        {
            TData<List<CameraPointEntity>> obj = new TData<List<CameraPointEntity>>();
            obj.Data = await ********************.GetPageList(param, pagination);
            obj.Total = pagination.TotalCount;
            obj.Tag = 1;
            return obj;
        }

        public async Task<TData<CameraPointEntity>> GetEntity(long id)
        {
            TData<CameraPointEntity> obj = new TData<CameraPointEntity>();
            obj.Data = await ********************.GetEntity(id);
            if (obj.Data != null)
            {
                obj.Tag = 1;
            }
            return obj;
        }
        #endregion

        #region 提交数据
        public async Task<TData<string>> SaveForm(HaiKangCameraInput model)
        {
            var entity = new CameraPointEntity();
            if (model.Id > 0)
            {
                entity = await ********************.GetEntity(model.Id);
            }
            entity.Id = model.Id;
            entity.CameraIndexCode = model.CameraIndexCode;
            entity.CameraName = model.CameraName;
            entity.RegionIndexCode = model.RegionIndexCode;
            entity.RegionName = model.RegionName;
            entity.ParentIndexCode = model.ParentIndexCode;
            entity.RegionPath = model.RegionPath;
            entity.RegionPathName = model.RegionPathName;
            entity.CreateTime = DateTime.Now;
            entity.Status = model.Status ?? 0;
            TData<string> obj = new TData<string>();
            await ********************.SaveForm(entity);
            obj.Data = entity.Id.ParseToString();
            obj.Tag = 1;
            return obj;
        }

        /// <summary>
        /// 修改摄像机状态（在线、不在线）
        /// </summary>
        /// <returns></returns>
        public async Task<TData<string>> UpdateStatuz(HaiKangCameraInput model)
        {
            TData<string> obj = new TData<string>();
            var camerapointlist = await ********************.GetList(new HaiKangCameraListParam() { CameraIndexCode = model.CameraIndexCode });
            if (camerapointlist.Count > 0)
            {
                CameraPointEntity entity = camerapointlist.LastOrDefault();
                entity.Status = model.Status.Value; //修改状态（在线、不在线）
                await ********************.SaveForm(entity);
                obj.Tag = 1;
                obj.Data = entity.Id.ToString();
            }
            else
            {
                obj.Tag = 0;
            }
            return obj;
        }

        public async Task<TData> DeleteForm(string ids)
        {
            TData obj = new TData();
            if (ids.IsEmpty()) { return obj; }
            if (ids.Length > 1)
                await ********************.DeleteForm(ids);
            else
                obj.Tag = 0;
            return obj;
        }
        #endregion

        #region 私有方法
        #endregion

        #region 海康业务
        //解析海康推送数据
        public async Task<TData<string>> AnalysisData()
        {
            TData<string> obj = new TData<string>();          
            while (true)
            {
                Repository hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2);
                string sql = "SELECT top 2000 Id,DataInfo,RegTime,statuz FROM HaiKangData WHERE statuz = 0 ORDER BY RegTime ASC";
                var list = await hkdb.FindList<HaiKangDataModel>(sql);
                if (list.Count() > 0)
                {
                    LogHelper.Info($"【解析海康原始数据】查询到数据：{list.Count()}条");
                    //string strInsertSqls = ""; //拼接insert HaiKangEventNotify表语句
                    //string strUpdateSqls = ""; //拼接update HaiKangData表语句

                    //string pattern = "framesPeopleCounting_number\":(\\d+)";  //人数（南京海康平台报警推送数据：人数）
                    string pattern = "count\":(\\d+)";  //人数（泰兴海康平台报警推送数据：人数）

                    //string pattern2 = "imageUrl\":\"(.*?)\""; //图片（南京海康平台报警推送数据：图片地址）
                    string pattern2 = "imageUrl\":\"(.*?)\""; //图片（泰兴海康平台报警推送数据：图片地址）
                    Regex regex2 = new Regex(pattern2);
                    string pattern3 = "(?<=\")channelName\":\"(.*?)(?=\")"; //通道名称
                    Regex regex3 = new Regex(pattern3);

                    //string pattern4 = "sendTime\":\"(.*?)(?=\")"; //时间
                    //Regex regex4 = new Regex(pattern4);

                    string cameraIndexCodeExp = "(?<=\")cameraIndexCode\":\"(.*?)(?=\")"; //通道名称
                    Regex cameraIndexCodeRegex = new Regex(cameraIndexCodeExp);

                    int index = 1;
                    StringBuilder sbInSql = new StringBuilder();
                    StringBuilder sbUpSql = new StringBuilder();

                    foreach (var item in list)
                    {
                        try
                        {
                            string peopleCounting_number = "0"; //人数
                            string imageUrl = ""; //图片
                            string channelName = ""; //通道名称
                            string cameraIndexCode = "";//摄像头编码
                                                        //DateTime sendTime = DateTime.Now; //发送时间      

                            Regex regex = new Regex(pattern);// 创建正则表达式对象  
                                                             // 字符串中查找匹配项  
                            item.DataInfo = item.DataInfo.Replace("alarmCount", "count").Replace("framesPeopleCounting_number", "count").Replace("backgroundImageURL", "imageUrl");
                            System.Text.RegularExpressions.Match match = regex.Match(item.DataInfo);
                            // 人数  
                            if (match.Success)
                            {
                                peopleCounting_number = match.Groups[1].Value;
                            }


                            // 图片  
                            System.Text.RegularExpressions.Match match2 = regex2.Match(item.DataInfo);
                            if (match2.Success)
                            {
                                imageUrl = match2.Groups[1].Value;
                                //LogHelper.Info($"imageUrl：{match2.Groups[1].Value}");
                            }

                            // 通道名称  channelName
                            System.Text.RegularExpressions.Match match3 = regex3.Match(item.DataInfo);
                            if (match3.Success)
                            {
                                channelName = match3.Groups[1].Value;
                            }

                            // 摄像头索引编码 
                            System.Text.RegularExpressions.Match cameraIndexCodeMatch = cameraIndexCodeRegex.Match(item.DataInfo);
                            if (cameraIndexCodeMatch.Success)
                            {
                                cameraIndexCode = cameraIndexCodeMatch.Groups[1].Value;
                            }
                            //// 发送时间 sendTime   
                            //System.Text.RegularExpressions.Match match4 = regex4.Match(item.DataInfo);

                            //if (match4.Success)
                            //{
                            //    strDtTime = match4.Groups[1].Value;
                            //    //sendTime = DateTime.Parse(match4.Groups[1].Value ?? DateTime.Now.ToString());                          
                            //}

                            LogHelper.Info($"【解析海康原始数据-第{index}条】人数：{peopleCounting_number}，图片:{imageUrl}，通道名称:{channelName}");
                            index++;
                            if (!string.IsNullOrEmpty(imageUrl) && !string.IsNullOrEmpty(cameraIndexCode))
                            {
                                //sendTime = DateTime.Parse(strDtTime);
                                //string strInsertSql = string.Format(@"insert into HaiKangEventNotify(PeopleNum, ImageUrl, SrcName, SendTime, statuz) values({0}, '{1}', '{2}', '{3}', 0);
                                //                                        ",
                                //   peopleCounting_number,
                                //   imageUrl,
                                //   channelName,
                                //   sendTime
                                //);
                                //strInsertSqls += strInsertSql;
                                //strUpdateSqls += $"update HaiKangData set statuz = 1 where Id = {item.Id};"; //成功处理的数据(statuz 1：成功、2：异常)

                                //sbInSql.AppendLine($"IF NOT EXISTS(SELECT Id FROM HaiKangEventNotify where SrcName = '{channelName}' and ImageUrl = '{imageUrl}')");
                                //sbInSql.AppendLine("BEGIN");
                                sbInSql.AppendLine($"insert into HaiKangEventNotify(PeopleNum, ImageUrl, SrcName,srcIndex, SendTime, statuz) values({peopleCounting_number}, '{imageUrl}', '{channelName}','{cameraIndexCode}', '{item.RegTime}', 0);");
                                //sbInSql.AppendLine("END");

                                sbUpSql.AppendLine($"update HaiKangData set statuz = 1 where Id = {item.Id}");
                                //LogHelper.Info($"【解析海康原始数据-成功】插入HaiKangEventNotify，更新HaiKangData表语句：{sbSql.ToString()}");
                            }
                            else
                            {
                                sbUpSql.AppendLine($"update HaiKangData set statuz = 2 where Id = {item.Id}");
                                //LogHelper.Info($"【解析海康原始数据-失败】更新HaiKangData表语句：{sbSql.ToString()}");
                                //strUpdateSqls += $"update HaiKangData set statuz = 2 where Id = {item.Id};"; //成功处理的数据(statuz 1：成功、2：异常)
                            }

                        }
                        catch (Exception exp)
                        {
                            sbUpSql.AppendLine($"update HaiKangData set statuz = 2 where Id = {item.Id}");
                            LogHelper.Info("【解析海康原始数据异常】：" + exp.Message + "，" + exp.StackTrace);
                            index++;
                        }
                    }

                    if (sbInSql.Length > 0)
                    {
                        hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2);
                        var insertResult = await hkdb.ExecuteBySql(sbInSql.ToString()); //批量执行insert语句
                        LogHelper.Info($"【解析海康原始数据-插入数据成功】插入HaiKangEventNotify表执行成功：{insertResult}条数据");
                    }
                    if (sbUpSql.Length > 0)
                    {
                        hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2);
                        var updateResult = await hkdb.ExecuteBySql(sbUpSql.ToString()); //批量执行update语句更新状态
                        LogHelper.Info($"【解析海康原始数据-更新数据成功】更新HaiKangData表状态执行成功：{updateResult}条数据");
                    }
                }
                else
                {
                    break;
                }
            }
           


            return obj;
        }

        private string ConvertDirectoryToHttp(string directory)
        {
            directory = directory.ParseToString();
            directory = directory.Replace(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
            return directory;
        }

        /// <summary>
        /// 执行实验教学数据库操作
        /// </summary>
        /// <param name="execSql">需要执行的sql语句</param>
        /// <param name="operate">操作提示文字</param>
        /// <returns></returns>
        private async Task<int> ExecDataBySql(string execSql, string operate)
        {
            Repository db = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString);
            try
            {
                if (!string.IsNullOrEmpty(execSql))
                {
                    var execResult = await db.ExecuteBySql(execSql);
                    LogHelper.Info($" ================【{operate}】=> 执行成功：{execResult}条数据 ================");
                    return execResult;
                }
                else
                {
                    return -1;
                }
            }
            catch (Exception ex)
            {
                LogHelper.Info($" ================【{operate}】=> 异常：{ex.Message} ================");
                return -1;
            }
        }

        /// <summary>
        /// 获取包含摄像头的功能室课程节次的数据
        /// </summary>
        /// <returns></returns>
        private async Task<IEnumerable<PatrolClassEntity>> GetFunRoomCourseSectionByCamera(string usedate)
        {
            Repository db = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString);
            string strSql = @$"SELECT Id,BaseIsDelete ,BaseCreateTime ,BaseModifyTime ,BaseCreatorId ,BaseModifierId ,BaseVersion
			                          ,UnitId ,FunRoomId ,DictionaryId1006A ,DictionaryId1006B ,SchoolTermName ,RoomAttribute ,SchoolStage
			                          ,DictionaryId1005 ,CourseSectionId ,BeginTime ,EndTime ,UseDate ,IsClass ,CameraId ,SrcName ,CameraIndexCode ,Statuz
		                       FROM  bn_PatrolClass WHERE UseDate = '{usedate}' AND (CameraIndexCode IS NOT NULL AND CameraIndexCode <> '')";
            var list = await db.FindList<PatrolClassEntity>(strSql);
            return list;
            //return new SqlServerDatabase(dbConnectionString).FindList<PatrolClassEntity>(strSql);
        }

        private async Task<bool> CheckIsInitPatrolClass(string usedate)
        {
            Repository db = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString);
            string strSql = @$"SELECT count(Id) FROM  bn_PatrolClass WHERE BaseIsDelete = 0 AND UseDate = '{usedate}'";
            object result = await db.FindObject(strSql);
            return result.ParseToLong() > 0;
            
        }

        #endregion

        #region 初始化【在线巡课】数据
        /// <summary>
        /// 初始化【在线巡课】数据
        /// </summary>
        public async Task<TData<string>> InitPatrolClass(string useDate)
        {
            TData<string> obj = new TData<string>();

            //如果已经初始化，则直接返回
            //var sql = $@"select count(Id) from bn_PatrolClass where BaseIsDelete = 0 and UseDate = '{useDate}'; ";
            var bInit = await CheckIsInitPatrolClass(useDate);
            if (bInit)
            {
                obj.Tag = 0;
                obj.Message = $"【{useDate}】在线巡课数据已经初始化过，无需重复操作;";
                return obj;
            }
            string strWhere = "";
            var patrollist = await GetPatrolClassList(strWhere); //查询需要巡课的课程节次数据
            StringBuilder insertSql = new StringBuilder();
            //StringBuilder deleteSql = new StringBuilder();
            if (patrollist.Count() > 0)
            {
                //deleteSql.Append($@"Delete from bn_PatrolClass where UseDate = '{useDate}'; ");
                //await ExecDataBySql(deleteSql.ToString(), $"删除【{useDate}】“在线巡课”数据操作"); //删除重复数据
                foreach (var item in patrollist)
                {
                    string currentDate = DateTime.Now.ToString("MM-dd"); //当前系统日期

                    //判断单位作息时间
                    var scheduleEntity = scheduleService.GetList(new UnitScheduleListParam() { UnitId = item.UnitId }).Result.LastOrDefault();
                    if (scheduleEntity == null) //单位未设置的情况查系统默认作息时间
                    {
                        scheduleEntity = scheduleService.GetList(new UnitScheduleListParam() { UnitId = 0 }).Result.LastOrDefault();
                    }

                    if (DateTime.Parse(currentDate) >= DateTime.Parse(scheduleEntity.BeginDate) && DateTime.Parse(currentDate) <= DateTime.Parse(scheduleEntity.EndDate)) //夏季作息时间
                    {
                        item.BeginTime = item.BeginTime;
                        item.EndTime = item.EndTime;
                    }
                    else if (DateTime.Parse(currentDate) >= DateTime.Parse(scheduleEntity.DjBeginTime) && DateTime.Parse(currentDate) <= DateTime.Parse(scheduleEntity.DjEndTime)) //冬季作息时间
                    {
                        item.BeginTime = item.DjBeginTime;
                        item.EndTime = item.DjEndTime;
                    }

                    if (!string.IsNullOrEmpty(item.BeginTime) && !string.IsNullOrEmpty(item.EndTime))
                    {
                        insertSql.Append(@$"INSERT INTO  bn_PatrolClass
                                                        ( Id ,BaseIsDelete ,BaseCreateTime ,BaseModifyTime ,BaseCreatorId ,BaseModifierId ,BaseVersion ,UnitId ,FunRoomId ,DictionaryId1006A ,DictionaryId1006B ,
                                                          SchoolTermName ,RoomAttribute ,SchoolStage ,DictionaryId1005 ,CourseSectionId ,BeginTime ,EndTime ,UseDate ,IsClass ,CameraId ,SrcName ,CameraIndexCode ,Statuz
                                                        ) 
                                          values({Dqy.Syjx.IdGenerator.IdGeneratorHelper.Instance.GetId()},0,'{DateTime.Now}','{DateTime.Now}',0,0,0,{item.UnitId},{item.FunRoomId},{item.DictionaryId1006A},{item.DictionaryId1006B},
                                          '{item.SchoolTermName}',{item.RoomAttribute},'{item.SchoolStage}',{item.DictionaryId1005},{item.CourseSectionId},'{item.BeginTime}','{item.EndTime}','{useDate}',0,'','{item.SrcName}','{item.CameraIndexCode}',1); ");

                    }
                }
                int resultobj = await ExecDataBySql(insertSql.ToString(), $"新增【{useDate}】“在线巡课”数据操作"); //插入bn_PatrolClass表数据操作
                if (resultobj >= 0)
                {
                    obj.Tag = 1;
                    obj.Message = "初始化巡课数据成功";
                }
                else
                {
                    obj.Tag = 0;
                    obj.Message = "初始化巡课数据失败";
                }
            }

            return obj;
        }
        #endregion

        #region 需要巡课的课程节次数据
        /// <summary>
        /// 查询需要巡课的课程节次数据
        /// </summary>
        /// <param name="strWhere"></param>
        /// <param name="bgntimeField"></param>
        /// <param name="endtimeField"></param>
        /// <returns></returns>
        public async Task<IEnumerable<PatrolClassEntity>> GetPatrolClassList(string strWhere)
        {
            /*
编号	单位属性	周	年级	年级编号
1001001	小学	一	三年级	1003003
1001002	初中	一	七年级	1003007
1001003	高中	一	高一	1003010
1001004	九年制	一	七年级	1003007
1001005	完中	一	七年级	1003007
1001006	十二年制	一	七年级	1003007

            */

            Repository db = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString);
            int weekid = DateTime.Now.DayOfWeek.ParseToInt(); //星期
            string strSql = @$"SELECT u.Name AS UnitName ,f.UnitId ,f.Id AS FunRoomId ,f.Name ,f.DictionaryId1006A ,f.DictionaryId1006B ,
				                     (RIGHT('0000'+CONVERT(NVARCHAR(10),ISNULL(st1.SchoolYearStart,'')),2)  + '~'+RIGHT('0000'+CONVERT(NVARCHAR(10),ISNULL(st1.SchoolYearEnd,'')),2) +(CASE WHEN st1.SchoolTerm =1 THEN '上学期' ELSE '下学期' END)) AS SchoolTermName ,
					                 f.RoomAttribute ,f.SchoolStagez AS SchoolStage ,f.DictionaryId1005 ,c.Id AS CourseSectionId 
									 ,c.BeginTime AS BeginTime ,c.EndTime AS EndTime ,c.DjBeginTime ,c.DjEndTime
                                     ,GETDATE() AS UseDate 
									 ,sc.SrcName ,sc.srcIndex AS CameraIndexCode
			                  FROM    bn_FunRoom f 
				                     CROSS JOIN up_CourseSection c 
				                     INNER JOIN  bn_SchoolTerm AS st1 ON st1.BaseIsDelete = 0 AND st1.TermStart <= GETDATE() AND GETDATE()  <= st1.TermEnd 
				                     INNER JOIN  up_Unit u ON u.Id = f.UnitId
                                     INNER JOIN  bn_SchoolCamera sc ON sc.FunRoomId = f.Id
			                  WHERE  f.UnitId = c.UnitId
				                     AND f.BaseIsDelete = 0 AND f.Statuz = 1 AND f.RoomAttribute != 1009003
				                     AND c.BaseIsDelete = 0 AND c.Statuz = 1
									 AND c.WeekId = 1015001
									  AND ((se.SchoolProp = 1001001 AND c.GradeId = 1003003 ) 
										OR (se.SchoolProp = 1001002 AND c.GradeId = 1003007 ) 
										OR (se.SchoolProp = 1001003 AND c.GradeId = 1003010 )
										OR (se.SchoolProp = 1001004 AND c.GradeId = 1003007 )
										OR (se.SchoolProp = 1001005 AND c.GradeId = 1003007 )
										OR (se.SchoolProp = 1001006 AND c.GradeId = 1003007 )) "; //修复没有七年级课表就无法巡课的bug 2025-08-12
            if (!string.IsNullOrEmpty(strWhere))
            {
                strSql += strWhere;
            }

            var list = await db.FindList<PatrolClassEntity>(strSql);
            if (list!=null)
            {
                list = list.DistinctBy(m=>m.FunRoomId).ToList();
            }
            return list;
        }
        #endregion

        #region 更新【在线巡课】数据
        /// <summary>
        /// 更新【在线巡课】数据
        /// </summary>
        public async Task<TData<string>> UpdatePatrolClass(string usedate)
        {
            TData<string> obj = new TData<string>();
            try
            {
                Repository hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2); //海康数据库

                List<ConfigSetEntity> configObj = await configSetService.GetList(new ConfigSetListParam { TypeCode = "5003_SXJCJYXRS", UnitId = 100000000000000001 });
                if (configObj.Count > 0)
                {
                    int.TryParse(configObj[0].ConfigValue, out isValidPicPeople);
                }
                var list = await GetFunRoomCourseSectionByCamera(usedate); //获取包含摄像头的功能室课程节次的数据
                LogHelper.Info($" ================【更新[{usedate}]“在线巡课”数据】=> 查询到 [{usedate}] 需要更新的巡课数据：{list.Count()}条 ================");

                if (list.Count() > 0)
                {
                    string dir = "Resource" + Path.DirectorySeparatorChar + "UploadFile" + Path.DirectorySeparatorChar + DateTime.Parse(usedate).ToString("yyyy-MM-dd").Replace('-', Path.DirectorySeparatorChar) + Path.DirectorySeparatorChar;
                    string filePath = null;
                    string newImgUrl = null;
                    string imgPath = null;
                    string upSql = null;

                    StringBuilder sbSyjxInsertSql = new StringBuilder();
                    StringBuilder sbSyjxUpdateSql = new StringBuilder();
                    StringBuilder sbHaiKangUpSql = new StringBuilder();

                    foreach (var item in list)
                    {
                        //获取课程节次抓拍最大人数及图片地址
                        MaxPeopleData maxData = await GetMaxPeopleAndPicData(item.SrcName, item.BeginTime, item.EndTime, usedate, item.CameraIndexCode);
                        if (maxData != null)
                        {
                            LogHelper.Info($" ================【查询到【{usedate}】-【摄像机：{item.SrcName}】在课程节次：【{item.BeginTime}】～【{item.EndTime}】采集的摄像头数据为：{JsonConvert.SerializeObject(maxData)} ================");

                            int isClass = 0;

                            LogHelper.Info($"======>摄像机采集的有效人数为：{isValidPicPeople}");

                            if (maxData.PeopleNum >= isValidPicPeople)
                            {
                                isClass = 1; //有效
                            }
                            upSql = @$"UPDATE  bn_PatrolClass SET IsClass = {isClass}, MaxPeopleNumber = {maxData.PeopleNum} WHERE Id = {item.Id};";
                            sbSyjxUpdateSql.AppendLine(upSql);
                            //await ExecDataBySql(upSql, $"更新[{usedate}]“在线巡课 - 更新最大人数”操作");

                            //下载图片
                            string dbImgUrl = maxData.ImageUrl.Substring(0, maxData.ImageUrl.IndexOf("/pic") + 1); //截取HaiKangEventNotify表中ImageUrl字段的http地址
                            newImgUrl = maxData.ImageUrl.Replace(dbImgUrl, PageCommon.ConfigRoot()["HaiKangReplaceHttpUrl"]); //替换后的新地址
                            string newFileName = DateTime.Now.ToString("yyyyMMddHHmmss") + Guid.NewGuid().ToString("n").Substring(0, 8) + ".jpg"; //图片名称
                            //string newFileName = GuidTo16String() + ".jpg";
                            string absoluteDir = Path.Combine(GlobalContext.HostingEnvironment.ContentRootPath, dir);  //图片保存物理路径
                            if (!Directory.Exists(absoluteDir))
                            {
                                Directory.CreateDirectory(absoluteDir);
                            }
                            string picVirtualPath = Path.AltDirectorySeparatorChar + ConvertDirectoryToHttp(dir) + newFileName;  //图片虚拟路径
                            filePath = absoluteDir + newFileName;
                            if (!File.Exists(filePath))
                            {
                                if (await PageCommon.DownloadPicture(newImgUrl, absoluteDir, newFileName, 6000) != "error")
                                {
                                    //hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2); //海康数据库
                                                                                                                                //更新
                                    upSql = $"update HaiKangEventNotify set statuz = 1,PicPath = '{picVirtualPath}' where Id = {maxData.Id};";
                                    sbHaiKangUpSql.AppendLine(upSql);

                                    //await hkdb.ExecuteBySql(upSql);
                                    LogHelper.Info($" ================【下载海康接口图片】=> 下载成功【ID:{item.Id}】图片至本地路径:【{absoluteDir + newFileName}】");
                                }
                                else
                                {
                                    //hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2); //海康数据库
                                                                                                                                //更新
                                    upSql = $"update HaiKangEventNotify set statuz = 4 where Id = {maxData.Id};";
                                    sbHaiKangUpSql.AppendLine(upSql);

                                    //await hkdb.ExecuteBySql(upSql);
                                    LogHelper.Info($" ================【下载海康接口图片失败】=> 图片完整路径为：{filePath}");
                                }

                            }
                            imgPath = picVirtualPath;
                            //判断图片是否真实存在
                            if (File.Exists(filePath))
                            {
                                LogHelper.Info($" ==>【图片存在】=> 下载地址为：{newImgUrl},存储地址为：{filePath},虚拟路径为：{imgPath}");

                                //保存摄像头抓拍图片
                                //string inSql = @$"insert into  bn_Attachment(Id,BaseIsDelete,BaseCreateTime,BaseModifyTime,BaseCreatorId,BaseModifierId,BaseVersion,ObjectId,FileCategory,Title,Path)  
                                //                         values({Dqy.Syjx.IdGenerator.IdGeneratorHelper.Instance.GetId()},0,'{DateTime.Now}','{DateTime.Now}',0,0,0,{item.Id},5003,'摄像头抓拍图片','{imgPath}')";

                                upSql = $"insert into  bn_Attachment(Id,BaseIsDelete,BaseCreateTime,BaseModifyTime,BaseCreatorId,BaseModifierId,BaseVersion,ObjectId,FileCategory,Title,Path)  values({Dqy.Syjx.IdGenerator.IdGeneratorHelper.Instance.GetId()},0,'{DateTime.Now}','{DateTime.Now}',0,0,0,{item.Id},5003,'摄像头抓拍图片','{imgPath}');";
                                sbSyjxInsertSql.AppendLine(upSql);
                                //await ExecDataBySql(inSql, "【保存摄像头抓拍图片】");
                            }
                            else
                            {
                                LogHelper.Info($" ==>【图片不存在】=> 下载地址为：{newImgUrl},存储地址为：{filePath},虚拟路径为：{imgPath}");
                            }
                        }
                        else
                        {
                            LogHelper.Info($" ================【更新[{usedate}]“在线巡课”数据】=> 未查询到【{usedate}】-【{item.SrcName}】摄像机在课程节次：【{item.BeginTime}】～【{item.EndTime}】采集的摄像头图片 ================");
                        }
                    }

                    if (sbSyjxInsertSql.Length > 0)
                    {
                        await ExecDataBySql(sbSyjxInsertSql.ToString(), "【保存摄像头抓拍图片】");
                    }
                    if (sbSyjxUpdateSql.Length > 0)
                    {
                        await ExecDataBySql(sbSyjxUpdateSql.ToString(), $"更新[{usedate}]“在线巡课 - 更新最大人数”操作");
                    }
                    if (sbHaiKangUpSql.Length > 0)
                    {
                        hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2);
                        await hkdb.ExecuteBySql(sbHaiKangUpSql.ToString());
                    }

                    obj.Tag = 1;
                }
                else
                {
                    LogHelper.Info($" ================【更新[{usedate}]“在线巡课”数据】=> 未查询到[{usedate}]需要更新的巡课数据 ================");
                }
            }
            catch (Exception ex)
            {
                LogHelper.Info($" ================程序出现异常，异常信息为：{ex.Message} ================");
            }
            
            return obj;
        }
        #endregion


        #region zyf后修改 2024-12-11

        /// <summary> 
        /// 根据GUID获取16位的唯一字符串 
        /// </summary> 
        /// <param name=\"guid\"></param> 
        /// <returns></returns> 
        private string GuidTo16String()
        {
            long i = 1;
            foreach (byte b in Guid.NewGuid().ToByteArray())
                i *= ((int)b + 1);

            return string.Format("{0:x}", i - DateTime.Now.Ticks);
        }

        /// <summary>
        /// 获取课程节次抓拍最大人数及图片地址
        /// </summary>
        /// <param name="srcName"></param>
        /// <param name="bgnTime"></param>
        /// <param name="endTime"></param>
        /// <param name="usedate"></param>
        /// <param name="srcIndex"></param>
        /// <returns></returns>
        private async Task<MaxPeopleData> GetMaxPeopleAndPicData(string srcName, string bgnTime, string endTime, string usedate, string srcIndex)
        {
            Repository hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2); //海康数据库
            Repository db = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString); //实验教学数据库
            MaxPeopleData obj = null;
            List<ConfigSetEntity> configObj = await configSetService.GetList(new ConfigSetListParam { TypeCode = "5003_SXJPP", UnitId = 100000000000000001 }); //获取平台配置的摄像机品牌
            if (configObj.Count > 0)
            {
                if (configObj.LastOrDefault().ConfigValue.Equals("1")) //海康
                {
                    string strSql = "";
                    string upSql = "";
                    StringBuilder sbUpSql = new StringBuilder();
                    if (GlobalContext.SystemConfig.DBProvider == "Dm")
                    {
                        //达梦数据库查询语句
                        strSql = $@"select Id ,PeopleNum ,ImageUrl ,SrcName ,SrcIndex ,SendTime ,statuz ,PicPath from (
                                select Id ,PeopleNum ,ImageUrl ,SrcName ,SrcIndex ,SendTime ,statuz ,PicPath,SUM(1) OVER(partition by TO_CHAR(SendTime, 'yyyy-mm-dd'),SrcName order by PeopleNum desc)as sortId  from HaiKangEventNotify
                                where (TO_CHAR(SendTime, 'hh24:mi:ss') BETWEEN '{bgnTime}' AND '{endTime}') AND srcIndex = '{srcIndex}' AND statuz = 0
                               )t where sortId=1";
                    }
                    else
                    {
                        string dtBegin = DateTime.Parse(usedate + " " + bgnTime).ToString("yyyy-MM-dd HH:mm:ss");
                        string dtEnd = DateTime.Parse(usedate + " " + endTime).ToString("yyyy-MM-dd HH:mm:ss");
                        strSql = $@"SELECT TOP 1 Id ,PeopleNum ,ImageUrl ,SrcName ,SrcIndex ,SendTime ,statuz ,PicPath FROM HaiKangEventNotify WHERE srcIndex='{srcIndex}'
                                    AND SendTime BETWEEN '{dtBegin}' AND '{dtEnd}'
                                    ORDER BY PeopleNum DESC";

                        upSql= $"UPDATE HaiKangEventNotify  SET statuz = 3 WHERE statuz = 0 AND srcIndex='{srcIndex}' AND SendTime BETWEEN '{dtBegin}' AND '{dtEnd}';";
                    }
                    //LogHelper.Info($" 获取最大人数地址信息 {strSql.ToString()}");
                    var objNotify = hkdb.FindList<HaiKangEventNotify>(strSql.ToString()).Result.FirstOrDefault();
                    if (objNotify != null)
                    {
                        obj = new MaxPeopleData() { Id = objNotify.Id, PeopleNum = objNotify.PeopleNum, SrcName = objNotify.SrcName, SrcIndex = objNotify.SrcIndex, ChannelCode = "", ChannelName = "", AlarmDate = objNotify.SendTime, ImageUrl = objNotify.ImageUrl, PicPath = objNotify.PicPath };

                        sbUpSql.AppendLine($"UPDATE HaiKangEventNotify  SET statuz = 1 WHERE Id = {objNotify.Id};");
                        sbUpSql.AppendLine(upSql);

                        hkdb = ********************.BaseRepository(GlobalContext.SystemConfig.DBConnectionString2);
                        await hkdb.ExecuteBySql(sbUpSql.ToString());
                    }
                }
                else if (configObj.LastOrDefault().ConfigValue.Equals("2")) //大华
                {
                    string strSql = "";
                    if (GlobalContext.SystemConfig.DBProvider == "Dm")
                    {
                        //达梦数据库查询语句
                        strSql = $@"SELECT collectionTime, regionCode, regionName, remainder, channelCode, channelName, channelSn, deviceCode, deviceName, sortId FROM ( " +
                        $"  SELECT collectionTime,regionCode, regionName, remainder, channelCode, channelName, channelSn, deviceCode, deviceName, " +
                        $"  SUM(1) OVER(partition by TO_CHAR(collectionTime, 'yyyy-mm-dd'),deviceName order by remainder desc )as sortId " +
                                        $"  FROM ( " +
                                        $"      SELECT p.regionCode, p.regionName, p.collectionTime, p.remainder, c.channelCode, c.channelName, c.channelSn, d.deviceCode, d.deviceName" +
                                        $"      FROM  DahuaPersonNum p " +
                                        $"      INNER JOIN  DahuaChannel c ON p.regionCode = c.channelSn" +
                                        $"      INNER JOIN  DahuaDevice d ON d.deviceCode = c.deviceCode ) t1 WHERE t1.deviceName = '{srcName}'" +
                                        $"      AND ((TO_CHAR(t1.collectionTime, 'hh24:mi:ss') BETWEEN '{bgnTime}' AND '{endTime}') " +
                                        $"      AND TO_CHAR(collectionTime, 'yyyy-mm-dd') = '{usedate}' " +
                                        $")t where sortId = 1";
                    }
                    else
                    {
                        //查找课程节次最大人数
                        strSql = $@"SELECT collectionTime, regionCode, regionName, remainder, channelCode, channelName, channelSn, deviceCode, deviceName, sortId FROM ( " +
                                        $"  SELECT collectionTime,regionCode, regionName, remainder, channelCode, channelName, channelSn, deviceCode, deviceName, " +
                                        $"  ROW_NUMBER() OVER(partition by CONVERT(nvarchar(10),collectionTime,121),deviceName order by remainder desc )as sortId " +
                                        $"  FROM ( " +
                                        $"      SELECT p.regionCode, p.regionName, p.collectionTime, p.remainder, c.channelCode, c.channelName, c.channelSn, d.deviceCode, d.deviceName" +
                                        $"      FROM  DahuaPersonNum p " +
                                        $"      INNER JOIN  DahuaChannel c ON p.regionCode = c.channelSn" +
                                        $"      INNER JOIN  DahuaDevice d ON d.deviceCode = c.deviceCode ) t1 WHERE t1.deviceName = '{srcName}'" +
                                        $"      AND (CONVERT(varchar(100),t1.collectionTime,24) BETWEEN '{bgnTime}' AND '{endTime}') AND CONVERT(varchar(10),t1.collectionTime,120) = '{usedate}' " +
                                        $")t where sortId = 1";
                    }

                    var objNotify = db.FindList<MaxPeopleResult>(strSql.ToString()).Result.FirstOrDefault();
                    if (objNotify != null)
                    {
                        LogHelper.Info($" ================【查询课程节次最大人数信息】=> 查找到{srcName}摄像机在 {bgnTime}~{endTime} 拍摄到的最大人数 {objNotify.remainder}、时间节点 {objNotify.collectionTime} ================");

                        #region 查找报警数据的图片
                        //查找最大人数时间节点前后3分钟的报警数据
                        DateTime newCourseBgnTime = objNotify.collectionTime.AddMinutes(-3);
                        DateTime newCourseEndTime = objNotify.collectionTime.AddMinutes(3);
                        string alarmSql = $"SELECT AlarmDate,ImageUrl,NodeCode,AlarmPosition FROM  DahuaAlarmData WHERE NodeCode = '{objNotify.channelCode}' AND AlarmDate BETWEEN '{newCourseBgnTime}' AND '{newCourseEndTime}' ORDER BY AlarmDate ASC ";
                        var alarmdatalist = await db.FindList<AlarmData>(alarmSql.ToString());
                        if (alarmdatalist.Count() > 0 && !string.IsNullOrEmpty(alarmdatalist.FirstOrDefault().ImageUrl))
                        {
                            LogHelper.Info($" ================【查询课程节次最大人数信息】=> 查找到{srcName}摄像机在 {newCourseBgnTime}~{newCourseEndTime} 拍摄到的照片1 {alarmdatalist.FirstOrDefault().ImageUrl}、时间 {alarmdatalist.FirstOrDefault().AlarmDate} ================");
                            obj = new MaxPeopleData() { PeopleNum = objNotify.remainder, SrcName = objNotify.deviceName, ChannelCode = objNotify.channelCode, ChannelName = objNotify.channelName, AlarmDate = objNotify.collectionTime, ImageUrl = alarmdatalist.FirstOrDefault().ImageUrl };
                            return obj;
                        }
                        else //无数据则查询课程节次之间的数据
                        {
                            //计算剔除后的课程节次开始、结束时间
                            int minutes = Convert.ToInt32(GlobalContext.SystemConfig.HaiKangVideoExcludeTime);
                            newCourseBgnTime = DateTime.Parse(usedate + " " + bgnTime).AddMinutes(minutes);
                            newCourseEndTime = DateTime.Parse(usedate + " " + endTime).AddMinutes(-minutes);
                            alarmSql = $"SELECT AlarmDate,ImageUrl,NodeCode,AlarmPosition FROM DahuaAlarmData WHERE NodeCode = '{objNotify.channelCode}' AND AlarmDate BETWEEN '{newCourseBgnTime}' AND '{newCourseEndTime}' ORDER BY AlarmDate DESC ";
                            var coursetimeAlarmlist = await db.FindList<AlarmData>(alarmSql.ToString());
                            if (coursetimeAlarmlist.Count() > 0 && !string.IsNullOrEmpty(coursetimeAlarmlist.FirstOrDefault().ImageUrl))
                            {
                                LogHelper.Info($" ================【查询课程节次最大人数信息】=> 查找到{srcName}摄像机在 {newCourseBgnTime}~{newCourseEndTime} 拍摄到的照片2 {coursetimeAlarmlist.FirstOrDefault().ImageUrl}、时间 {coursetimeAlarmlist.FirstOrDefault().AlarmDate} ================");
                                obj = new MaxPeopleData() { PeopleNum = objNotify.remainder, SrcName = objNotify.deviceName, ChannelCode = objNotify.channelCode, ChannelName = objNotify.channelName, AlarmDate = objNotify.collectionTime, ImageUrl = coursetimeAlarmlist.FirstOrDefault().ImageUrl };
                                return obj;
                            }
                            else
                            {
                                LogHelper.Info($" ================【查询课程节次最大人数信息】=> 未查找到{srcName}摄像机在 {newCourseBgnTime}~{newCourseEndTime} 拍摄到的照片2 ================");
                                return obj;
                            }
                        }
                        #endregion
                    }
                }
            }
            return obj;
        }

        #endregion
    }
}
