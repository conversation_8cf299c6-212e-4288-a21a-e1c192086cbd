{"version": 3, "file": "metisMenu.js", "sources": ["../src/util.js", "../src/index.js"], "sourcesContent": ["import $ from 'jquery';\n\nconst Util = (($) => { // eslint-disable-line no-shadow\n  const TRANSITION_END = 'transitionend';\n\n  const Util = { // eslint-disable-line no-shadow\n    TRANSITION_END: 'mmTransitionEnd',\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(TRANSITION_END);\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(TRANSITION_END);\n    },\n  };\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: TRANSITION_END,\n      delegateType: TRANSITION_END,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event\n            .handleObj\n            .handler\n            .apply(this, arguments); // eslint-disable-line prefer-rest-params\n        }\n        return undefined;\n      },\n    };\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false;\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true;\n    });\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this);\n      }\n    }, duration);\n\n    return this;\n  }\n\n  function setTransitionEndSupport() {\n    $.fn.mmEmulateTransitionEnd = transitionEndEmulator; // eslint-disable-line no-param-reassign\n    // eslint-disable-next-line no-param-reassign\n    $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent();\n  }\n\n  setTransitionEndSupport();\n\n  return Util;\n})($);\n\nexport default Util;\n", "import $ from 'jquery';\nimport Util from './util';\n\nconst NAME = 'metisMenu';\nconst DATA_KEY = 'metisMenu';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst DATA_API_KEY = '.data-api';\nconst JQUERY_NO_CONFLICT = $.fn[NAME];\nconst TRANSITION_DURATION = 350;\n\nconst Default = {\n  toggle: true,\n  preventDefault: true,\n  triggerElement: 'a',\n  parentTrigger: 'li',\n  subMenu: 'ul',\n};\n\nconst Event = {\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  CLICK_DATA_API: `click${EVENT_KEY}${DATA_API_KEY}`,\n};\n\nconst ClassName = {\n  METIS: 'metismenu',\n  ACTIVE: 'mm-active',\n  SHOW: 'mm-show',\n  COLLAPSE: 'mm-collapse',\n  COLLAPSING: 'mm-collapsing',\n  COLLAPSED: 'mm-collapsed',\n};\n\nclass MetisMenu {\n  // eslint-disable-line no-shadow\n  constructor(element, config) {\n    this.element = element;\n    this.config = {\n      ...Default,\n      ...config,\n    };\n    this.transitioning = null;\n\n    this.init();\n  }\n\n  init() {\n    const self = this;\n    const conf = this.config;\n    const el = $(this.element);\n\n    el.addClass(ClassName.METIS); // add metismenu class to element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the trigger element\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .addClass(ClassName.ACTIVE);\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .parents(conf.parentTrigger)\n      .children(conf.triggerElement)\n      .attr('aria-expanded', 'true'); // add attribute aria-expanded=true the triggers of all parents\n\n    el.find(`${conf.parentTrigger}.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`);\n\n    el\n      .find(conf.parentTrigger)\n      .not(`.${ClassName.ACTIVE}`)\n      .has(conf.subMenu)\n      .children(conf.subMenu)\n      .addClass(ClassName.COLLAPSE);\n\n    el\n      .find(conf.parentTrigger)\n      // .has(conf.subMenu)\n      .children(conf.triggerElement)\n      .on(Event.CLICK_DATA_API, function (e) { // eslint-disable-line func-names\n        const eTar = $(this);\n\n        if (eTar.attr('aria-disabled') === 'true') {\n          return;\n        }\n\n        if (conf.preventDefault && eTar.attr('href') === '#') {\n          e.preventDefault();\n        }\n\n        const paRent = eTar.parent(conf.parentTrigger);\n        const sibLi = paRent.siblings(conf.parentTrigger);\n        const sibTrigger = sibLi.children(conf.triggerElement);\n\n        if (paRent.hasClass(ClassName.ACTIVE)) {\n          eTar.attr('aria-expanded', 'false');\n          self.removeActive(paRent);\n        } else {\n          eTar.attr('aria-expanded', 'true');\n          self.setActive(paRent);\n          if (conf.toggle) {\n            self.removeActive(sibLi);\n            sibTrigger.attr('aria-expanded', 'false');\n          }\n        }\n\n        if (conf.onTransitionStart) {\n          conf.onTransitionStart(e);\n        }\n      });\n  }\n\n  setActive(li) {\n    $(li).addClass(ClassName.ACTIVE);\n    const ul = $(li).children(this.config.subMenu);\n    if (ul.length > 0 && !ul.hasClass(ClassName.SHOW)) {\n      this.show(ul);\n    }\n  }\n\n  removeActive(li) {\n    $(li).removeClass(ClassName.ACTIVE);\n    const ul = $(li).children(`${this.config.subMenu}.${ClassName.SHOW}`);\n    if (ul.length > 0) {\n      this.hide(ul);\n    }\n  }\n\n  show(element) {\n    if (this.transitioning || $(element).hasClass(ClassName.COLLAPSING)) {\n      return;\n    }\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.SHOW);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).addClass(ClassName.ACTIVE);\n\n    if (this.config.toggle) {\n      const toggleElem = elem.parent(this.config.parentTrigger).siblings().children(`${this.config.subMenu}.${ClassName.SHOW}`);\n      this.hide(toggleElem);\n    }\n\n    elem\n      .removeClass(ClassName.COLLAPSE)\n      .addClass(ClassName.COLLAPSING)\n      .height(0);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(`${ClassName.COLLAPSE} ${ClassName.SHOW}`)\n        .height('');\n\n      this.setTransitioning(false);\n\n      elem.trigger(Event.SHOWN);\n    };\n\n    elem\n      .height(element[0].scrollHeight)\n      .one(Util.TRANSITION_END, complete)\n      .mmEmulateTransitionEnd(TRANSITION_DURATION);\n  }\n\n  hide(element) {\n    if (\n      this.transitioning || !$(element).hasClass(ClassName.SHOW)\n    ) {\n      return;\n    }\n\n    const elem = $(element);\n\n    const startEvent = $.Event(Event.HIDE);\n    elem.trigger(startEvent);\n\n    if (startEvent.isDefaultPrevented()) {\n      return;\n    }\n\n    elem.parent(this.config.parentTrigger).removeClass(ClassName.ACTIVE);\n    // eslint-disable-next-line no-unused-expressions\n    elem.height(elem.height())[0].offsetHeight;\n\n    elem\n      .addClass(ClassName.COLLAPSING)\n      .removeClass(ClassName.COLLAPSE)\n      .removeClass(ClassName.SHOW);\n\n    this.setTransitioning(true);\n\n    const complete = () => {\n      // check if disposed\n      if (!this.config || !this.element) {\n        return;\n      }\n      if (this.transitioning && this.config.onTransitionEnd) {\n        this.config.onTransitionEnd();\n      }\n\n      this.setTransitioning(false);\n      elem.trigger(Event.HIDDEN);\n\n      elem\n        .removeClass(ClassName.COLLAPSING)\n        .addClass(ClassName.COLLAPSE);\n    };\n\n    if (elem.height() === 0 || elem.css('display') === 'none') {\n      complete();\n    } else {\n      elem\n        .height(0)\n        .one(Util.TRANSITION_END, complete)\n        .mmEmulateTransitionEnd(TRANSITION_DURATION);\n    }\n  }\n\n  setTransitioning(isTransitioning) {\n    this.transitioning = isTransitioning;\n  }\n\n  dispose() {\n    $.removeData(this.element, DATA_KEY);\n\n    $(this.element)\n      .find(this.config.parentTrigger)\n      // .has(this.config.subMenu)\n      .children(this.config.triggerElement)\n      .off(Event.CLICK_DATA_API);\n\n    this.transitioning = null;\n    this.config = null;\n    this.element = null;\n  }\n\n  static jQueryInterface(config) {\n    // eslint-disable-next-line func-names\n    return this.each(function () {\n      const $this = $(this);\n      let data = $this.data(DATA_KEY);\n      const conf = {\n        ...Default,\n        ...$this.data(),\n        ...(typeof config === 'object' && config ? config : {}),\n      };\n\n      if (!data) {\n        data = new MetisMenu(this, conf);\n        $this.data(DATA_KEY, data);\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined) {\n          throw new Error(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = MetisMenu.jQueryInterface; // eslint-disable-line no-param-reassign\n$.fn[NAME].Constructor = MetisMenu; // eslint-disable-line no-param-reassign\n$.fn[NAME].noConflict = () => {\n  // eslint-disable-line no-param-reassign\n  $.fn[NAME] = JQUERY_NO_CONFLICT; // eslint-disable-line no-param-reassign\n  return MetisMenu.jQueryInterface;\n};\n\nexport default MetisMenu;\n"], "names": ["$"], "mappings": ";;;;;;;;;;;;;;;;;EAEA,MAAM,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK;EACrB,EAAE,MAAM,cAAc,GAAG,eAAe,CAAC;AACzC;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,cAAc,EAAE,iBAAiB;AACrC;EACA,IAAI,oBAAoB,CAAC,OAAO,EAAE;EAClC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;EACzC,KAAK;AACL;EACA,IAAI,qBAAqB,GAAG;EAC5B,MAAM,OAAO,OAAO,CAAC,cAAc,CAAC,CAAC;EACrC,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,SAAS,4BAA4B,GAAG;EAC1C,IAAI,OAAO;EACX,MAAM,QAAQ,EAAE,cAAc;EAC9B,MAAM,YAAY,EAAE,cAAc;EAClC,MAAM,MAAM,CAAC,KAAK,EAAE;EACpB,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACtC,UAAU,OAAO,KAAK;EACtB,aAAa,SAAS;EACtB,aAAa,OAAO;EACpB,aAAa,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EACpC,SAAS;EACT,QAAQ,OAAO,SAAS,CAAC;EACzB,OAAO;EACP,KAAK,CAAC;EACN,GAAG;AACH;EACA,EAAE,SAAS,qBAAqB,CAAC,QAAQ,EAAE;EAC3C,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;AACvB;EACA,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM;EAC3C,MAAM,MAAM,GAAG,IAAI,CAAC;EACpB,KAAK,CAAC,CAAC;AACP;EACA,IAAI,UAAU,CAAC,MAAM;EACrB,MAAM,IAAI,CAAC,MAAM,EAAE;EACnB,QAAQ,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;EACxC,OAAO;EACP,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjB;EACA,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,SAAS,uBAAuB,GAAG;EACrC,IAAI,CAAC,CAAC,EAAE,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;EACxD;EACA,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,4BAA4B,EAAE,CAAC;EAC1E,GAAG;AACH;EACA,EAAE,uBAAuB,EAAE,CAAC;AAC5B;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC,EAAEA,qBAAC,CAAC;;ECvDL,MAAM,IAAI,GAAG,WAAW,CAAC;EACzB,MAAM,QAAQ,GAAG,WAAW,CAAC;EAC7B,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;EACjC,MAAM,YAAY,GAAG,WAAW,CAAC;EACjC,MAAM,kBAAkB,GAAGA,qBAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;EACtC,MAAM,mBAAmB,GAAG,GAAG,CAAC;AAChC;EACA,MAAM,OAAO,GAAG;EAChB,EAAE,MAAM,EAAE,IAAI;EACd,EAAE,cAAc,EAAE,IAAI;EACtB,EAAE,cAAc,EAAE,GAAG;EACrB,EAAE,aAAa,EAAE,IAAI;EACrB,EAAE,OAAO,EAAE,IAAI;EACf,CAAC,CAAC;AACF;EACA,MAAM,KAAK,GAAG;EACd,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1B,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;EAC5B,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;EAC1B,EAAE,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;EAC9B,EAAE,cAAc,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,YAAY,CAAC,CAAC;EACpD,CAAC,CAAC;AACF;EACA,MAAM,SAAS,GAAG;EAClB,EAAE,KAAK,EAAE,WAAW;EACpB,EAAE,MAAM,EAAE,WAAW;EACrB,EAAE,IAAI,EAAE,SAAS;EACjB,EAAE,QAAQ,EAAE,aAAa;EACzB,EAAE,UAAU,EAAE,eAAe;EAC7B,EAAE,SAAS,EAAE,cAAc;EAC3B,CAAC,CAAC;AACF;EACA,MAAM,SAAS,CAAC;EAChB;EACA,EAAE,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE;EAC/B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;EAC3B,IAAI,IAAI,CAAC,MAAM,GAAG;EAClB,MAAM,GAAG,OAAO;EAChB,MAAM,GAAG,MAAM;EACf,KAAK,CAAC;EACN,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B;EACA,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,GAAG;EACT,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC;EACtB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;EAC7B,IAAI,MAAM,EAAE,GAAGA,qBAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/B;EACA,IAAI,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACjC;EACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;EACpC,OAAO,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AACrC;EACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;EAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAClC;EACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;EAClC,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;EACpC,OAAO,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AACrC;EACA,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;EACxD,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;EACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;EAC7B,OAAO,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3D;EACA,IAAI,EAAE;EACN,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;EAC/B,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;EAClC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;EACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;EAC7B,OAAO,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACpC;EACA,IAAI,EAAE;EACN,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;EAC/B;EACA,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;EACpC,OAAO,EAAE,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE;EAC7C,QAAQ,MAAM,IAAI,GAAGA,qBAAC,CAAC,IAAI,CAAC,CAAC;AAC7B;EACA,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;EACnD,UAAU,OAAO;EACjB,SAAS;AACT;EACA,QAAQ,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE;EAC9D,UAAU,CAAC,CAAC,cAAc,EAAE,CAAC;EAC7B,SAAS;AACT;EACA,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACvD,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EAC1D,QAAQ,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/D;EACA,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;EAC/C,UAAU,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;EAC9C,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;EACpC,SAAS,MAAM;EACf,UAAU,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;EAC7C,UAAU,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;EACjC,UAAU,IAAI,IAAI,CAAC,MAAM,EAAE;EAC3B,YAAY,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;EACrC,YAAY,UAAU,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;EACtD,WAAW;EACX,SAAS;AACT;EACA,QAAQ,IAAI,IAAI,CAAC,iBAAiB,EAAE;EACpC,UAAU,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACpC,SAAS;EACT,OAAO,CAAC,CAAC;EACT,GAAG;AACH;EACA,EAAE,SAAS,CAAC,EAAE,EAAE;EAChB,IAAIA,qBAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;EACrC,IAAI,MAAM,EAAE,GAAGA,qBAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;EACnD,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;EACvD,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACpB,KAAK;EACL,GAAG;AACH;EACA,EAAE,YAAY,CAAC,EAAE,EAAE;EACnB,IAAIA,qBAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;EACxC,IAAI,MAAM,EAAE,GAAGA,qBAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1E,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;EACvB,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACpB,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,CAAC,OAAO,EAAE;EAChB,IAAI,IAAI,IAAI,CAAC,aAAa,IAAIA,qBAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;EACzE,MAAM,OAAO;EACb,KAAK;EACL,IAAI,MAAM,IAAI,GAAGA,qBAAC,CAAC,OAAO,CAAC,CAAC;AAC5B;EACA,IAAI,MAAM,UAAU,GAAGA,qBAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7B;EACA,IAAI,IAAI,UAAU,CAAC,kBAAkB,EAAE,EAAE;EACzC,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACtE;EACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;EAC5B,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAChI,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EAC5B,KAAK;AACL;EACA,IAAI,IAAI;EACR,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC;EACtC,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;EACrC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB;EACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC;EACA,IAAI,MAAM,QAAQ,GAAG,MAAM;EAC3B;EACA,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;EACzC,QAAQ,OAAO;EACf,OAAO;EACP,MAAM,IAAI;EACV,SAAS,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC;EAC1C,SAAS,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,SAAS,MAAM,CAAC,EAAE,CAAC,CAAC;AACpB;EACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACnC;EACA,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;EAChC,KAAK,CAAC;AACN;EACA,IAAI,IAAI;EACR,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;EACtC,OAAO,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC;EACzC,OAAO,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;EACnD,GAAG;AACH;EACA,EAAE,IAAI,CAAC,OAAO,EAAE;EAChB,IAAI;EACJ,MAAM,IAAI,CAAC,aAAa,IAAI,CAACA,qBAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC;EAChE,MAAM;EACN,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,MAAM,IAAI,GAAGA,qBAAC,CAAC,OAAO,CAAC,CAAC;AAC5B;EACA,IAAI,MAAM,UAAU,GAAGA,qBAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC7B;EACA,IAAI,IAAI,UAAU,CAAC,kBAAkB,EAAE,EAAE;EACzC,MAAM,OAAO;EACb,KAAK;AACL;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;EACzE;EACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;AAC/C;EACA,IAAI,IAAI;EACR,OAAO,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC;EACrC,OAAO,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC;EACtC,OAAO,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACnC;EACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChC;EACA,IAAI,MAAM,QAAQ,GAAG,MAAM;EAC3B;EACA,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;EACzC,QAAQ,OAAO;EACf,OAAO;EACP,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE;EAC7D,QAAQ,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;EACtC,OAAO;AACP;EACA,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;EACnC,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACjC;EACA,MAAM,IAAI;EACV,SAAS,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC;EAC1C,SAAS,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;EACtC,KAAK,CAAC;AACN;EACA,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,EAAE;EAC/D,MAAM,QAAQ,EAAE,CAAC;EACjB,KAAK,MAAM;EACX,MAAM,IAAI;EACV,SAAS,MAAM,CAAC,CAAC,CAAC;EAClB,SAAS,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC;EAC3C,SAAS,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;EACrD,KAAK;EACL,GAAG;AACH;EACA,EAAE,gBAAgB,CAAC,eAAe,EAAE;EACpC,IAAI,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;EACzC,GAAG;AACH;EACA,EAAE,OAAO,GAAG;EACZ,IAAIA,qBAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACzC;EACA,IAAIA,qBAAC,CAAC,IAAI,CAAC,OAAO,CAAC;EACnB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;EACtC;EACA,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;EAC3C,OAAO,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACjC;EACA,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;EAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;EACvB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;EACxB,GAAG;AACH;EACA,EAAE,OAAO,eAAe,CAAC,MAAM,EAAE;EACjC;EACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;EACjC,MAAM,MAAM,KAAK,GAAGA,qBAAC,CAAC,IAAI,CAAC,CAAC;EAC5B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACtC,MAAM,MAAM,IAAI,GAAG;EACnB,QAAQ,GAAG,OAAO;EAClB,QAAQ,GAAG,KAAK,CAAC,IAAI,EAAE;EACvB,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,CAAC;EAC/D,OAAO,CAAC;AACR;EACA,MAAM,IAAI,CAAC,IAAI,EAAE;EACjB,QAAQ,IAAI,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EACzC,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;EACnC,OAAO;AACP;EACA,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;EACtC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,SAAS,EAAE;EACxC,UAAU,MAAM,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACzD,SAAS;EACT,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;EACvB,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;EACH,CAAC;EACD;EACA;EACA;EACA;EACA;AACA;AACAA,uBAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,eAAe,CAAC;AACvCA,uBAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,WAAW,GAAG,SAAS,CAAC;AACnCA,uBAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,MAAM;EAC9B;EACA,EAAEA,qBAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC;EAClC,EAAE,OAAO,SAAS,CAAC,eAAe,CAAC;EACnC,CAAC;;;;;;;;"}