﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="addTabDetailForm()"><i class="fa fa-street-view"></i>&nbsp;按学校查看</a>
                    </li>
                    <li>
                        <span id="funRoomEvaluateProjectId" col="funRoomEvaluateProjectId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:200px;"></span>
                    </li>
                    <li>
                        <span id="schoolStageId" col="SchoolStageId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="level" col="Level" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="oneClassId" col="OneClassId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="指标名称" style="width:150px" />
                    </li>
                    @*<li>
                        <div id="isOnlyShowNoStandard" col="IsOnlyShowNoStandard" style="display:inline-block;width:120px;"></div>
                    </li>*@
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            loadSchool();
            $('#liSchoolId').show();
        }

        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#liCountyId').show();
            loadCounty();
        }

        //$('#isOnlyShowNoStandard').ysCheckBox({
        //    data: [{ Key: 1, Value: '只显示不达标' }]
        //});

        loadFunRoomEvaluateProjectIdId();
        loadSchoolStage();
        loadSubject();
        loadOneClass();
        loadLevel();
        
        
    });

     function loadCounty() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#countyId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '区县名称'
                    });
                }
            }
        });
    }

    function initGrid() {
        if (!$('#funRoomEvaluateProjectId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        var queryUrl = '@Url.Content("~/EvaluateManage/FunRoomAttendStatic/GetCityFunRoomAttendStaticListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: "CountyId ASC,StageId ASC, CourseId ASC",
            columns: [
                { field: 'CountyName', title: '区县名称', sortable: true, halign: 'center', align: 'center', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt()), width: 120  },
                { field: 'StageName', title: '学段', halign: 'center', align: 'center', sortable: true, width: 100  },
                { field: 'OneClassName', title: '一级分类', halign: 'center', align: 'center', sortable: true, width: 140  },
                { field: 'TargetName', title: '指标名称', halign: 'center', align: 'left', sortable: true, width: 140  },
                { field: 'CourseName', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: 120  },
                {
                    field: 'RoomStandardRate', title: '数量达标率', halign: 'center', align: 'center', sortable: true, width: 100 ,
                    formatter: function (value, row, index) {
                        return row.RoomStandardRate + '%';
                    }
                },
                {
                    field: 'AreaStandardRate', title: '面积达标率', halign: 'center', align: 'center', sortable: true, width: 100  ,
                    formatter: function (value, row, index) {
                        return row.AreaStandardRate + '%';
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function loadFunRoomEvaluateProjectIdId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/GetProjectComboJson")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#funRoomEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        $('#funRoomEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    initGrid();
                }
            }
        });
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }

    function loadSubject() {
        $("#courseId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadOneClass() {
        $("#oneClassId").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.FunRoomClass.ParseToInt()&OptType=7&Pid=0',
            defaultName: '一级分类',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function loadLevel() {
        $('#level').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=@DicTypeCodeEnum.StandardLevel.ParseToInt()",
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '达标级别'
        });
    }

    function loadSchool() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000",
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#schoolId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'Name',
                        defaultName: '单位名称'
                    });
                }
            }
        });
    }

    function searchGrid() {
        if ($('#funRoomEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }

    function resetGrid() {
        //清空条件
        $('#schoolStageId').ysComboBox('setValue', -1);
        $('#level').ysComboBox('setValue', -1);
        $('#oneClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', -1);
        }
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function addTabDetailForm() {
        var url = '/EvaluateManage/FunRoomAttendStatic/CityFunRoomAttendStaticBySchool';
        createMenuItem(url, "按学校查看");
    }

</script>
