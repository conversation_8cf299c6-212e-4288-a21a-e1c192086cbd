﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";

}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="BackupYear" col="BackupYear" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolId" col="SchoolId" style="display: inline-block; width: 180px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                   @*  <li>
                        <a class="btn btn-secondary btn-sm" onclick="backupsGrid()"><i class="fa fa-refresh"></i>&nbsp;备份</a>
                    </li> *@
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var columnArr = [];
    var columnFunRooms = [];
    var Query_DicIds = [1006002, 1006004, 1006007, 1006009, 1006014, 1006018, 1006021, 1006023, 1006030, 1006032, 1006037, 1006040];
    var Current_QueryYear = new Date().getFullYear();
    $(function () {
        $('#SchoolId').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenHasKindergartenPageList")' + "?PageSize=10000", key: 'Id', value: 'Name',
            defaultName: '单位名称',
            minimumResultsForSearch: 0
        });

        ComBox.loadSearchYear($("#BackupYear"), "年度");
    
        //获取加载列
        $.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetDictionaryListJson")',
            data: { dicids: Query_DicIds },
            async: false,
            type: 'post',
            success: function (obj) {
                if (obj.Tag == 1) {
                    columnFunRooms = obj.Data;
                }
            }
        });
        initGrid();

    });

    function initGrid() {
        var columnsGroup = [{
            title: '序号', align: 'center', colspan: 1, rowspan: 2, halign: 'center', align: 'left', valign: 'middle',
            formatter: function (value, row, index) {
                if (row.SchoolId > 0) return index + 1;
                else return '';
            }
        },
        {
            field: 'SchoolCode', title: '<div style="width:80px;">单位编号</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
            formatter: function (value, row, index) {
                if (row.SchoolId > 0) return value;
                else return '';
            }

        },
        {
            field: 'SchoolName', title: '<div style="width:120px;">单位名称</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
            formatter: function (value, row, index) {
                var html = value;
                if (html == "<b>总计：<b>") {
                    html = "<div style='text-align:center;'><b>总计：<b></div>";
                }
                return html;
            }
        }];
        var columns = []; 
        if (columnFunRooms != undefined && columnFunRooms.length > 0) {
            for (var i = 0; i < columnFunRooms.length; i++) {
                var itemObj = columnFunRooms[i];
                columnsGroup.push({ title: itemObj.DicName + '（人）', halign: 'center', align: 'center', colspan: 2 , valign: 'middle'});

                columns.push({
                    field: 'Dic_' + itemObj.DictionaryId, title: '人数', halign: 'center', align: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        if (row.SchoolId > 0) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                    }
                });

                columns.push({
                    field: 'Dic_' + itemObj.DictionaryId + "_2", title: '其中专职', halign: 'center', align: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        if (row.SchoolId > 0) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                    }
                });
            }
        }
         

        columnArr = [
            columnsGroup,
            columns
        ];
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            var postData = { ModuleId: @BackupsModuleIdEnum.ZbStatistics.ParseToInt(), 
                PageCode: "@BackupsPageCodeEnum.LabManager.ParseToInt().ToString()", 
                BackupType: @BackupTypeEnum.DataTitleJson.ParseToInt(), 
                BackupYear: thisYear };
            $.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetBackupsTitleJson")',
                async: false,
                type: 'get',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var colArr = obj.Data[0].Content;
                        if (typeof (colArr) == "string") {
                            colArr = JSON.parse(colArr);
                        }
                        if (colArr && colArr.length > 0) {
                            columnArr = [];
                            for (var i = 0; i < colArr.length; i++) {
                                columnArr.push(Syjx.GetGridTableColumns(colArr[i]));
                            }
                        }
                    } else {

                    }
                }
            });
        }
        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetLabManagerListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Sort ASC ,SchoolId DESC ',
            columns: columnArr,
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.DicIds = Query_DicIds.join(",");
                return queryString;
            }
        });
    }

    function searchGrid() {
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        if (Current_QueryYear != thisYear && parseInt(thisYear) > 0) {
            Current_QueryYear = thisYear;
            $('#gridTable').bootstrapTable('destroy');
            initGrid();//重新表头。
        } else {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
    }


    function resetGrid() {
        $("#SchoolId").ysComboBox('setValue', -1);
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function exportForm() {
        var url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/LabManagerExport")';
        var postData = $("#searchDiv").getWebControls();
        postData.DicIds = Query_DicIds.join(",");
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/BackupExport")';
            postData.PageCode = '@BackupsPageCodeEnum.LabManager.ParseToInt().ToString()';
        }   
        ys.exportExcel(url, postData);
    }

    // function backupsGrid() {
    //     var postData = { year: 2023 };
    //     ys.ajax({
    //         url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/SaveBackUpsFormJson")',
    //         type: 'POST',
    //         data: postData,
    //         success: function (obj) {
    //             if (obj.Total > 0) {
                    
    //             } else {
                    
    //             }
    //         }
    //     });
    // }
</script>
