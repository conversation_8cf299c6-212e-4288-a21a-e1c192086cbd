﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<div class="container-div">
    @*<div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、是指对实验（专用）室管理员可以管理那些实验室或专用室进行授权；<br />
                            2、实验室是指小学科学和中学理化生实验室；专用室是指除实验室以外的室。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <input id="RealName" col="RealName" type="text" placeholder="姓名" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-primary btn-sm" onclick="resetGrid()"><i class="fa fa-search"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 新增</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'RealName', title: '姓名', sortable: true, width: 200,  halign: 'center', valign: 'middle'  },
                { field: 'SubjectNamez', title: '管理科目', sortable: true, halign: 'center', valign: 'middle' },
                {
                    field: 'IsExperimenter', title: '是否实验员', sortable: true, width: 200, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (value ==@IsEnum.Yes.ParseToInt()) {
                            html = "@IsEnum.Yes.GetDescription()";
                        } else {
                              html = "@IsEnum.No.GetDescription()";
                        }
                        return html;
                    }
                },
                {
                    field: 'ExperimenterNature', title: '实验员性质', sortable: true, width: 200, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (value ==@ExperimenterNatureEnum.FullTime.ParseToInt()) {
                            html = "@ExperimenterNatureEnum.FullTime.GetDescription()";
                        } else if (value ==@ExperimenterNatureEnum.PartTime.ParseToInt()) {
                            html = "@ExperimenterNatureEnum.PartTime.GetDescription()";
                        }
                        return html;
                    }
                },
                { field: 'Remark', title: '备注', sortable: true, width: 180, halign: 'center', valign: 'middle' },
                {
                    field: 'opt1', title: '操作', width: 140, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="showSaveForm(true,\'{0}\');"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'{0}\');"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $("#RealName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError('每次只能修改一条数据，请选择一条数据！');
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        var url = '@Url.Content("~/BusinessManage/UserSchoolStageSubject/Form")' + '?id=' + id;
        createMenuItem(url, "管理员授权管理页面");
    }

    function deleteForm(selectid) {
        var ids = '';
        var confirmTitle = '';
        if (parseInt(selectid) > 0) {
            ids = selectid;
            confirmTitle = '确认要删当前这条数据吗？';
        } else {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                ids = ys.getIds(selectedRow);
                confirmTitle = ('确认要删除选中的' + selectedRow.length + '条数据吗？');
            } else {
                ys.msgError('请选择需要删除的数据！');
                return;
            }
        }
        ys.confirm(confirmTitle, function () {
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>
