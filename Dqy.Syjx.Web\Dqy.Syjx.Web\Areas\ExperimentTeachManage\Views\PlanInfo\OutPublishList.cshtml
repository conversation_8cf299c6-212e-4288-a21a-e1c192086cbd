﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .span-left-tag {
        margin-left: 82px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolYearStart" col="SchoolYearStart" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolTerm" col="SchoolTerm" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="CourseId" col="CourseId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="Statuz" col="Statuz" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="PlanName" col="PlanName" placeholder="名称" style="display: inline-block;width:180px;" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnPublish" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-rss"></i> 发布计划</a>
            <a id="btnCancel" class="btn btn-warning" onclick="showCancelForm(0)"><i class="fa fa-reply"></i> 撤销发布</a>
            <a id="btnLook" class="btn btn-info" onclick="showLookForm(0)"><i class="fa fa-eye"></i> 查看信息</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    $(function () {
        loadCourse();
        ComBox.SchoolTermYear($('#SchoolYearStart'), undefined, '学年', 10);
        $("#SchoolTerm").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())),
            defaultName: '学期'
        });
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#SchoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#SchoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        var statuzArr = [];
        var statuzlist = ys.getJson(@Html.Raw(typeof(PublishStatusEnum).EnumToDictionaryString()));
        if (statuzlist && statuzlist.length > 0) {
            statuzlist.forEach(element => {
                console.log(element);
                if (element.Key > 0) {
                    statuzArr.push(element);
                }
            });
        }
        $("#Statuz").ysComboBox({
            data: statuzArr,
            minimumResultsForSearch: -10000,
            defaultName: '发布状态'
        });
        initGrid();


    });

    function loadCourse() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolCourseListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    bindCourseData(obj.Data);
                } else {
                    bindCourseData({});
                }
            }
        });
    }
    function bindCourseData(data) {
        $('#CourseId').ysComboBox({
            data: data,
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '课程'
        });
    }
    function initGrid() {
        var statuzPublish = @PublishStatusEnum.Published.ParseToInt();
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/PlanInfo/GetOutPublishPageJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                { checkbox: true, visible: true },
                {
                    field: 'SchoolYearStart', title: '学期', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2);
                        if (@SchoolTermEnum.NextSemester.ParseToInt()== value) {
                            html += '@SchoolTermEnum.NextSemester.GetDescription()';
                        } else {
                            html += '@SchoolTermEnum.LastSemester.GetDescription()';
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '适用学科', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'PlanName', title: '实验计划名称', sortable: true, width: 300, halign: 'center', valign: 'middle' },
                {
                    field: 'UseNum', title: '实验清单', width: 70, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-info btn-xs" href="#" onclick="openLookDetailForm(\'' + row.CourseId + '\')"><i class="fa fa-eye"></i>查看</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { field: 'Num', title: '实验数量', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'RealName', title: '编制人', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'Statuz', title: '状态', sortable: true, width: 60, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.StatuzName;
                    }
                },
                {
                    field: 'PublishDate', title: '发布时间', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '--';
                        if (value && row.Statuz == statuzPublish && value.length > 0) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#Statuz").ysComboBox('setValue', -1);
        $("#CourseId").ysComboBox('setValue', -1);
        $("#SchoolYearStart").ysComboBox('setValue', -1);
        $('#SchoolTerm').ysComboBox('setValue', -1);
        $("#PlanName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(id) {
        var ids = '';
        var tagMsg = '';
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            console.log(selectedRow);
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                //验证选择的状态是否为待发布。
                var errorMsg = '';
                if (selectedRow.length > 1) {
                    ys.msgError('每次只能选择一条计划发布。');
                    return false;
                }
                $.each(selectedRow, function (i, obj) {
                    if (obj.Statuz != null && obj.Statuz!=1) {
                        errorMsg = "已发布的实验，禁止再选择发布。";
                    }
                });
                if (errorMsg!='')
                {
                    ys.msgError(errorMsg);
                    return false;
                }
                id = ys.getIds(selectedRow);
            }
        } 
        var url = '@Url.Content("~/ExperimentTeachManage/PlanInfo/OutPublishForm")' + '?id=' + id;
        createMenuItem(url, "发布计划");
    }
   
    //撤销发布
    function showCancelForm(id) { 
        var schoolYearStart = $("#SchoolYearStart").ysComboBox('getValue');
        var schoolTerm = $("#SchoolTerm").ysComboBox('getValue');
        if (Com_SchoolTermYear != schoolYearStart || Com_SchoolTerm != schoolTerm) {
            ys.msgError("撤销失败，禁止撤销非当前学年发布计划。");
        }
        var ids = '';
        var tagMsg = '';
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                tagMsg = '确认要撤销选中的' + selectedRow.length + '条发布计划吗？';
                ids = ys.getIds(selectedRow);
            }
        } else {
            ids = id;
            tagMsg = '确认要撤销当前发布计划数据吗？';
        }
        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/PlanInfo/CancelPublishOutFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

        //查看
    function showLookForm() {
        ys.openDialog({
            title: '查看信息',
            content: '@Url.Content("~/ExperimentTeachManage/PlanInfo/QrCodeForm")',
            width: '668px',
            height: '440px;',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function openLookDetailForm(courseid) {
        var url = '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/OutViewList")' + '?courseid=' + courseid;
        createMenuItem(url, "实验清单");
    }

    function removeConfirm() {
        $(".layui-layer-btn0").hide();
    }
</script>
