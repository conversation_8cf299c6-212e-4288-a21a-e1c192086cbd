﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

<style type="text/css">

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    @await Html.PartialAsync("/Areas/InstrumentManage/Shared/DeclarationSearchPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    @await Html.PartialAsync("/Areas/InstrumentManage/Shared/DeclaretionStatuzPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetPurchaseYearStatisticsPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showFooter: true,
            columns: [
                {
                    field: 'opt', title: '操作',
                    formatter: function (value, row, index) {
                        var html = '';
                        html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-eye"></i>查看</a>', row.Id);
                        return html;
                    }
                },
                { field: 'Course', title: '适用学科', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Course,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '<b>总计（元）：</b>';
                    }
                },
                //{ field: 'PurchaseYear', title: '采购年度' },
                //{
                //    field: 'InstrumentClassName', title: '仪器类别',
                //    footerFormatter: function (value) {
                //        return "<b>总计（元）：</b>";
                //    }
                //},
                {
                    field: 'Name', title: '仪器名称', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            var html = "";
                            if (row.IsDangerChemical == 1) {
                                html += Syjx.GetDangerHtml();
                            }
                            html += value;
                            return html;
                        }
                        else return '';
                    }
                },
                {
                    field: 'Model', title: '规格属性', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                {
                    field: 'Num', title: '数量', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'UnitName', title: '单位', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.UnitName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Price', title: '单价', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        if (value) return ComBox.ToLocaleString(value);
                        else return '';
                    }
                },
                {
                    field: 'AmountSum', title: '金额', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        if (row.Id) return ComBox.ToLocaleString(value);
                        else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                    }
                },
               
                { field: 'UserName', title: '填报人', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.UserName, },
                { field: 'ApprovalDate', title: '审批日期', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime, },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function look(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseDetail")' + '?id=' + id;
        createMenuItem(url, "查看计划");
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#purchaseYear').ysComboBox('setValue', -1);
        $('#instrumentClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#statuz').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

</script>
