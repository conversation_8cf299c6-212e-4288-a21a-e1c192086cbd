﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment

@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsrender.js"),true)
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jsrender/jsviews.js"),true)

<style>


    #searchDiv {
        position: fixed;
        /*  position: sticky;*/
        width: 96%;
        top: 0px;
        z-index: 996;
    }

    .container-div {
        padding: 10px 35px;
        /*  height: 100%;
                overflow-y: auto;*/
    }


    .namesearch {
        display: block;
        position: absolute;
        font-size: 14px;
        border: 1px solid #0077cc;
        border-radius: 5px;
        width: 200px;
        max-height: 200px;
        background-color: #fff;
        overflow: auto;
        box-shadow: 0px 0px 2px #0077cc;
        margin: 5px 0;
        padding: 5px 0;
        z-index: 999;
    }

    .hide {
        display: none !important;
    }

    .show {
        display: block !important;
    }

    .form-control {
        display: block;
        width: 100%;
        /*/*height: 100%;*/ */ font-size: 14px;
        color: #555;
        background-color: #fff;
        background-image: none;
        /****控制输入框无边框及颜色***/
        border: none;
        /****控制输入框颜色显示***/
        /* border:solid 1px #1ab394;*/
        border-radius: 4px;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

        .form-control:focus {
            outline: none;
            border: 1px solid #0077cc;
        }

    option:hover {
        background-color: #90bafb;
        color: #ffffff;
    }

    option {
        text-align: left;
        padding-left: 5px;
        word-break: break-all;
        white-space: pre-wrap;
    }

    .coursearch {
        display: block;
        position: absolute;
        font-size: 14px;
        border: 1px solid #0077cc;
        border-radius: 5px;
        width: 200px;
        max-height: 200px;
        background-color: #fff;
        overflow: auto;
        box-shadow: 0px 0px 2px #0077cc;
        margin: 5px 0;
        padding: 5px 0;
        z-index: 999;
    }

    .form-control[disabled],
    .form-control[readonly],
    fieldset[disabled] .form-control {
        background-color: #fff;
        opacity: 1;
    }



    .padTd {
        padding: 0 !important;
        height: 32px;
    }

    .content {
        width: 100%;
        height: 100%;
        line-height: 32px;
        padding: 0 8px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }


    .nameContent {
        width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .modelContent {
        width: 240px;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .modelDiv {
        display: block;
        position: absolute;
        font-size: 14px;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 240px;
        height: 120px;
        max-height: 300px;
        background-color: #fff;
        overflow: auto;
        box-shadow: 0px 0px 10px #ceccca;
        margin: 2px 0;
        padding: 10px 5px;
        z-index: 999;
    }

    .modeltextarea {
        width: 100%;
        height: 100%;
        border: none;
        resize: none;
        cursor: pointer;
    }

    .area-control .form-control {
        display: block;
        width: 100%;
        height: 100%;
        font-size: 14px;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        border-radius: 4px;
        -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
        -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
        transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    }

        .area-control .form-control:focus {
            outline: none;
            border: 1px solid #0077cc;
        }

    #gridTable tbody tr td {
        padding: 0;
    }

    .form-control {
        padding: 12px !important;
    }


    #addTable th,
    #addTable td {
        text-align: center;
        width: 140px;
        padding: 5px !important;
        border: none;
    }

    #addTable tr {
        border-top: 1px solid #e3e3e3;
    }

    #addTable thead tr {
        border-top: 1px solid #000000;
    }

    #addTable tbody tr:hover {
        background-color: #ECF5FF;
    }

    #addTable thead {
        background-color: #eeeeee;
    }

    .noform-control {
        display: block;
        width: 100%;
        height: 100%;
        font-size: 14px;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid red;
        border-radius: 4px;
        -webkit-box-shadow: none;
        box-shadow: none;
    }

    /*   #gridTable tbody > tr > td {
                padding: 2px;
            }*/

    #gridTable tbody tr:nth-child(2n+1) td .form-control {
        background-color: #F9F9F9;
    }

    #gridTable tbody tr:nth-child(2n) td .form-control {
        background-color: #FFFFFF;
    }

    #gridTable tbody tr:hover td .form-control {
        background-color: #F5F5F5;
    }

    #gridTable tbody tr td {
        position: relative;
        overflow: visible;
    }

    .table {
        margin-top: 10px;
        margin-bottom: 20px;
    }

    #inputObj:focus {
        outline: none;
        /*height:26px;*/
        border: 1px solid #0077cc;
    }

    #inputObj {
        border: none;
    }

    #searchLeft {
        position: absolute;
        padding-left: 8px;
        padding-top: 3px;
        color: #C0C4CC;
    }

    .iconDown {
        position: absolute;
        top: 8px;
        right: 5px;
    }

    .iconUp {
        position: absolute;
        top: 11px;
        right: 5px;
    }


    @@media (max-width: 768px) {
        #divCourseInfo {
            margin-top: 10px !important;
        }
    }

</style>


<div class="container-div">

    <div id="searchDiv" class="col-sm-12 search-collapse">
        <div class="select-list">
            <ul>
                <li>
                    <span id="schoolStageId" col="StageId" style="display:inline-block;width:100px;"></span>
                </li>
                <li>
                    <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                </li>
                <li>
                    <input id="realName" col="RealName" type="text" placeholder="实验员/管理员" style="display: inline-block;width:160px;" />
                </li>
                <li>
                    <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                </li>
                <li>
                    <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                </li>
                <!--帮助文档需要内容,id值必须为“helpBtn”-->
                <li>
                    <i class="fa fa-question-circle" id="helpBtn"></i>
                </li>
            </ul>
        </div>
    </div>

    <div id="divCourseInfo" style="margin-top:60px;">
    </div>

</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->

<div id="namesearch" class="namesearch hide">



</div>



<div id="tipMsg"></div>

<script id="dateList" type="text/x-jsrender">
    <div class="col-sm-12 select-table table-striped">
        <div class="bootstrap-table" >
            <div class="fcontainer" style="padding-bottom: 0px;">
                <div class="fixed-table-body" >
                    <form id="commentForm">
                        <table id="gridTable" data-mobile-responsive="true" class="table table-hover table-striped table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">操作</div><div class="fht-cell"></div>
                                </th>

                                <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">序号</div><div class="fht-cell"></div>
                                </th>

                                 <th style="text-align: center; vertical-align: middle;" data-field="Code">
                                    <div class="th-inner sortable both">学段</div><div class="fht-cell"></div>
                                </th>

                                 <th style="text-align: center; vertical-align: middle;" data-field="Code">
                                    <div class="th-inner sortable both">学科</div><div class="fht-cell"></div>
                                </th>





                                <th style="text-align: center; vertical-align: middle;" data-field="Name">
                                    <div class="th-inner sortable both">实验员/管理员</div><div class="fht-cell"></div>
                                </th>
                              @*  <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">是实验员</div><div class="fht-cell"></div>
                                </th>
*@

                                 <th style="text-align: center; vertical-align: middle;" data-field="Stage">
                                    <div class="th-inner sortable both">实验员性质</div><div class="fht-cell"></div>
                                </th>

                                 <th style="text-align: center; vertical-align: middle;">
                                    <div class="th-inner sortable both">备注</div><div class="fht-cell"></div>
                                </th>


                            </tr>
                        </thead>
                        <tbody id="list">
                             {^{for data}}
                                 <tr>
                                     <td style="text-align: center;width:100px; white-space:nowrap;">
                                        <input type="hidden" data-link="Id" value="{{>Id}}" />
                                        <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.addRow #index}" value=""><i class="fa fa-plus"></i>新增</a>

    @* {{if Id > 0}}*@
                                            <a class="btn btn-link btn-xs" href="#" style="color:#537BF0;" data-link="{on ~root.deleteRow #getIndex()}" value=""><i class="fa fa-remove">删除</i></a>
    @*{{/if}}*@
                                    </td>

                                     <td style="text-align: center;width:50px; ">{^{:#index + 1}}</td>

                                      <td style="text-align: center;width:80px;">
                                           {{>SchoolStageName}}
                                    </td>

                                     <td style="text-align: center;width:80px;">
                                           {{>SubjectName}}
                                    </td>



                                    <td style="text-align: center;width:120px;">
    @* <input index="{{:#index}}" name="inputName" value="{{>RealName}}" autocomplete="off"  oninput="yearInput(this)" style="text-align:center;" class="form-control form-control-lg" type="text" placeholder=""/>*@
    @*  <input index="{{:#index}}" name="inputName" value="{{>RealName}}" autocomplete="off" readOnly  onclick="userClick(this)" style="text-align:center;" class="form-control form-control-lg" type="text" placeholder=""/>
                                       <div class="hide widthDiv"></div>*@
                                           <input index="{{:#index}}" name="inputName" value="{{>RealName}}" autocomplete="off"  readOnly  onclick="userClick(this,{{>Id}},{{>SchoolStageId}},{{>SubjectId}})" id="userRealName"  class="form-control form-control-lg" style="cursor: pointer;" type="text" placeholder=""/>

                                       <div class="hide widthDiv"></div>
                                       <i class="fa fa-sort-down iconDown"></i>
                                       <i class="fa fa-sort-up iconUp hide"></i>
                                    </td>
 
                                        <td style="text-align: center;width:80px;">
                                            <input index="{{:#index}}" name="inputName" value="{{>ExperimenterNatureName}}" autocomplete="off" readOnly  onclick="stageClick(this,{{>Id}},{{>SchoolStageId}},{{>SubjectId}})" style="text-align:center;cursor: pointer;" class="form-control form-control-lg" type="text" placeholder=""/>
                                            <div class="hide widthDiv"></div>
                                             <i class="fa fa-sort-down iconDown"></i>
                                            <i class="fa fa-sort-up iconUp hide"></i>
                                        </td>
                                   


                                    <td style="text-align: left;width:200px;cursor: pointer;" class="padTd">
                                            <div class="content reason"  title="{{>Remark}}" data-content="{{>Remark}}" onclick="modelClick(this,{{>Id}},{{>SchoolStageId}},{{>SubjectId}})" id="ReasonHover">{{>Remark}}</div>
                                            <div  class="area-control hide"><textarea data-link="Remark"  onchange="reasonTextarea(this,{{>Id}},{{>SchoolStageId}},{{>SubjectId}})" class="form-control form-control-lg modeltextarea" type="text" maxlength="150">{{>Remark}}</textarea></div>
                                    </td>


                                </tr>
                             {{/for}}
                        </tbody>
                    </table>
                    </form>
                </div>
            </div>
        </div><div class="clearfix"></div>
    </div>
</script>

<script type="text/javascript">

    var course_info = null;
    var StageData = [];
    var userData = [];
    var typeSearch = 0;

    $(function () {

        $(".inputprompt").popover({
            trigger: 'hover',
            html: true
        });
        
        
        getCourse();
        loadSchoolStage();

        course_info = {
            data: [],
            addRow: function (index) {
                var currentObj = this.data[index];
                var postData = { Id: 0, Nature: currentObj.Nature, SchoolStageId: currentObj.SchoolStageId, SchoolStageName: currentObj.SchoolStageName, SubjectId: currentObj.SubjectId, SubjectName: currentObj.SubjectName, UserId: 0, RealName: '', IsExperimenter: 0, ExperimenterNature: 0, ExperimenterNatureName: '', Remark: '' };
                ys.ajax({
                    url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/SaveStageSubjectFormJson")',
                    type: "post",
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess("新增成功");
                            $.observable(course_info.data).insert(index + 1, { Id: obj.Message, Nature: currentObj.Nature, SchoolStageId: currentObj.SchoolStageId, SchoolStageName: currentObj.SchoolStageName, SubjectId: currentObj.SubjectId, SubjectName: currentObj.SubjectName, UserId: 0, RealName: '', IsExperimenter: 0, ExperimenterNature: 0, ExperimenterNatureName: '', Remark: '' });
                            //刷新页面
                            getForm();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });

            },
            deleteRow: function (index) {
                //console.log("index:"+index);
                var obj = this.data[index];
                var selectData = this.data.filter((item, index) => {
                    return item.SchoolStageId == obj.SchoolStageId && item.SubjectId == obj.SubjectId
                })
                if (selectData.length == 1 && obj.UserId == 0) {

                } else {
                    if (obj.UserId > 0) {
                        ys.confirm("确定要删除吗？", function () {
                            ys.ajax({
                                url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/DeleteStageSubjectFormJson")' + '?id=' + obj.Id,
                                type: 'post',
                                success: function (obj) {
                                    if (obj.Tag == 1) {
                                        ys.msgSuccess(obj.Message);

                                        if (selectData.length == 1) {
                                            var tempRow = course_info.data[index];
                                            tempRow.Id = 0;
                                            tempRow.UserId = 0;
                                            tempRow.RealName = "";
                                            tempRow.IsExperimenter = 0;
                                            tempRow.ExperimenterNature = 0;
                                            tempRow.ExperimenterNatureName = "";
                                            tempRow.Remark = "";
                                            $.observable(course_info.data).remove(index).insert(index, tempRow);
                                        } else {
                                            $.observable(course_info.data).remove(index);
                                            //刷新页面
                                            getForm();
                                        }
                                    }
                                    else {
                                        ys.msgError(obj.Message);
                                    }
                                }
                            });
                        }, function () {

                        });
                    } else {
                        ys.ajax({
                            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/DeleteStageSubjectFormJson")' + '?id=' + obj.Id,
                            type: 'post',
                            success: function (obj) {
                                if (obj.Tag == 1) {
                                    ys.msgSuccess(obj.Message);
                                    $.observable(course_info.data).remove(index);
                                    //刷新页面
                                    getForm();
                                }
                                else {
                                    ys.msgError(obj.Message);
                                }
                            }
                        });
                    }
                }
            },
        };

        $.templates("#dateList").link("#divCourseInfo", course_info);

        getForm();
        loadUser();
        loadExperimenterNature();

        $(".select2-container").width("100%");
        $(".select-list");

    });

    $(".white-bg").scroll(function () {

        let top = $(".white-bg").scrollTop()
        if (top > 0) {
            $("#searchDiv").css({
                "top": "-10px"
            })
        } else {
            $("#searchDiv").css({
                "top": "0px"
            })
        }

    });

    function loadExperimenterNature() {
        StageData = ys.getJson(@Html.Raw(typeof(ExperimenterNatureEnum).EnumToDictionaryString()));
    }


    function loadUser() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/GetUnitLaboratoryUserListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {

                    userData = obj.Data;

                    //$('#SysUserId').ysComboBox({
                    //    data: obj.Data,
                    //    key: 'Id',
                    //    value: 'RealName',
                    //    defaultName: '管理人'
                    //});
                    //if (obj.Data.length == 1) {
                    //    $("#SysUserId").ysComboBox('setValue', obj.Data[0].DictionaryId);
                    //}
                }
            }
        });
    }

    /**
     *
     * 绑定Table Json数据
     */
    function getForm() {
        var url = '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetStageSubjectList")';
        if (typeSearch != 0) {
            var schoolStageId = $("#schoolStageId").ysComboBox('getValue');
            var subjectId = $("#courseId").ysComboBox('getValue');
            var realName = $("#realName").val();
            url = '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetStageSubjectList")' + '?SchoolStage=' + schoolStageId + '&SubjectId=' + subjectId + '&RealName=' + realName + '';
        }
        ys.ajax({
            url: url,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $.observable(course_info).setProperty("data", obj.Data);
                }
            }
        });
    }

    /***
     * 查询
     */
    function searchGrid() {
        typeSearch = 1;
        getForm();
    }

    /***
     * 重置
     */
    function resetGrid() {
        typeSearch = 0;
        $("#schoolStageId").ysComboBox('setValue', -1);
        $("#courseId").ysComboBox('setValue', -1)
        $("#realName").val('');
        getForm();
    }

    /**
     *
     * 备注信息弹出框修改
     */
    function modelTextarea(obj) {
        //赋值给span标签
        $($(obj).parent().prev()).text($(obj).val());
        $(obj).parent().parent().children(".model").removeClass('noform-control');
        console.log(course_info.data, $(obj).parent().parent().children(".model").text())
    }
    $(document).on('mouseup', function (e) {
        //只有在mouseup是展示的效果是正确的，down不行
        let e_class = e.target.className;
        if (e_class != 'form-control form-control-lg modeltextarea' && e_class != 'area-control modelDiv') {
            $('.area-control').removeClass("modelDiv")
            $('.area-control').addClass("hide")
        }
    })
    function modelClick(obj) {

        $(obj.nextElementSibling).removeClass("hide")
        $(obj.nextElementSibling).addClass("modelDiv")
        $(obj.nextElementSibling.firstChild).focus()
        let outerWdh = $(obj).parent().outerWidth()
        $('.modelDiv').css({
            "width": outerWdh + "px",
        })

        let popupH = $(".modelDiv").height() + 30
        popupHeigth(obj, popupH, '.modelDiv')
    }


    /**
     *
     * 采购理由弹出框修改
     */
    function reasonTextarea(obj, id, stageId, subjectId) {
        //var index = $($(obj).parent().parent().siblings()[1]).text() - 1;
        var index = getIndexById(id, stageId, subjectId);
        var id = course_info.data[index].Id;
        if (id > 0) {
            course_info.data[index].Remark = $(obj).val();
            var postData = course_info.data[index];
            postData.EditType = 3;
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/SaveStageSubjectFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess("执行成功");

                    }
                    else {
                        layer.alert(obj.Message, {
                            icon: 2,
                            skin: 'layer-ext-moon'
                        });
                    }
                }
            });
        }
        //console.log("index:"+index);
        //赋值给span标签
        $($(obj).parent().prev()).text($(obj).val());
        $(obj).parent().parent().children(".reason").removeClass('noform-control');


    }
    $(document).on('mouseup', function (e) {
        //只有在mouseup是展示的效果是正确的，down不行
        let e_class = e.target.className;
        if (e_class != 'form-control form-control-lg modeltextarea' && e_class != 'area-control modelDiv') {
            $('.area-control').removeClass("modelDiv")
            $('.area-control').addClass("hide")
        }
    })
    function reasonClick(obj) {
        $(obj.nextElementSibling).removeClass("hide")
        $(obj.nextElementSibling).addClass("modelDiv")
        $(obj.nextElementSibling.firstChild).focus()
        let outerWdh = $(obj).parent().outerWidth()
        $('.modelDiv').css({
            "right": "5px",
            "width": outerWdh + "px",
        })
        let popupH = $(".modelDiv").height() + 30

    }


    function popupHeigth(obj, popupH, cssDiv) {
        // 获取父元素高度进行定位
        let h = $(obj).height();//元素高度
        let wh = $(window).height();//浏览器窗口高度
        let xh = wh - (h + $(obj).offset().top - $(document).scrollTop());//元素到浏览器底部的高度
        // 获取表格元素高度进行定位
        let tabh = $(".fcontainer").height();//表格元素高度
        let tabxh = -(wh - (tabh + $(".fcontainer").offset().top - $(document).scrollTop()) - 20);//表格元素到浏览器底部的高度
        let th = $(obj).offset().top//元素距离顶部高度
        let tw = $(obj).offset().left//元素距离顶部高度
        let tabth = $(".fcontainer").offset().top//表格元素距离顶部高度
        let boxh = th - tabth//获取当前元素与表格的像素差进行判断
        let ph = $(obj).parent().outerHeight();// 获取父元素高度进行定位
        var Y = $(obj).offset().top;
        var X = $(obj).offset().left;
        console.log(X, Y)
        $(".namesearch").css({
            "top": th + 30 + "px",
            "left": tw + "px"
        });
        if (xh < popupH) {
            if (boxh < popupH || th < popupH) {
                $(cssDiv).css({
                    "bottom": tabxh + "px"
                });
                $("#gridTable tbody tr td").css({
                    "position": ""
                })
            } else {
                $(cssDiv).css({
                    "bottom": ph + "px"
                });
                $("#gridTable tbody tr td").css({
                    "position": "relative"
                })

            }
            $(".namesearch").css({
                "top": th -200 + "px"
            });
        }
    }

    /**
    *
    * 实验员性质点击事件
    */
    function stageClick(obj, id, stageId, subjectId) {
        //var index = $($(obj).parent().siblings()[1]).text() - 1;
        var index = getIndexById(id, stageId, subjectId);
        $(obj.nextElementSibling).removeClass("hide").addClass("coursearch")
        $(obj).next().next().removeClass("show").addClass("hide")
        $(obj).next().next().next().removeClass("hide").addClass("show")
        let searchSuggestions = $(obj.nextElementSibling);
        let searchTerm = $(obj).val();
        // 获取列表所有规格列表   绑定搜索条件通过条件筛选展示
        let data = StageData;
        searchSuggestions.html('');
        $.each(data, function (index, item) {
            let option = $('<option>');
            option.val(item.Key);
            option.text(item.Value);
            searchSuggestions.append(option);
        });
        // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
        //   新添加的option节点添加点击事件
        $(obj.nextElementSibling).off('click', 'option').on('click', 'option', function () {
            let selvaText = $(this).text();//选中的下拉选项
            let selvaValue = $(this).val();
            //$(obj).val(selvaText)//下拉选中的值赋值给span

            //此处修改data内对象的值
            course_info.data[index].ExperimenterNatureName = selvaText;
            course_info.data[index].ExperimenterNature = selvaValue;

            var id = course_info.data[index].Id;
            if (id > 0) {
                var postData = course_info.data[index];
                postData.EditType = 2;
                ys.ajax({
                    url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/SaveStageSubjectFormJson")',
                    type: "post",
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess("执行成功");
                            var tempRow = course_info.data[index];
                            $.observable(course_info.data).remove(index).insert(index, tempRow);
                            getForm();
                        }
                        else {
                            layer.alert(obj.Message, {
                                icon: 2,
                                skin: 'layer-ext-moon'
                            });
                        }
                    }
                });
            }

            // 选择后隐藏弹窗
            if (selvaText) {
                $(obj.nextElementSibling).removeClass("coursearch").addClass("hide")
                $(obj).next().next().removeClass("hide").addClass("show")
                $(obj).next().next().next().removeClass("show").addClass("hide")
            }
        });
        $('.widthDiv').width($(obj).innerWidth() + "px")
        $(obj).one("blur", () => {
            // 点击弹出框内部不隐藏
            $(obj.nextElementSibling).click(function (e) {
                e.stopPropagation()
            });
            // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
            $(document).one("click", function (e) {
                $(obj.nextElementSibling).removeClass("coursearch").addClass("hide")
                $(obj).next().next().removeClass("hide").addClass("show")
                $(obj).next().next().next().removeClass("show").addClass("hide")
            });
        })

        let popupH = $(".coursearch").height() + 30
        popupHeigth(obj, popupH, '.coursearch')
    }


    /**
    *
    * 是否实验员
    */
    function isSelfMadeClick(obj, id, stageId, subjectId) {
        //var index = $($(obj).parent().siblings()[1]).text() - 1;
        var index = getIndexById(id, stageId, subjectId);
        var isSelfMade = 0;
        if ($(obj).prop("checked")) {
            isSelfMade = 1;
        }
        course_info.data[index].IsExperimenter = isSelfMade;

        var id = course_info.data[index].Id;
        if (id > 0) {
            var postData = course_info.data[index];
            postData.EditType = 2;
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/SaveStageSubjectFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess("执行成功");
                        getForm();
                    }
                    else {
                        layer.alert(obj.Message, {
                            icon: 2,
                            skin: 'layer-ext-moon'
                        });
                    }
                }
            });
        }

    }

    function getCourse() {
        $('#courseId').ysComboBox({
            //url:'@Url.Content("~/PersonManage/UserClassInfo/GetUserDictionaryList")' + '?typeCode=' + @DicTypeCodeEnum.Course.ParseToInt(),
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });
    }

    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }

    ///**
    //*
    //* 人员点击事件
    //*/
    //function userClick(obj) {
    //    var index = $($(obj).parent().siblings()[0]).text() - 1;
    //    $(obj.nextElementSibling).removeClass("hide")
    //    $(obj.nextElementSibling).addClass("coursearch")
    //    let searchSuggestions = $(obj.nextElementSibling);
    //    let searchTerm = $(obj).val();
    //    // 获取列表所有规格列表   绑定搜索条件通过条件筛选展示
    //    let data = userData;
    //    searchSuggestions.html('');
    //    $.each(data, function (index, item) {
    //        let option = $('<option>');
    //        option.val(item.Id);
    //        option.text(item.RealName);
    //        searchSuggestions.append(option);
    //    });
    //    // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
    //    //   新添加的option节点添加点击事件
    //    $(obj.nextElementSibling).on('click', 'option', function () {
    //        let selvaText = $(this).text();//选中的下拉选项
    //        let selvaValue = $(this).val();
    //        //$(obj).val(selvaText)//下拉选中的值赋值给span


    //        //此处修改data内对象的值
    //        course_info.data[index].RealName = selvaText;
    //        course_info.data[index].UserId = selvaValue;

    //        //执行ajax向数据库中插入数据
    //        var postData = course_info.data[index];
    //        //console.log("postData:" + JSON.stringify(postData));
    //        ys.ajax({
    //            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/SaveStageSubjectFormJson")',
    //            type: "post",
    //            data: postData,
    //            success: function (obj) {
    //                if (obj.Tag == 1) {
    //                    ys.msgSuccess("执行成功");
    //                    //console.log("obj.Message:" + obj.Message);
    //                    course_info.data[index].Id = obj.Message;
    //                    var tempRow = course_info.data[index];
    //                    $.observable(course_info.data).remove(index).insert(index, tempRow);
    //                    //刷新页面
    //                    getForm();
    //                }
    //                else {
    //                    //layer.alert(obj.Message, {
    //                    //    icon: 2,
    //                    //    skin: 'layer-ext-moon'
    //                    //});
    //                    ys.msgError(obj.Message);
    //                }
    //            }
    //        });

    //        // 选择后隐藏弹窗
    //        if (selvaText) {
    //            $(obj.nextElementSibling).removeClass("coursearch")
    //            $(obj.nextElementSibling).addClass("hide")
    //        }
    //    });
    //    $('.widthDiv').width($(obj).innerWidth() + "px")
    //    $(obj).blur(() => {
    //        // 点击弹出框内部不隐藏
    //        $(obj.nextElementSibling).click(function (e) {
    //            e.stopPropagation()
    //        });
    //        // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
    //        $(document).one("click", function (e) {
    //            $(obj.nextElementSibling).removeClass("coursearch")
    //            $(obj.nextElementSibling).addClass("hide")
    //        });
    //    })

    //    let popupH = $(".coursearch").height() + 30
    //    popupHeigth(obj, popupH, '.coursearch')
    //}



    //// 输入框输入事件
    //function yearInput(obj) {
    //    $(obj.nextElementSibling).removeClass("hide")
    //    $(obj.nextElementSibling).addClass("coursearch")
    //    let searchSuggestions = $(obj.nextElementSibling);
    //    let searchTerm = $(obj).val();
    //    console.log("searchTerm:" + searchTerm);
    //    // 获取列表所有规格列表   绑定搜索条件通过条件筛选展示
    //    if (searchTerm.length > 0) {
    //        let data = userData;
    //        let newArr = data.filter((item) => item.RealName.indexOf(searchTerm) != -1)
    //        // console.log(searchTerm)//输入值
    //        // console.log(newArr)//过滤符合条件的数组
    //        searchSuggestions.html('');
    //        if (newArr.length) {
    //            $.each(newArr, function (index, item) {
    //                let option = $('<option>');
    //                option.val(item.Id);
    //                option.text(item.RealName);
    //                searchSuggestions.append(option);
    //            });
    //        } else {
    //            searchSuggestions.append('<div>无匹配数据</div>');
    //        }
    //    } else {
    //        $(obj.nextElementSibling).removeClass("namesearch")
    //        $(obj.nextElementSibling).addClass("hide")
    //    }

    //    $(obj).blur(() => {
    //        // 点击弹出框内部不隐藏
    //        $(obj.nextElementSibling).click(function (e) {
    //            e.stopPropagation()
    //        });
    //        // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
    //        $(document).one("click", function (e) {
    //            $(obj.nextElementSibling).removeClass("namesearch")
    //            $(obj.nextElementSibling).addClass("hide")
    //        });
    //    })
    //    //   新添加的option节点添加点击事件
    //    $(obj.nextElementSibling).on('click', 'option', function () {

    //        let selvaText = $(this).text();//选中的下拉选项
    //        // var selvaVal = $(this).val();//选中的下拉选项的val  （传参值）
    //        $(obj).val(selvaText)//下拉选中的值赋值给span
    //        //此处修改data内对象的值

    //        // 选择后隐藏弹窗
    //        if (selvaText) {
    //            $(obj.nextElementSibling).removeClass("namesearch")
    //            $(obj.nextElementSibling).addClass("hide")
    //        }
    //    });
    //    $('.widthDiv').width($(obj).innerWidth()  + "px")
    //}
    // 实验员点击下拉搜索
    function userClick(obj, id, stageId, subjectId) {
        //var index = $($(obj).parent().siblings()[1]).text() - 1;
        var index = getIndexById(id, stageId, subjectId);

        $('.namesearch').removeClass("hide").addClass("show")
        $(obj).next().next().removeClass("show").addClass("hide")
        $(obj).next().next().next().removeClass("hide").addClass("show")
        let searchSuggestions = $('.namesearch');
        searchSuggestions.html('')


        // 获取列表所有规格列表   绑定搜索条件通过条件筛选展示

        let html1 = ''
        html1 += '<i id="searchLeft" class="fa fa-search"></i><input id="inputObj" type="text" autocomplete="off" style="width:100%; border:none;padding:0 5px;">'
        html1 += '<div id="searchOption"></div>'
        searchSuggestions.html(html1)
        var searchOption = $('.namesearch').children("div")
        var searchInput = $('.namesearch').children("input")
        searchInput.focus()
        // 开始进入显示全部 不需要可删
        let data = userData;
        $.each(data, function (i, item) {
            let option = $('<option>');
            option.val(item.Id);
            option.text(item.RealName);
            searchOption.append(option);
        });
        //

        $('.namesearch').on('input', "input", function (e) {
            // console.log($(this).val())
            let searchTerm = $(this).val();
            if (searchTerm.length > 0) {
                $(this).prev().hide()
                let data = userData;

                $.each(data, function (i, item) {
                    let option = $('<option>');
                    option.val(item.Id);
                    option.text(item.RealName);
                    searchOption.append(option);
                });

                let newArr = data.filter((item) => item.RealName.indexOf(searchTerm) != -1)
                // console.log(searchTerm)//输入值
                //console.log(newArr)//过滤符合条件的数组
                searchOption.html('');
                if (newArr.length) {
                    $.each(newArr, function (i, item) {
                        let option = $('<option>');
                        option.val(item.Id);
                        option.text(item.RealName);
                        searchOption.append(option);
                    });
                } else {
                    searchOption.append('<div>无匹配数据</div>');
                }
            }
            else {
                // 输入框为空显示全部
                let data = userData;
                $(this).prev().show()
                searchOption.html("");
                $.each(data, function (i, item) {
                    let option = $('<option>');
                    option.val(item.Id);
                    option.text(item.RealName);
                    searchOption.append(option);
                });
            }
        })
            $(searchInput).off("blur").one("blur", function () {
             
                // 点击弹出框内部不隐藏
                $('.namesearch').click(function (e) {
                       console.log(22222222222222222222,e)
                    e.stopPropagation()
                });
                // 点击弹出框外部隐藏// one添加事件  执行后立即删除事件
                $(document).one("click", function (e) {
                     
                    if ($(e.target).attr('id') != "userRealName") {
                         console.log(333333333333 ,'eeeee',e)
                        $('.namesearch').removeClass("show").addClass("hide")
                        $(obj).next().next().removeClass("hide").addClass("show")
                        $(obj).next().next().next().removeClass("show").addClass("hide")
                    }
                });

            })
        //   新添加的option节点添加点击事件
        $('.namesearch').off('click', 'option').on('click', 'option', function () {
            console.log($(this).text())
            let selvaText = $(this).text();//选中的下拉选项
            var selvaValue = $(this).val();//选中的下拉选项的val  （传参值）
            $(obj).val(selvaText)//下拉选中的值赋值给span

            //此处修改data内对象的值
            course_info.data[index].RealName = selvaText;
            course_info.data[index].UserId = selvaValue;

            //执行ajax向数据库中插入数据
            var postData = course_info.data[index];
            //console.log("postData:" + JSON.stringify(postData));
            postData.EditType = 1;
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/SaveStageSubjectFormJson")',
                type: "post",
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess("执行成功");
                        //console.log("obj.Message:" + obj.Message);
                        course_info.data[index].Id = obj.Message;
                        var tempRow = course_info.data[index];
                        $.observable(course_info.data).remove(index).insert(index, tempRow);
                        //刷新页面
                        getForm();
                    }
                    else {
                        ys.msgError(obj.Message);
                        course_info.data[index].RealName = '';
                        course_info.data[index].UserId = 0;
                        //刷新页面
                        getForm();
                    }
                }
            });

            //此处修改data内对象的值
            // 选择后隐藏弹窗
            if (selvaText) {
                $('.namesearch').removeClass("show").addClass("hide")
                $(obj).next().next().removeClass("hide").addClass("show")
                $(obj).next().next().next().removeClass("show").addClass("hide")
            }
        });
        $('.namesearch').width($(obj).innerWidth() + "px")
        let popupH = $(".namesearch").height() + 30
        popupHeigth(obj, popupH, '.namesearch')
    }

    function getIndexById(id, stageId, subjectId) {
        let index = -1;
        course_info.data.forEach((item, i) => {
            if (item.Id == id && item.SchoolStageId == stageId && item.SubjectId == subjectId) {
                index = i;
                return;
            }
        });
        return index;
    }

</script>


