﻿ <!--登录页和首页模板-->
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@{
    var loginCfg = await Dqy.Syjx.Web.CommonLib.Configs.GetWebTitle();
}
<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />

    <meta name="keywords" content="@loginCfg">
    <meta name="description" content="@loginCfg">

    <link rel="bookmark" href='@Url.Content("~/favicon.ico")' />
    <link rel="shortcut icon" href="@Url.Content("~/favicon.ico")" type="image/x-icon" />

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap/css/bootstrap.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fontawesome/4.7.0/css/fontawesome.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery/3.7.1/jquery.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap/js/bootstrap.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/layer/3.1.1/layer.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/style.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha.min.js"))

    <!--首页需要用到的js开始-->
    @*@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha-index.min.js"))
        <script type="text/javascript" src='@Url.Content("~/lib/jquery.metisMenu/1.1.3/metisMenu.js")'></script>
        <script type="text/javascript" src='@Url.Content("~/lib/jquery.slimscroll/1.3.8/jquery.slimscroll.min.js")'></script>*@

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/skins/custom.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/icheck.min.js"))
    @*@Dqy.Syjx.Util.BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
        @Dqy.Syjx.Util.BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))*@

    <!--首页需要用到的js结束-->
<title>@loginCfg</title>
    <style type="text/css">
        .nav > li:hover .dropdown-menu {
            display: block;
        }
    </style>
    <script type="text/javascript">
        var ctx = '@Url.Content("~/")';
    </script>
</head>
<body class="fixed-sidebar full-height-layout gray-bg login">
    <style type="text/css">
        .tableoption thead tr td {
            background-color: #e8e8e8;
            padding: 3px 5px;
            font-size: 16px;
        }

        .tableoption tr {
            height: 46px;
            text-align: left;
            display: table-row;
            vertical-align: inherit;
            border-color: inherit;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .choicetxt {
            height: 24px;
            padding: 2px 6px;
            font-size: 14px;
        }

        .option-icon {
            height: 30px;
            cursor: pointer;
        }

        .preview-namedesc {
            color: gray;
            padding-left: 25px;
        }

        .preview-picture {
            max-width: 400px;
            max-height: 400px;
            display: flex;
            padding-left: 25px;
        }

            .preview-picture img {
                width: auto;
                height: auto;
                max-width: 100%;
                max-height: 100%;
            }

        .paper-login, .paper-examine, .paper-finish {
            display: none;
        }
    </style>
    <div class="exterior__chapternav text-center paper-examine" style="width: 100%; left: 0px; position: fixed; top: 0; z-index: 1; height: 70px; background-color: #fff;">
        <div style=" width: 1100px; margin-left: calc(50% - 550px);">
            <div class="left-preview-tip">
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">手机号：<span id="spanMobile">--</span></label>
                    <label class="col-sm-3 control-label ">姓名：<span id="spanUserName">--</span></label>
                </div>
            </div>
            <div style="float:right;" onclick="saveSubmit();">
                <a class="btn btn-success" style="font-size: 16px; width: 100px;"><i class="fa fa-save"></i> 提交</a>
            </div>
        </div>
    </div>
    <div class="container-div" style="margin-top:90px;">
        <div class="col-sm-12 search-collapse paper-login" style="padding-bottom: 100px; width: 600px; margin-left: calc(50% - 300px);">
            <div id="paperTitle" style="text-align: center; padding: 20px;"><h1></h1></div>
            <form id="form" class="form-horizontal m">
                <div class="form-group useobj-teacher">
                    <label class="col-sm-3 control-label ">单位名称<font class="red"> *</font></label>
                    <div class="col-sm-7">
                        <input id="OrganizationName" name="OrganizationName" class="form-control" col="UserName" type="text" placeholder="单位名称">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">姓名<font class="red"> *</font></label>
                    <div class="col-sm-7">
                        <input id="RealName" name="RealName" class="form-control" col="UserName" type="text" placeholder="姓名">
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">手机号<font class="red"> *</font></label>
                    <div class="col-sm-7">
                        <input id="Mobile" name="Mobile" class="form-control" col="UserName" type="text" placeholder="手机号">
                    </div>
                </div>
                <div class="form-group useobj-student">
                    <label class="col-sm-3 control-label ">学号<font class="red"> *</font></label>
                    <div class="col-sm-7">
                        <input id="StudentNo" name="StudentNo" class="form-control" col="UserName" type="text" placeholder="学号">
                    </div>
                </div>
                <div class="form-group useobj-student">
                    <label class="col-sm-3 control-label ">年级班级名称<font class="red"> *</font></label>
                    <div class="col-sm-7">
                        <input id="GradeClassName" name="GradeClassName" class="form-control" col="UserName" type="text" placeholder="年级班级名称">
                    </div>
                </div>
                <div style="text-align: center;">
                    <a class="btn btn-success" style="font-size: 16px; " onclick="saveExamineRegister();"><i class="fa fa-save"></i> 开始考试</a>
                </div>
                <div>
                    <a style="float:left;">已有账号登录</a>
                </div>
                <hr style=" margin-top: 40px; margin-bottom: 40px;" />
                <div class="form-group row">
                    <label class="col-sm-3 control-label ">考核编码<font class="red"> *</font></label>
                    <div class="col-sm-7">
                        <input id="ExamineUserCode" name="ExamineUserCode" class="form-control" col="UserName" type="text" placeholder="考试代号">
                    </div>
                </div>
                <div style="text-align: center;">
                    <a class="btn btn-success" style="font-size: 16px;" onclick="submitCode();"><i class="fa fa-save"></i> 继续上次考试</a>
                </div>
            </form>
        </div>
        <div class="col-sm-12 search-collapse paper-examine" style="padding-bottom: 100px; width: 1100px; margin-left: calc(50% - 550px);">
            <span style="color:red;" id="spanCodeTage">你本次评测考核编码：<span style="font-size:18px;font-weight:bold;" id="spanUserCode"></span>（请记下此编码，如果中断考试，可凭此码继续。）</span>
            <div id="paperTitle" style="text-align: center; padding: 20px;"><h1></h1></div>
            <div id="questions" style="padding: 50px;">

            </div>
            <div style="text-align: center;" onclick="saveSubmit();">
                <a class="btn btn-success" style="font-size: 16px; width: 100px;"><i class="fa fa-save"></i> 提交</a>
            </div>
        </div>
        <div class="col-sm-12 search-collapse paper-finish" style="padding-bottom: 100px; width: 1100px; margin-left: calc(50% - 550px);display:none;">
            <div style="text-align: center;">
                <h1 style="color:red;">考试时间已结束</h1>
            </div>
        </div>
    </div>

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/yisha.min.css"))
    @*@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha-init.min.js"))*@
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.string.format/jquery.string.format.min.js"))

    <script type="text/javascript">

        var ExamineCode = ys.request("c");//试卷编码
        var PaperId = '';//试卷Id
        var PaperType = 0;//试卷类型
        var ExamineId = '';//考核Id
        var ExamineListId='';//试卷Id
        var ExamineDuration = 0;//考试时长
        var UseObjNature = 0;
        $(function () {
            loadExamineInfo();
        });

        //#region 获取试卷考核信息，和保存登记信息

        //加载试卷考核信息
        function loadExamineInfo() {
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Examine/GetPaper")' + '?c=' + ExamineCode,
                type: 'get',
                success: function (obj) {
                    if (obj.Data != undefined && obj.Tag > 0) {
                        PaperId = obj.Data.PaperId;
                        ExamineId = obj.Data.ExamineId;
                        ExamineDuration = obj.Data.Duration;
                        PaperType = obj.Data.PaperType;
                        UseObjNature = obj.Data.UseObjNature;
                        //添加试卷标题。
                        $("#paperTitle h1").html(obj.Data.PaperName);

                        if (obj.Tag == 3) {
                            ExamineListId = obj.Data.ExamineListId;
                            $("#spanUserName").text(obj.Data.UserName);
                            $("#spanMobile").text(obj.Data.Mobile);

                            $("#spanCodeTage").hide();
                            //加载试卷实体
                            loadPaperQuestionList(obj.Data.ExamineListId);
                        } else {
                            $(".paper-login").show();
                            $(".paper-examine").hide();

                            if (obj.Data.UseObjNature == 2) {

                                $(".useobj-teacher").hide();
                                $(".useobj-student").show();
                            } else {
                                $(".useobj-teacher").show();
                                $(".useobj-student").hide();
                            }
                        }
                    } else {
                        if (obj.Data.IsTimeFinish == 1) {
                            //考核时间已结束
                            setFinishClass();
                            ys.msgError(obj.Message);
                        } else {
                            ys.msgError(obj.Message);
                        }

                    }
                }
            });
        }

        function setFinishClass() {
            $(".paper-login,.paper-examine").hide();
            $(".paper-finish").show();
        }

        //保存登记信息
    function saveExamineRegister() {

            var studentno = $("#StudentNo").val();
            var organizationname = $("#OrganizationName").val();
            var realname = $("#RealName").val();
            var mobile = $("#Mobile").val();
            var gradeclassname = $("#GradeClassName").val();

            /* 增加验证*/
        var msg = '';
            if (UseObjNature == 1) {
                if (!(organizationname != undefined && organizationname.length > 0)) {
                    msg += '请输入单位名称！<br/>';
                }
            }

            if (!(realname != undefined && realname.length > 0)) {
                msg += '请输入姓名！<br/>';
            }
            if (!(mobile != undefined && mobile.length > 0)) {
                msg += '请输入手机号！<br/>';
            } else{
                var length = mobile.length;
                var phone = /^1\d{10}$/;
                if(!(length == 11 && phone.test(mobile))){
                    msg += '请输入正确的11位手机号！<br/>';
                }
            }
            if (UseObjNature == 2) {
                if (!(studentno != undefined && studentno.length > 0)) {
                    msg += '请输入学号！<br/>';
                }
                if (!(gradeclassname != undefined && gradeclassname.length > 0)) {
                    msg += '请输入年级班级名称！<br/>';
                }
            }
            if (msg != '') {
                ys.msgError("验证失败<br/>" + msg);
                return;
            }

            var postData = {
                ExamineCode: ExamineCode,
                PaperId: PaperId,
                StudentNo: studentno,
                OrganizationName: organizationname,
                RealName: realname,
                Mobile: mobile,
                GradeClassName: gradeclassname,
                UseObjNature: UseObjNature
            };
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Examine/SaveLoginExamine")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        //提示考试成功，显示考试时间
                        //获取考核试卷试题
                        $("#spanUserName").text(realname);
                        $("#spanMobile").text(mobile);
                        ExamineListId = obj.Data;
                        loadPaperQuestionList(obj.Data);
                        var tagMessage = '登记成功，开始评测考核</br>本次评测考核为' + ExamineDuration + "(分钟)";
                        layer.open({
                            content: tagMessage
                            , btn: ['关闭']
                            , yes: function (index, layero) {
                                layer.closeAll();
                            }
                            , cancel: function () {
                                layer.closeAll();
                            }
                        });
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }

        function saveSubmit() {
            var postData = {
                Id: ExamineListId
            };
            ys.confirm('确认要提交吗？<br/>提交后将禁止修改和再次提交。', function () {
                ys.ajax({
                    url: '@Url.Content("~/PaperExamineManage/Examine/SubmitPaper")',
                    type: 'post',
                    data: postData,
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            layer.open({
                                content: obj.Message
                                , btn: ['关闭']
                                , yes: function (index, layero) {
                                    layer.closeAll();
                                }
                                , cancel: function () {
                                    layer.closeAll();
                                }
                            });
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
        //#endregion
        //加载试卷题目清单详情
        function loadPaperQuestionList(id) {
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Examine/GeExamineJson")' + '?Id=' + id,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (obj.Data != undefined) {
                            UseObjNature = obj.Data.UseObjNature;
                            if (obj.Data.UseObjNature == 2) {
                                $(".useobj-teacher").hide();
                                $(".useobj-student").show();
                            } else {
                                $(".useobj-teacher").show();
                                $(".useobj-student").hide();
                            }
                            $(".paper-login").hide();
                            $(".paper-examine").show();

                            //修改编码

                            $("#spanUserCode").text(obj.Data.ExamineUserCode);

                            //添加试卷标题。
                            $("#paperTitle h1").html(obj.Data.PaperName);

                            if (obj.Data.QuestionList != undefined && obj.Data.QuestionList.length > 0) {
                                $.each(obj.Data.QuestionList, function (index, item) {
                                    loadPreviewQuestion(index + 1, item);
                                });
                            }

                            //绑定保存事件
                            $(".question-option").change(function () {
                                var answers = [];
                                var questionid = $(this).attr("questionid");
                                var optionid = $(this).attr('optionid');
                                var nature = $(this).attr('nature');
                                if (nature == 1) {
                                    answers.push(optionid);
                                } else {
                                    var questionindex = $(this).attr('questionindex');
                                    $.each($(".option-checkbox-" + questionindex + " .icheckbox-blue"), function (index, item) {
                                        var thisclass = $(this).attr("class");
                                        if (thisclass != undefined && thisclass.indexOf('checked') > -1) {
                                            var thisoptionid = $(this).children("input").attr("optionid");
                                            answers.push(thisoptionid);
                                        }
                                    });
                                }
                               // saveExamineQuestion(questid, optionid, questionindex, nature);
                                var postData = { Id: questionid, ExamineListId: ExamineListId, Answers: answers.join(",") };
                                $.ajax({
                                    url: '@Url.Content("~/PaperExamineManage/Examine/SaveQuestionAnswers")',
                                    type: 'post',
                                    data: postData,
                                    success: function (obj) {
                                        if (obj.Tag == 1) {
                                            if (typeof (obj.Data) == "string") {
                                                obj.Data = JSON.parse(obj.Data);
                                            }
                                            //显示考试题目信息
                                        }
                                        else {
                                            if (obj.Data != undefined && obj.Data.IsTimeFinish == 1) {
                                                setFinishClass();
                                            }
                                            layer.open({
                                                content: obj.Message
                                                , btn: ['关闭']
                                                , yes: function (index, layero) {
                                                    //按钮【按钮一】的回调
                                                    layer.closeAll();
                                                }
                                                , cancel: function () {
                                                    //右上角关闭回调
                                                    layer.closeAll();
                                                    //return false 开启该代码可禁止点击该按钮关闭
                                                }
                                            });
                                        }
                                    }
                                });
                            });
                        }
                    } else {
                        //考核时间已结束
                        if (obj.Data.IsTimeFinish == 1) {
                            setFinishClass();
                        }
                        ys.msgError(obj.Message);
                    }
                }
            });
        }

        //#region 预览效果

        function loadPreviewQuestion(number, jsondata) {
            var html = '';
            var checkboxTag = '';
            if (jsondata.QuestionTypeNature == 2) {
                checkboxTag = '【多选】';
            }
            html += '<div style="line-height: 20px; letter-spacing: 2px; padding: 10px;">';
            html += $.Format(' <span style="font-size:15px;text-wrap:normal;font-weight:bold;">{0}、{1} {2}</span><br />', number, jsondata.Title, checkboxTag);
            html += $.Format('<div id="title-Picture">{0}</div>', getQuestionPicture(jsondata.PicturePathList));
            html += $.Format('<div style="padding:10px 5px;">{0}</div>', getOptions(number, jsondata.QuestionOptionList, jsondata.QuestionTypeNature, jsondata.Id));
            html += '</div>';
            $("#questions").append(html);
        }

        function getQuestionPicture(jsondata) {
            var html = '';
            if (jsondata != undefined && jsondata.length > 0) {
            for (var i = 0; i < jsondata.length; i++) {
                var thisPath = jsondata[i];
                if (thisPath != undefined && thisPath.length > 0) {
                    html += $.Format('<div class="preview-picture" style="display: inline-block;"><img src="{0}" /></div>', thisPath);
                }
                }
            }
            return html;
        }

        function getOptions(number,jsondata, nature,questionid) {
            var html = '';
            //加载选项
            if (jsondata != undefined && jsondata.length > 0) {
                for (var i = 0; i < jsondata.length; i++) {
                    var optionTag = getOptionTag(i);
                    var optionmsg = jsondata[i].Name;
                    if (!(optionmsg != undefined && optionmsg.length > 0)) {
                        optionmsg = '';
                    }
                    html += '<div class="preOption" style="padding-left:25px;padding-bottom: 10px;">';
                    var checkedstr = '';
                    if (nature == 1) {
                        if (jsondata[i].IsSelected == 1) {
                            checkedstr = ' checked = "checked" ';
                        }
                        html += '<label class="radio-box" style="padding-left:0px;">';
                        html += $.Format('<input type="radio" name="previewradiobox_{3}" class="question-option" questionid="{0}" optionid="{1}" nature="{2}" {4}>', questionid, jsondata[i].Id, nature, number, checkedstr);
                        html += $.Format('&nbsp;&nbsp;{0}：{1}</label>', optionTag, optionmsg);
                        html += '</label>';
                    } else {
                        if (jsondata[i].IsSelected == 1) {
                            checkedstr = ' checked  ';
                        }
                        html += $.Format('<label class="check-box option-checkbox-{0}" >', number);
                        html += $.Format('<div class="icheckbox-blue {4}" onclick="previewcheckbox(this);" "><input name="previewcheckbox" class="question-option" type="checkbox" style="position: absolute; opacity: 0;" questionid="{0}" optionid="{1}" nature="{2}" questionindex="{3}"></div>', questionid, jsondata[i].Id, nature, number, checkedstr);
                        html += $.Format('{0}：{1}</label>', optionTag, optionmsg);
                    }
                    if (jsondata[i].PicturePath != undefined && jsondata[i].PicturePath.length > 0) {
                        html += $.Format('<div class="preview-picture"><img src="{0}"/></div>', jsondata[i].PicturePath);
                    }
                    if (jsondata[i].NameDesc != undefined && jsondata[i].NameDesc.length > 0) {
                        html += $.Format('<div class="preview-namedesc">（注：{0}）</div>', jsondata[i].NameDesc);
                    }
                    html += '</div>';
                }
            }
            return html;
        }

        //获取字母
        function getOptionTag(index) {
            var html = '';
            var strArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            if (index > 25) {
                var thisIndex = index % 25;
                var thisNum = index / 25;
                html = (strArr[thisIndex] + '' + thisNum);
            } else {
                html = strArr[index];
            }
            return html;
        }

        function previewcheckbox(obj, questionid, examinelistid, thisindex, questionnature) {
            var classstr = $(obj).attr('class');
            if (classstr != undefined && classstr.length > 0) {
                if (classstr.indexOf('checked') > -1) {
                    $(obj).removeClass("checked");
                } else {
                    $(obj).addClass('checked');
                }
            }
        }

        //#endregion

        function submitCode() {
            var examineUserCode = $("#ExamineUserCode").val();
            if (!(examineUserCode != undefined && examineUserCode.length > 0)) {
                ys.msgError("请填写考核编码<br/>");
                return false;
            } else {
                if (examineUserCode.length!=4) {
                    ys.msgError("请正确填写考核编码<br/>");
                    return false;
                }
            }
            var postData = { ExamineUserCode: examineUserCode, Id: ExamineId };
            ys.ajax({
                url: '@Url.Content("~/PaperExamineManage/Examine/GetExamineListJson")',
                type: 'Post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (typeof (obj.Data) == "string") {
                            obj.Data = JSON.parse(obj.Data);
                        }
                        //显示考试题目信息
                        ExamineListId = obj.Data.ExamineListId;
                        $("#spanUserName").text(obj.Data.UserName);
                        $("#spanMobile").text(obj.Data.Mobile);
                        loadPaperQuestionList(obj.Data.ExamineListId);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    </script>
</body>
</html>

