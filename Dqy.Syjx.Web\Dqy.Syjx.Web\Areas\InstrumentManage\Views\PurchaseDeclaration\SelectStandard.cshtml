﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    #wrap {
        width: 600px;
        margin: 100px auto 0;
        overflow: hidden;
    }

    #tit {
        display: flex;
        justify-content: space-around;
        height: 30px;
        width: 600px;
    }

        #tit span {
            float: left;
            /*display:block;*/
            height: 30px;
            line-height: 30px;
            width: 200px;
            font-size: 20px;
            text-align: center;
            color: #ccc;
            background-color: green;
        }

    #con div {
        height: 200px;
        width: 600px;
        background: pink;
        font-size: 100px;
        line-height: 200px;
        text-align: center;
    }

    .hide {
        display: none;
    }

    .select {
        /*display:block;*/
        background: red;
        color: #ccc;
    }

    .show {
        display: block;
    }

    .layui-layer-iframe {
        width: 800px;
        left: auto;
    }

    @@media screen and (max-width: 1100px) {
        .layui-layer-iframe {
            overflow-y: visible;
            -webkit-overflow-scrolling: touch;
        }
    }



    #concatenation {
        /*overflow: hidden;*/
        position: absolute;
        top: 0;
        left: 0;
        z-index: 999999;
        width: 100%;
        height: 100%;
        margin: 0 auto;
        padding: 10px 20px;
        overflow-y: auto;
        background-color: #ffffff;
    }

    #oneConcatenation {
        display: flex;
        justify-content: space-around;
        height: 40px;
        line-height: 40px;
        background-color: #3C8DBC;
    }

    #oneConcatenation .oneConTitle {
        width: 100%;
        font-size: 14px;
        text-align: center;
        color: #ffffff;
        /*padding: 0 15px;*/
        border-left: 1px solid #ffffff;
    }

    #oneConcatenation .oneConTitle:nth-child(1) {
        border-left: none;
    }


    .oneConTitle:hover {
        background-color: #386d8b;
    }

    .hoverClass {
        background-color: #386d8b;
    }

    #oneContent span:hover {
        color: blue;
    }

    .oneContentBor {
        display: flex;
        margin-top: 10px;
    }

    #oneContent-left {
        background-color: #3C8DBC;
        width: 200px;
    }

    #oneContent-left ul li {
        padding: 10px 10px;
        color: #ffffff;
        font-size: 14px;
        cursor: pointer;
    }

    #oneContent-left ul li:hover {
        background-color: #386d8b;
    }

    #oneContent-right {
        width: 100%;
    }

    #oneContent-right div {
        padding: 0 10px;
        display: flex;
        flex-wrap: wrap;
    }

    #oneContent-right div span {
        font-size: 12px;
        padding: 5px 8px;
        cursor: pointer;
    }

    .hide {
        display: none;
    }

    .show {
        display: block;
    }
</style>

<div class="container-div ui-layout-center">

    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <input type="hidden" id="departmentId" col="DepartmentId">
            <div class="select-list">
                <ul> 
                    <li>
                        <input type="hidden" id="Pid" col="Pid" value="" />
                        @*<a id="btnCancle" class="btn btn-secondary btn-sm" onclick="oneGrid()"><i class="fa fa-refresh"></i>一级菜单</a>*@
                        <input id="ClassName" col="ClassName" type="text" onclick="oneGrid()" placeholder="点击根据分类查询" style="display:inline-block;width:160px;" readonly="readonly" />
                    </li>
                    <li>
                        <input id="code" col="Code" type="text" placeholder="编号" style="display: inline-block;width:160px;" />
                    </li>
                    <li>
                        <input id="name" col="Name" type="text" placeholder="名称" style="display: inline-block;width:160px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>

        @* <div id="toolbar" class="btn-group d-flex" role="group">
        <a id="btnDelete" class="btn btn-primary disabled" onclick="batchEnter()"><i class="fa fa-ok"></i> 批量选入</a>
        </div>*@

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>

    <!--弹出内容-->
    <div id="concatenation" class="hide">
        <div id="oneConcatenation">
        </div>
        <div id="oneContent">
        </div>
    </div>
</div>

<script type="text/javascript">


    var firstOrderData = []
    var secondLevelData = []
    var threeLevelData = []
    var twoArrData = []
    var threeArrData = []

    $(function () {
        initGrid();
        $("#concatenation").hide();
        loadInstrumentStandardData();
    });



    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/InstrumentStandard/GetInstrumentModelListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                { checkbox: true, visible: true },
                { field: 'Code', title: '编码', width: commonWidth.Instrument.UnitName, sortable: true, halign: 'center', align: 'center' },
                { field: 'Name', title: '物品名称', width: commonWidth.Instrument.Name, halign: 'center', align: 'left', sortable: true, },
                {
                    field: 'Model', title: '规格型号', width: commonWidth.Instrument.Model, halign: 'center', align: 'left', sortable: true,
                    formatter: function (value, row, index) {
                        var html = value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${value}'>${value}</span>`;
                        return html;
                    }
                },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', width: commonWidth.Instrument.UnitName, sortable: true },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $("#searchDiv").getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function clearGrid() {
        $("#Pid").val("");
        $("#ClassName").val("");
        $("#code").val("");
        $("#name").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function oneGrid() {
        $("#concatenation").addClass('show');
    }


    function saveForm(index) {
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        var oldDataLength = 0;
        if (selectedRow.length > 0) {
            var standardLength = parent.course_info.data.length;
            oldDataLength = standardLength;
            if (standardLength == 1) {
                var obj = parent.course_info.data[0];
                if (obj.OriginalCode == "" && obj.Name == "" && obj.UnitName == "" && obj.Num == "" && obj.Price == "") {
                    parent.$.observable(parent.course_info.data).remove(0);
                    oldDataLength = 0;
                }
            }
            var leiJi = parseInt(oldDataLength) + parseInt(selectedRow.length);
            if (leiJi > 200) {
                layer.alert("为了方便您查阅请确保每次提交数据不能超出200条数据！您当前累计填报计划条目数量为：" + leiJi + "条数据！", {
                    icon: 2,
                    skin: 'layer-ext-moon'
                });
                return;
            }

            $(selectedRow).each(function (i, v) {
                parent.$.observable(parent.course_info.data).insert(parent.course_info.data.length + 1, { Id: 0, EntryType: 2, ModelCode: '', OriginalCode: v.Code, Model: v.Model, InstrumentStandardId: v.InstrumentStandardId, ModelStandardId: v.ModelStandardId, Name: v.Name, UnitName: v.UnitName, Num: '', Price: '', Reason: '', StageId: parent.defaultStageId, CourseId: parent.defaultCourseId, Stage: parent.defaultStage, Course: parent.defaultCourse });
            });
            ys.msgSuccess("添加成功");
            parent.layer.close(index);
        } else {
            ys.msgError('请至少选择一项保存!')
        }
    }


    function loadInstrumentStandardData() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/InstrumentStandard/GetTabSelectList")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var objData = obj.Data;
                    $(obj.Data).each(function (i, v) {
                        if (v.Depth == 0) {
                            firstOrderData.push(v);
                        } else if (v.Depth == 1) {
                            secondLevelData.push(v);
                        } else if (v.Depth == 2) {
                            threeLevelData.push(v);
                        }
                    });

                    oneConFun();
                }
            }
        });
    }


    function oneConFun() {
        //console.log("firstOrderData", firstOrderData)
        //console.log("secondLevelData", secondLevelData)
        //console.log("threeLevelData", threeLevelData)

        //一级导航栏
        var html = '';
        $.each(firstOrderData, function (i, v) {
            html += '<div id="oneConTitle" class="oneConTitle">' + v.Name + '</div>';
        });

        $('#oneConcatenation').html(html);
        //二三级内容区域
        var htmlLR = '';
        htmlLR += '<div class="oneContentAll">';
        htmlLR += '<div class="oneContentBor">';
        htmlLR += '<div class="oneContent-left" id="oneContent-left"></div>';
        htmlLR += '<div class="oneContent-right" id="oneContent-right"></div>';
        htmlLR += '</div>';
        htmlLR += '</div>';
        //});
        $('#oneContent').html(htmlLR);

        //左侧二级
        var htmlLeft = '';
        htmlLeft += '<div class="oneContentArea">';
        htmlLeft += '<ul class="twoConcatenation">';
        htmlLeft += '</ul>';
        htmlLeft += '</div>';
        //});
        $('#oneContent-left').html(htmlLeft);

        //右侧三级
        var htmlRight = '';
        htmlRight += '<div >';
        htmlRight += '</div>';
        $('#oneContent-right').html(htmlRight);

        //初始二级 三级 列表
        twoArrData = secondLevelData.filter((item, index) => {
            return item.Pid == firstOrderData[0].Id
        })
        threeArrData = threeLevelData.filter((item, index) => {
            return item.Pid == twoArrData[0].Id
        })

        //二级列表渲染
        var twoHtmlLift = $("#oneContent-left").children("div").children("ul")
        var htmlTwo = '';
        $.each(twoArrData, function (i, v) {
            htmlTwo += '<li class="twoConTitle">' + v.Name + '</li>';
        });
        twoHtmlLift.html(htmlTwo);

        //三级内容渲染
        var threeHtmlRight = $("#oneContent-right").children("div")
        var htmlThr = '';
        $.each(threeArrData, function (i, v) {
            htmlThr += '<span class="">' + v.Name + '</span>';
        });
        threeHtmlRight.html(htmlThr)

        //类名修改样式
        $('.oneContentAll').eq(0).addClass('show').removeClass('hide')
        $('.oneConTitle').eq(0).addClass('hoverClass')
        $('#oneContent li').eq(0).addClass('hoverClass')
    }

    $(document).ready(function () {
        //一级鼠标点击click事件
        $('#oneConcatenation').on("click", '.oneConTitle', function () {
            var i = $(this).index();
            var name = $(this).text();
            var id = firstOrderData[i].Id;
            //$("#concatenation").hide();
            //console.log("一级Id：" + id);
            $("#concatenation").addClass('hide').removeClass('show');
            $("#Pid").val(id);
            $("#ClassName").val(name);
            searchGrid();
        })

        //一级鼠标mouseover事件
        $('#oneConcatenation').on("mouseover", '.oneConTitle', function () {
            let i = $(this).index();//下标
            twoArrData = secondLevelData.filter((item, index) => {
                return item.Pid == firstOrderData[i].Id
            })
            threeArrData = threeLevelData.filter((item, index) => {
                return item.Pid == twoArrData[0].Id
            })
            var htmlLR = '';
            htmlLR += '<div class="oneContentAll">';
            htmlLR += '<div class="oneContentBor">';
            htmlLR += '<div class="oneContent-left" id="oneContent-left"></div>';
            htmlLR += '<div class="oneContent-right" id="oneContent-right"></div>';
            htmlLR += '</div>';
            htmlLR += '</div>';
            $('#oneContent').html(htmlLR);

            var htmlLeft = '';
            htmlLeft += '<div class="oneContentArea">';
            htmlLeft += '<ul class="twoConcatenation">';
            htmlLeft += '</ul>';
            htmlLeft += '</div>';
            $('#oneContent-left').html(htmlLeft);

            var htmlRight = '';
            htmlRight += '<div >';
            htmlRight += '</div>';
            $('#oneContent-right').html(htmlRight);

            //选择一级时展示二级
            var twoHtmlLift = $(".oneContent-left").children("div").children("ul")
            var htmlTwo = '';
            $.each(twoArrData, function (i, v) {
                htmlTwo += '<li class="twoConTitle">' + v.Name + '</li>';
            });
            twoHtmlLift.html(htmlTwo);

            //选择一级时默认三级
            var threeHtmlRight = $(".oneContent-right").children("div")
            var htmlThr = '';
            $.each(threeArrData, function (i, v) {
                htmlThr += '<span class="">' + v.Name + '</span>';
            });
            threeHtmlRight.html(htmlThr)

            //类名修改样式
            $('.oneContentAll').addClass('show').removeClass('hide').siblings().addClass('hide').removeClass('show');
            $('.oneConTitle').eq(i).addClass('hoverClass').siblings().removeClass('hoverClass')
            $('#oneContent li').eq(0).addClass('hoverClass')
        });

        //二级click事件
        $("#oneContent").off("click", 'li').on("click", 'li', function () {
            //console.log("二级111", $(this).text())
            var i = $(this).index();
            var name = $(this).text();
            var id = twoArrData[i].Id;
            //console.log("二级Id：" + id);
            $("#Pid").val(id);
            $("#ClassName").val(name);
            $("#concatenation").addClass('hide').removeClass('show');
            searchGrid();
        })
        //二级mouseover事件
        $("#oneContent").off("mouseover", 'li').on("mouseover", 'li', function () {
            var idx = $(this).index();//下标
            threeArrData = threeLevelData.filter((item, index) => {
                return item.Pid == twoArrData[idx].Id
            })
            $('#oneContent li').eq(idx).addClass('hoverClass').siblings().removeClass('hoverClass')

            var htmlRight = '';
            htmlRight += '<div >';
            htmlRight += '</div>';
            $('#oneContent-right').html(htmlRight);

            //三级数据
            var threeHtmlRight = $("#oneContent-right").children("div")
            var htmlThr = '';
            $.each(threeArrData, function (i, v) {
                htmlThr += '<span class="">' + v.Name + '</span>';
            });
            threeHtmlRight.html(htmlThr);
        })

        //三级点击事件
        $('#oneContent').off("click", 'span').on("click", 'span', function () {
            //console.log("三级", $(this).text())

            var i = $(this).index();
            var name = $(this).text();
            var id = threeArrData[i].Id;
            //console.log("三级Id：" + id);
            $("#Pid").val(id);
            $("#ClassName").val(name);
            $("#concatenation").addClass('hide').removeClass('show');
            searchGrid();

        })
    })
</script>
