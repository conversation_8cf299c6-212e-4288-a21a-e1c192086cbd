﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container { width: 100% !important;}
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolStage" col="SchoolStage" style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1005" col="DictionaryId1005" style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <input id="VersionName" col="VersionName" type="text" style="display:inline-block;width:160px;" placeholder="版本名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a id="btnSearch" class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 添加</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>


            <a id="btnUpdate" class="btn btn-primary" onclick="showEdit()"><i class="fa fa-refresh"></i>刷新数据</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>

            <div style="margin:auto;width:800px;margin-top:20px; ">
                <span style="color:red;">新增、修改“加权值设置”数据后需要选中新增或修改的条目点击“刷新数据”；若操作后未点击刷新数据，可能会导致达标数据不准确！</span>

            </div>
        </div>

        
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSchoolStage();
        loadSubject(0);
        initGrid();
        
        
    });
    function loadSchoolStage() {
        $('#SchoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                //var selectid = $('#SchoolStage').ysComboBox('getValue');
                //loadSubject(selectid);
            }
        });
    }
    function loadSubject(selectid) {
        $("#DictionaryId1005").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                //var selectid = $('#DictionaryId1005').ysComboBox('getValue');
                //loadClassA(selectid);
            }

        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/EvaluateManage/InstrumentEvaluateWeighted/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                {
                    title: '操作', width: 140, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { checkbox: true, visible: true },
                { field: 'VersionName', title: '版本名称', sortable: true, halign: 'center', valign: 'middle', },
                {
                    field: 'SchoolStage', title: '学段', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SchoolStageName;
                    }
                },
                {
                    field: 'DictionaryId1005', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SubjectName;
                    }
                },
                { field: 'RailStart', title: '轨数起', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center', },
                { field: 'RailEnd', title: '轨数止', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'WeightedRatio', title: '加权系数', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'right'},
                { field: 'Remark', title: '备注' },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#SchoolStage").ysComboBox('setValue', -1);
        $("#DictionaryId1005").ysComboBox('setValue', -1);
        $("#VersionName").val("");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/EvaluateManage/InstrumentEvaluateWeighted/Form")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    function deleteForm(id) {
        var ids = '';
        var tagMsg = '';
        if (id > 0) {
            ids = id;
            tagMsg = '确定要删除当前数据吗？';
        } else {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (selectedRow.length == 0) {
                ys.msgError('请选择需要删除的数据!');
                return '';
            }
            ids = ys.getIds(selectedRow);
            tagMsg = '确认要删除选中的' + selectedRow.length + '条数据吗？';
        }
        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateWeighted/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }


    function showEdit(){
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length == 0) {
            ys.msgError('请选择需要更新的版本名称!');
            return '';
        }
        var ids = ys.getIds(selectedRow);
        ys.confirm("请确认选择的版本名称有变动，有变动才需要更新；因为点击此按钮可能会耗时很长，请勿在执行过程中关闭此页面！请再三确认无误后在点击！", function () {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/InstrumentAttendStatic/BatchEvaluateWeightedAttendStatic")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

  
</script> 