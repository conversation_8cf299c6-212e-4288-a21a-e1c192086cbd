﻿
@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }

    .modelShow{
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div ui-layout-center">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="instrumentEvaluateProjectId" col="InstrumentEvaluateProjectId" style="display:inline-block;width:250px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="width:150px" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>
       @* <div class="btn-group d-flex" role="group" id="toolbar">
        </div>*@
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function () {
        loadInstrumentEvaluateProjectVersionId();
        getCourse();
    });

    function initGrid() {
        if (!$('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetInstrumentStandardListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'CourseId asc',
            columns: [
                { checkbox: true, visible: true },
                //{ field: 'StageName', title: '学段', halign: 'center', align: 'center', sortable: true, width: 50 },
                { field: 'CourseName', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course },
                { field: 'InstrumentCode', title: '仪器代码', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Opt2 },
                { field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name },
                { 
                    field: 'Model', title: '规格型号', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${value}'>${value}</span>`;
                        return html;
                    }
                },
                //{ field: 'StandardNum', title: '指标量', halign: 'center', align: 'center', sortable: true, width: 50 },
                //{ field: 'StockNum', title: '存量', halign: 'center', align: 'center', sortable: true, width: 50 },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                //{
                //    field: 'AllocateType', title: '配备要求', halign: 'center', align: 'center', sortable: true, width: 50,
                //    formatter: function (value, row, index) {
                //        return value == 1 ? '必配' : '选配';
                //    }
                //},
                {
                    field: 'DifferenceNum', title: '差额', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (value != null && value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                },
                //{ field: 'InputNum', title: '已填报量', halign: 'center', align: 'center', sortable: true, width: 50 },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess:function(){
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });

       
    }

    function loadInstrumentEvaluateProjectVersionId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectByUser")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#instrumentEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        $('#instrumentEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    initGrid();
                }
                else {
                    ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.SchoolManager.ParseToInt());
                }
            }
        });
    }

    //function getCourse() {
    //    $('#courseId').ysComboBox({
    //        url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
    //        key: 'DictionaryId',
    //        value: 'DicName',
    //        dataName: 'Data',
    //        defaultName: '适用学科'
    //    });
    //}

    function getCourse() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/UserSchoolStageSubject/GetSubjectByUser")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#courseId').ysComboBox({
                        data: obj.Data,
                        key: 'DictionaryId',
                        value: 'DicName',
                        class: "form-control",
                        placeholder: '请选择学科'
                    });
                     if (obj.Data.length == 1) {
                        $('#courseId').ysComboBox('setValue', obj.Data[0].DictionaryId);
                    }
                }
            }
        });
    }


    function searchGrid() {
        if ($('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }

    function resetGrid() {
        //清空条件
        $('#courseId').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function saveForm(index) {
        var selectedRow = $("#gridTable").bootstrapTable("getSelections");
        var oldDataLength = 0;
        if (selectedRow.length > 0) {

            var standardLength = parent.course_info.data.length;
            oldDataLength = standardLength;
            if (standardLength == 1) {
                var obj = parent.course_info.data[0];
                //console.log("obj:" + JSON.stringify(obj));
                if (obj.OriginalCode == "" && obj.Name == "" && obj.UnitName == "" && obj.Num == "" && obj.Price == "") {
                    parent.$.observable(parent.course_info.data).remove(0);
                    oldDataLength = 0;
                }
            }
            var leiJi = parseInt(oldDataLength) + parseInt(selectedRow.length);
            if (leiJi > 200) {
                layer.alert("为了方便您查阅请确保每次提交数据不能超出200条数据！您当前累计填报计划条目数量为：" + leiJi + "条数据！", {
                    icon: 2,
                    skin: 'layer-ext-moon'
                });
                return;
            }
            $(selectedRow).each(function (i, v) {
                //console.log("v:"+JSON.stringify(v));
                parent.$.observable(parent.course_info.data).insert(parent.course_info.data.length + 1, { Id: 0, EntryType: 3, OriginalCode: v.InstrumentStandardCode, ModelCode: v.InstrumentCode, Model: v.Model, InstrumentStandardId: 0, ModelStandardId: 0, Name: v.Name, UnitName: v.UnitName, Num: Math.abs(v.DifferenceNum), Price: '', Reason: '', StageId: v.StageId, CourseId: v.CourseId, Stage: v.StageName, Course: v.CourseName });
            });
            ys.msgSuccess("添加成功");
            parent.layer.close(index);
        }
    }

</script>
