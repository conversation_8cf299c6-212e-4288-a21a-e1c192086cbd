﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .span-left-tag {
        margin-left: 82px;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="GradeId" col="GradeId" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="ClassInfoId" col="ClassInfoId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <input id="Name" col="Name" placeholder="姓名" style="display: inline-block;width:180px;" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnCheck" class="btn btn-success" onclick="showEditForm(0,2)"><i class="fa fa-plus"></i> 批量考勤</a>
            <a id="btnEvaluate" class="btn btn-success" onclick="showEditForm(0,3)"><i class="fa fa-plus"></i> 批量评价</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var id = ys.request("id"); 
    var experimentid = ys.request("experimentid");
    var Com_IsLook = ys.request("v");//1：只查看不操作。
    $(function () {
        if (Com_IsLook == 1) {
            $("#toolbar").hide();
        }
        loadComboBoxInit();
        initGrid();
    });
    function loadComboBoxInit() {
        loadGrade();
        $('#ClassInfoId').ysComboBox({
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '班级'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                {
                    title: '操作', width: 100, halign: 'center', valign: 'middle', align: 'center', visible: (Com_IsLook != 1),
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showEditForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        return actions.join('');
                    }
                },
                { checkbox: true, visible: true },
                { field: 'index', title: '序号', width: 50, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) { return index + 1; } },
                { field: 'StudentName', title: '姓名', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'IDCard6', title: '学籍号', sortable: true, width: 160, halign: 'center', align: 'center' },
                { field: 'GradeName', title: '年级', sortable: true, width: 90, halign: 'center', valign: 'middle' },
                { field: 'ClassName', title: '班级', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center' },
                {
                    field: 'AttendanceStatuz', title: '学生考勤', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (row.AttendanceStatuz > 0) {
                                if (@AttendanceStatuzEnum.Yes.ParseToInt()== value) {
                                    html = '@AttendanceStatuzEnum.Yes.GetDescription()';
                                } else {
                                    html = '@AttendanceStatuzEnum.No.GetDescription()';
                                }
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'Score', title: '等级评价', sortable: true, width: 90, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (row.Score > 0) {
                                html = getScoreName(row.Score);
                            }
                        }
                        return html;
                    }
                },
                { field: 'Remark', title: '综合评价', sortable: true, width: 200, halign: 'center', valign: 'middle', align: 'left' },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.ExperimentPublishId = id;
                queryString.ExperimentId = experimentid
                return queryString;
            }
        });
    }

    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetSchoolGradeListByPidJson")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级',
        });
    }

    function getScoreName(score) {
        var html = '--';
        switch (score) {
            case 1:
                html = '一星';
                break;
            case 2:
                html = '二星';
                break;
            case 3:
                html = '三星';
                break;
            case 4:
                html = '四星';
                break;
            case 5:
                html = '五星';
                break;
        }
        return html;
    }
    function searchGrid() {
        $('#gridTable').ysTable('search');
    }
    function resetGrid() {
        $("#GradeId").ysComboBox('setValue', -1);
        $("#ClassInfoId").ysComboBox('setValue', -1);
        $("#Name").val('');
        $('#gridTable').ysTable('search');
    }
    /***  opttype：（1：修改  2：批量考勤  3：批量评价）
     * 弹出窗体修挂。
     */
    function showEditForm(id, opttype) {
        var ids = ''
        var titleMsg = '修改';
        if (opttype == 2) {
            titleMsg = '批量考勤';
        } else if (opttype == 3) {
            titleMsg = '批量评价';
        }
        if (opttype == 2 || opttype == 3) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            console.log("+" + selectedRow.length);
            if (!ys.checkRowDelete(selectedRow)) {
                return;
            }
            else {
                ids = ys.getIds(selectedRow);
            }
        }
        ys.openDialog({
            title: titleMsg,
            content: '@Url.Content("~/ExperimentTeachManage/ExperimentCriticize/CriticizeForm")' + '?id=' + id + '&ids=' + ids + '&t=' + opttype,
            width: '768px',
            height: '300px;',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
