﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<link rel="stylesheet" href='@Url.Content("~/junfei/css/bookingeditform.css")'>

<div class="container-div">
    <div class="col-sm-12 select-table table-striped" style="min-width:900px;padding-top:20px;">
        <!--帮助文档需要内容,id值必须为“helpBtn”-->
        <sapn style="padding: 10px;"><i class="fa fa-question-circle" id="helpBtn"></i></sapn>
        <div class="row">
            <center style="margin:0 auto;">
                <table id="bookingTb" class="tbform font-song">
                    <tr class="trpading title-background">
                        <td colspan="4">
                            <font style="font-size:30px;font-weight:bold;">中小学幼儿园教育技术装备基本情况统计表</font> 
                        </td>
                    </tr>
                    <tr class="trpading">
                        <td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">
                            1、学校基本信息
                        </td>
                    </tr>
                    <tr class="trpading">
                        <td style="width:200px;" class="font-yahei">学校规模（轨）</td>
                        <td style="width:160px;" id="tdSchoolRail"></td>
                        <td style="width:200px;" class="font-yahei">班级数（个）</td>
                        <td style="width:160px;" id="tdClassNum"></td>
                    </tr>
                    <tr class="trpading">
                        <td class="font-yahei">教师数（人）</td>
                        <td id="tdTeacherNum"></td>
                        <td class="font-yahei">学生数（人）</td>
                        <td id="tdStudentNum"></td>
                    </tr>
                </table>
            </center>
        </div>

    </div>

</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var id = ys.request("id");
    var Query_FunroomDicIdList = [1006014, 1006016, 1006018, 1006021, 1006023, 1006025, 1006027, 1006030, 1006032, 1006037, 1006040];
    var Query_LabManagerList = [1006002, 1006004, 1006007, 1006009, 1006014, 1006018, 1006021, 1006023, 1006030, 1006032, 1006037, 1006040];
    $(function () { 
        if (!id) {
            id = 0;
        }
        loadInfoForm(); 
    });


    function loadInfoForm() {
        //BackupYear = 0 当年
        var paramData = { SchoolId: id, DicIds: Query_FunroomDicIdList.join(","), LabManagerDicIds: Query_LabManagerList.join(","), BackupYear:0 };
        ys.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetInfoDetailJson")',
            type: 'get',
            data: paramData,
            success: function (obj) {
                if (obj.Tag == 1) {  
                    $("#tdSchoolRail").text(obj.Data.SchoolRail);
                    $("#tdClassNum").text(obj.Data.ClassNum);
                    $("#tdTeacherNum").text(obj.Data.TeacherNum);
                    $("#tdStudentNum").text(obj.Data.StudentNum); 
                    //加载对应的项目
                    var html = '';
                    //2
                    html =loadEquimentAmount(obj.Data); 
                    if (html && html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }
                    //3
                    html = getloadExperimentRoomHtml(obj.Data);
                    if (html && html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }
                    //4
                    html = getloadBookHtml(obj.Data); 
                    if (html && html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }
                    //5
                    html = getloadMediaSysHtml(obj.Data);
                    if (html && html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }
                    //6
                    html = getloadFunroomHtml(obj.Data);
                    if (html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }

                    //7
                    html = getloadSportsHtml(obj.Data);
                    if (html && html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }

                    //8
                    html = getloadInnovateEquipmentHtml(obj.Data);
                    if (html && html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }

                    //9
                    html = getloadLabManagerHtml(obj.Data);
                    if (html && html != '' && html.length > 0) {
                        $("#bookingTb").append(html);
                    }
                } else {

                }
            }
        });
    }
    /**2、教育技术装备投入经费（万元） */
    function loadEquimentAmount(dataJson) {
        var html = '';
        if (dataJson != undefined) {
      
            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">2、教育技术装备投入经费（万元）</td></tr>';
            
            html += '<tr class="trpading">';
            html += '<td class="font-yahei">累计投入经费</td><td  colspan="3">' + dataJson.TotalAmount + '</td>';
            html += '</tr>';
           
            html += '<tr class="trpading">';
            html += '<td class="font-yahei">累计班均</td><td>' + dataJson.ClassAmountRate + '</td>';
            html += '<td class="font-yahei">累计生均额</td><td>' + dataJson.StudentAmountRate + '</td>';
            html += '</tr>';
            
            html += '<tr class="trpading">';
            html += '<td class="font-yahei">当年投入经费</td><td  colspan="3">' + dataJson.CurrentYearAmount + '</td>';
            html += '</tr>';
            
            html += '<tr class="trpading">';
            html += '<td class="font-yahei">当年班均额</td><td>' + dataJson.ClassAmountCurrentRate + '</td>';
            html += '<td class="font-yahei">当年生均额</td><td>' + dataJson.StudentAmountCurrentRate + '</td>';
            html += '</tr>';
        }
        return html;
    }

    /**3、实验室与图书馆 */
    function getloadExperimentRoomHtml(dataJson) {
        var html = '';
        if (dataJson) {
            if (dataJson.ExperimentFunroom && dataJson.ExperimentFunroom.length > 0) {
                html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">3、实验室</td></tr>';

                for (var i = 0; i < dataJson.ExperimentFunroom.length; i++) {
                    var experimentFunroom = dataJson.ExperimentFunroom[i];
                    if (experimentFunroom) {
                        var dicname = experimentFunroom.name;
                        if (dicname=="科学") {
                            dicname = "科学/探究";
                        } else {
                            dicname = dicname + "实验";
                        }
                        html += '<tr class="trpading">';
                        html += '<td class="font-yahei">' + dicname + '室数量（间）</td><td>' + experimentFunroom.num + '</td>';
                        html += '<td class="font-yahei">总面积（㎡）</td><td>' + experimentFunroom.area + '</td>';
                        html += '</tr>';
                    }
                }
            }
        }
        return html;
    }
    /**4、图书馆 */
    function getloadBookHtml(dataJson) {
        var html = '';
        if (dataJson != undefined) {
            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">4、图书馆</td></tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">藏书量（册）</td><td>' + dataJson.BookTotalNum + '</td>';
            html += '<td class="font-yahei">生均图书（册）</td><td>' + dataJson.StudentBookRate + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">当年新增图书（册）</td><td>' + dataJson.BookNewYearNum + '</td>';
            html += '<td class="font-yahei">当年生均新增图书（册）</td><td>' + dataJson.StudentBookNewRate + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">阅览室座位（个）</td><td>' + dataJson.BookRoomSeatNum + '</td>';
            html += '<td class="font-yahei">图书馆总面积（㎡）</td><td>' + dataJson.BookLibraryArea + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">图书管理员（人）</td><td>' + dataJson.BookAdminNum + '</td>';
            html += '<td class="font-yahei">其中专职（人）</td><td>' + dataJson.BookFulltimeNum + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">移动图书柜（个）</td><td>' + dataJson.BookCabinetNum + '</td>';
            html += '<td class="font-yahei">朗读亭（个）</td><td>' + dataJson.BookPavilionNum + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">'; 
            html += '<td class="font-yahei">供读者使用的终端（台）</td><td>' + dataJson.BookReadersTerminal + '</td>';
            html += '<td class="font-yahei"></td><td></td>';//空
            html += '</tr>';
        }
        return html;
    }

    /**5、信息技术 */
    function getloadMediaSysHtml(dataJson) {
        var html = '';
        if (dataJson != undefined) {
            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">5、信息技术</td></tr>';

            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding-left:10px;">（1）多媒体设备</td></tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">普通教室多媒体设备（套）</td><td>' + dataJson.MediaFunroomNum + '</td>';
            html += '<td class="font-yahei">其中交互式多媒体（套）</td><td>' + dataJson.MediaInteractiveNum + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">专用教室多媒体设备（套）</td><td>' + dataJson.MedialaboratoryNum + '</td>';
            html += '<td class="font-yahei">其中交互式多媒体（套）</td><td>' + dataJson.MediaInteractiveLabNum + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">其它场所多媒体设备（套）</td><td>' + dataJson.MediaOtherNum + '</td>';
            html += '<td class="font-yahei">其中交互式多媒体（套）</td><td>' + dataJson.MediaInteractiveOtherNum + '</td>';
            html += '</tr>';

            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding-left:10px;">（2）学生用计算机</td></tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">计算机数量(台)</td><td>' + dataJson.ComputerStudentNum + '</td>';
            html += '<td class="font-yahei">其中平板数量(台)</td><td>' + dataJson.ComputerPadNum + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">生机比</td><td>' + dataJson.StudentComputRate + '</td>';
            html += '<td class="font-yahei"></td><td></td>';
            html += '</tr>';

            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding-left:10px;">（3）教师用计算机</td></tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">计算机数量(台)</td><td>' + dataJson.ComputerTeacherNum + '</td>';
            html += '<td class="font-yahei">其中平板数量(台)</td><td>' + dataJson.ComputerTeacherNum + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">师机比</td><td>' + dataJson.TeacherComputRate + '</td>';
            html += '<td class="font-yahei"></td><td></td>';
            html += '</tr>';

            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding-left:10px;">（4）信息化系统</td></tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">有线网络系统（套）</td><td>' + dataJson.SysNetworkWired + '</td>';
            html += '<td class="font-yahei">无线网络系统（套）</td><td>' + dataJson.SysNetworkWireless + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">校园广播系统（套）</td><td>' + dataJson.SysCampusBroadcasting + '</td>';
            html += '<td class="font-yahei">标准化考场（套）</td><td>' + dataJson.SysStandardizedRoom + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">户外LED大屏系统（套）</td><td>' + dataJson.SysOutdoorsLEDBig + '</td>';
            html += '<td class="font-yahei">电子班牌系统（套）</td><td>' + dataJson.SysElectronicClassCard + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">安防监控系统（套）</td><td>' + dataJson.SysSecurityMonitor + '</td>';
            html += '<td class="font-yahei">校园访客系统  （套）</td><td>' + dataJson.SysCampusVisitor + '</td>';
            html += '</tr>';


            html += '<tr class="trpading">';
            html += '<td class="font-yahei">一卡通系统 （套）</td><td>' + dataJson.SysOneCard + '</td>';
            html += '<td class="font-yahei">物联网系统（套）</td><td>' + dataJson.SysLoT + '</td>';
            html += '</tr>';

            html += '<tr class="trpading">';
            html += '<td class="font-yahei">校园电视台 （套）</td><td>' + dataJson.SysCampusTV + '</td>';
            html += '<td class="font-yahei">录播系统（套）</td><td>' + dataJson.SysNormalizedBroadcast + '</td>';
            html += '</tr>';
        }
        return html;
    }

    /**6、专用教室 */
    function getloadFunroomHtml(dataJson) {
        var html = '';
        if (dataJson != undefined) {
            html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">6、专用教室</td></tr>';
            if (dataJson.DedicatedRoom) {
                var listDedicatedRoom = dataJson.DedicatedRoom;
                if (listDedicatedRoom.Dic_1006014) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">计算机教室（间）</td><td>' + listDedicatedRoom.Dic_1006014 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006014_area + '</td>';
                    html += '</tr>';
                }

                if (listDedicatedRoom.Dic_1006016) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">录播教室（间）</td><td>' + listDedicatedRoom.Dic_1006016 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006016_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006018) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">科技创新室（间）</td><td>' + listDedicatedRoom.Dic_1006018 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006018_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006021) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">音乐教室（间）</td><td>' + listDedicatedRoom.Dic_1006021 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006021_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006023) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">美术教室（间）</td><td>' + listDedicatedRoom.Dic_1006023 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006023_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006027) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">舞蹈教室（间）</td><td>' + listDedicatedRoom.Dic_1006027 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006027_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006025) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">书法教室（间）</td><td>' + listDedicatedRoom.Dic_1006025 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006025_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006030) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">劳技技术教室（间）</td><td>' + listDedicatedRoom.Dic_1006030 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006030_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006032) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">通用技术教室（间）</td><td>' + listDedicatedRoom.Dic_1006032 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006032_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006040) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">历史地理教室（间）</td><td>' + listDedicatedRoom.Dic_1006040 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006040_area + '</td>';
                    html += '</tr>';
                }
                if (listDedicatedRoom.Dic_1006037) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">心理健康教室（间）</td><td>' + listDedicatedRoom.Dic_1006037 + '</td>';
                    html += '<td class="font-yahei">总面积（㎡）</td><td>' + listDedicatedRoom.Dic_1006037_area + '</td>';
                    html += '</tr>';
                }
            }

            if (dataJson.SpecialRoomList != undefined && dataJson.SpecialRoomList.length > 0) {
                var youeryuanlist = dataJson.SpecialRoomList;
                html += '<tr class="trpading">';
                html += '<td class="font-yahei">幼儿园音乐类</td><td>' + youeryuanlist.Category_1 + '</td>';
                html += '<td class="font-yahei">总面积（㎡）</td><td>' + youeryuanlist.Category_1_area + '</td>';
                html += '</tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">幼儿园美工类</td><td>' + youeryuanlist.Category_2 + '</td>';
                html += '<td class="font-yahei">总面积（㎡）</td><td>' + youeryuanlist.Category_2_area + '</td>';
                html += '</tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">幼儿园科学类</td><td>' + youeryuanlist.Category_3 + '</td>';
                html += '<td class="font-yahei">总面积（㎡）</td><td>' + youeryuanlist.Category_3_area + '</td>';
                html += '</tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">幼儿园建构类</td><td>' + youeryuanlist.Category_4 + '</td>';
                html += '<td class="font-yahei">总面积（㎡）</td><td>' + youeryuanlist.Category_4_area + '</td>';
                html += '</tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">幼儿园体育类</td><td>' + youeryuanlist.Category_5 + '</td>';
                html += '<td class="font-yahei">总面积（㎡）</td><td>' + youeryuanlist.Category_5_area + '</td>';
                html += '</tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">幼儿园其它类</td><td>' + youeryuanlist.Category_6 + '</td>';
                html += '<td class="font-yahei">总面积（㎡）</td><td>' + youeryuanlist.Category_6_area + '</td>';
                html += '</tr>';
            }

        }
        return html;
    }
    /**7、体育场所 */
    function getloadSportsHtml(dataJson) {
        var html = '';
        if (dataJson != undefined) {
            if (dataJson.SportsPlace) {
                var sportsPlaceInfo = dataJson.SportsPlace
                html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">7、体育场所</td></tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">跑道长度（米）</td><td>' + sportsPlaceInfo.SportRunwayLength + '</td>';
                html += '<td class="font-yahei">足球场（个）</td><td>' + sportsPlaceInfo.SportFootballNum + '</td>';
                html += '</tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">篮球场（个）</td><td>' + sportsPlaceInfo.SportBasketballNum + '</td>';
                html += '<td class="font-yahei">排球场（个）</td><td>' + sportsPlaceInfo.SportVolleyballNum + '</td>';
                html += '</tr>';

                html += '<tr class="trpading">';
                html += '<td class="font-yahei">乒乓球桌（张）</td><td>' + sportsPlaceInfo.SportPingPongNum + '</td>';
                html += '<td class="font-yahei">室内运动场（个）</td><td>' + sportsPlaceInfo.SportRoomNum + '</td>';
                html += '</tr>';
            }
        }
        return html;
    }
    /**8、科技创新设备 */
    function getloadInnovateEquipmentHtml(dataJson) {
        var html = '';
        if (dataJson != undefined) {
            if (dataJson.InnovateEquipment && dataJson.InnovateEquipment.length > 0) {
                //
                var innovateEquipment = dataJson.InnovateEquipment;
               
                html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">8、科技创新设备</td></tr>';
                var categoryName = '';
                for (var i = 0; i < innovateEquipment.length; i++) {
                    var itemObj = innovateEquipment[i];
                    if (itemObj.CategoryName != categoryName) {
                        categoryName = itemObj.CategoryName;
                        html += '<tr class="trpading"><td colspan="4" style="text-align:left;padding-left:10px;">' + itemObj.CategoryName +'</td></tr>';
                    }
                    if (itemObj.BrandModelList && itemObj.BrandModelList.length > 0) {
                        for (var j = 0; j < itemObj.BrandModelList.length; j++) {
                            var brandmodelItem = itemObj.BrandModelList[j];
                            var brandmodelz = "";
                            if (brandmodelItem.brand && brandmodelItem.brand.length > 0) {
                                brandmodelz = brandmodelItem.brand;
                            }
                            if (brandmodelItem.modelz && brandmodelItem.brand.modelz > 0) {
                                if (brandmodelz != "") {
                                    brandmodelz += " , ";
                                }
                                brandmodelz = brandmodelItem.modelz;
                            }
                            html += '<tr class="trpading">';
                            html += '<td class="font-yahei">品牌型号</td><td>' + brandmodelz + '</td>';
                            html += '<td class="font-yahei">数量（套）</td><td>' + brandmodelItem.num + '</td>';
                            html += '</tr>';
                        }
                    }
                    
                }
            }
        }
        return html;
    }
    /**9、实验室管理员 */
    function getloadLabManagerHtml(dataJson) {
        var html = '';
        if (dataJson != undefined) {
            if (dataJson.LabManager && dataJson.LabManager.length > 0) { 
                var labManager = dataJson.LabManager;
                html += '<tr class="trpading"><td colspan="4" style="font-weight:bold;text-align:left;padding:5px;">9、实验室管理员</td></tr>';

                for (var i = 0; i < labManager.length; i++) {
                    html += '<tr class="trpading">';
                    html += '<td class="font-yahei">' + labManager[i].name + '（人）</td><td>' + labManager[i].num + '</td>';
                    html += '<td class="font-yahei">其中专职（人）</td><td>' + labManager[i].numfull + '</td>';
                    html += '</tr>';
                }
            }
        }
        return html;
    }
</script>