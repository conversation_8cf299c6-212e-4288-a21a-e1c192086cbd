﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

<style type="text/css">

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>

<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="storagePlace" col="StoragePlace" style="display:inline-block;"></div>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExportOut" class="btn btn-warning btn-sm" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
            <a id="btnLend" class="btn btn-success disabled" onclick="editForm(1)"><i class="fa fa-edit"></i> 批量借出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var BasePageCode = 101011;
    $(function () {
        loadStoragePlace();

        initGrid();
        
        
        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnLend')) {
                $('#btnLend').toggleClass('disabled', !ids.length);
            }
        });
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/InstrumentLend/GetInstrumentListPageJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt1,
                    formatter: function (value, row, index) {
                        var html = '';
                        html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="editForm(0,this)" value="{0}" t="1"><i class="fa fa-edit"></i>借出</a> ', row.Id);
                        return html;
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        if (row.IsSelfMade == 1) {
                            html += Syjx.GetSelfMadeHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        if (html.length > 24) {
                            html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        }
                        return html;
                    }
                },
                { field: 'StockNum', title: '可借数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'FunRoom', title: '存放地', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.FunRoom,
                    formatter: function (value, row, index) {
                        if (row.Cupboard) {
                            value += '>' + row.Cupboard;
                        }
                        if (row.Floor) {
                            value += '>' + row.Floor;
                        }
                        return value;
                    }
                },
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function editForm(isBatch, obj) {
        var id = '';
        if (isBatch == 1) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (selectedRow.length > 0) {
                id = ys.getIds(selectedRow);
            }
            else {
                ys.msgError('请至少选择一项！');
                return false;
            }
        } else {
            id = $(obj).attr('value');
        }
        ys.openDialog({
            title: '借出',
            content: '@Url.Content("~/InstrumentManage/InstrumentLend/LendForm")' + '?schoolInstrumentId=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function loadStoragePlace() {
        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListAllJson")',
            class: "form-control",
            key: 'id',
            value: 'name',
            defaultName: '存放地'
        });
        $('#storagePlace').ysComboBoxTree('setValue', -1);
    }

    function resetGrid() {
        //清空条件
        $('#storagePlace').ysComboBoxTree('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function exportForm() { //导出
        var url = '/InstrumentManage/InstrumentLend/ExportOut';//仪器借出(101011)
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "Id", "order": "asc", "offset": 0, "limit": 10 });
        var postData = $("#searchDiv").getWebControls(pagination);
        postData.BasePageCode = BasePageCode;//仪器借出(101011)
        ys.exportExcel(url, postData);
    }
</script>