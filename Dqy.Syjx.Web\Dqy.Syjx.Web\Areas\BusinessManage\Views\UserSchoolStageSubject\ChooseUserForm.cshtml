﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
} 
    @*<div class="container-div">*@
<div class="wrapper animated fadeInRight">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        姓名：<input id="RealName" col="RealName" type="text" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-primary btn-sm" onclick="resetGrid()"><i class="fa fa-search"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar" style="color:red;">
            你所需的人员如不在下表中，请联系单位管理员进行添加！
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<script type="text/javascript">
    var OptType = ys.request("opttype");
    var RoleId = 0;
    $(function () {
        //if (OptType != undefined && OptType == 1) {
        //    RoleId = @RoleEnum.LaboratoryManager.ParseToInt();
        //}
        loadAdmin();
        initGrid();
        $("#searchDiv").show();
    });
    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/User/GetUserListJson")' + '?isHide=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            showColumns:false,
            showRefresh: false,
            showToggle:false,
            columns: [
                { radio: true, visible: true },
                {
                    field: 'Id0', title: '序号', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html = index += 1;
                        }
                        return html;
                    }
                },
                { field: 'DepartmentNames', title: '部门', sortable: true, halign: 'center', valign: 'middle' },
                { field: 'RealName', title: '姓名', sortable: true, width: 150, halign: 'center', valign: 'middle'},
                { field: 'Mobile', title: '手机号码', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center' },
                { field: 'UserName', title: '账号', sortable: true, width: 150, halign: 'center', valign: 'middle'},
                {
                    field: 'opt1', title: '操作', width: 60, halign: 'center', valign: 'middle', align: 'center', formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html += $.Format('&nbsp;<a onclick="selectUser(\'{0}\',\'{1}\');">选择</a>&nbsp;', row.RealName,row.Id);
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }
    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $("#RealName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
        }
    function loadAdmin() {
         ys.ajax({
                url: '@Url.Content("~/OrganizationManage/User/GetAdminJson")',
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        $("#toolbar").text($.Format("你所需的人员如不在下表中，请联系（{0}）老师进行添加！",obj.Data));
                    }
                }
            });
    }
    //赋值
    function selectUser(name,userid) {
        parent.setSelectName(name, userid);
        ys.closeDialog();
    }
    function saveForm(index) {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (!ys.checkRowEdit(selectedRow)) {
            ys.msgError('请选择数据信息后再确认！');
            return;
        }
        else {
            parent.setSelectName(selectedRow[0].RealName, selectedRow[0].Id);
            parent.layer.close(index);
        }
    }
</script>

