﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container { width: 100% !important; }
</style>
<div class="container-div">
@*    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、请在新学年的9月1日前创建新增班级，点击【创建班级】；<br />
                            2、如班级创建有误，请点击【删除】；<br />
                            3、班级数和学生数是各类达标、评价的基础数据，请确保数据正确。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    @await Html.PartialAsync("/Areas/OrganizationManage/Shared/SchoolStageGradeIndexPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    <li>
                        @Html.Hidden("IsGraduate", 0, new { col = "IsGraduate" })
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <li>
                        <div id="spanClassInfo" style="height: 30px; vertical-align: middle; display: table-cell;font:bold;"></div>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>
        <div id="toolbar" class="btn-group d-flex" role="group">
            <sapn id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 创建班级</sapn>
            <span id="btnEdit" class="btn btn-primary" onclick="showEditForm(0)"><i class="fa fa-edit"></i> 修改</span>
            <sapn id="btnDelete" class="btn btn-danger" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</sapn>
            <sapn class="btn btn-info" onclick="importForm()"><i class="fa fa-upload"></i> 导入班级</sapn>
            <sapn class="btn btn-info" onclick="upgradeForm()"><i class="fa fa-upload"></i> 年级升级</sapn>
            <sapn class="btn btn-info btn-student" style="display:none;" onclick="showImportStudentForm()"><i class="fa fa-upload"></i> 导入学生</sapn>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>


<script type="text/javascript">
    var IsShowStudent=false;
    $(function () {
        loadStudentVali();

        initGrid();
        loadClassInfo();
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'GradeId ASC,ClassId ASC',
            sortOrder: 'ASC',
            pageSize:25,
            showColumns:false,
            showRefresh:false,
            showToggle:false,
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'opt1', title: '操作', halign: 'center', align: 'center', width: 150, formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="showEditForm(\'{0}\');"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'{0}\');"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                            if (row.Statuz == 1) {
                                html += $.Format('<a class="btn btn-warning btn-xs" href="#" onclick="setStatuz(\'{0}\',{1});"><i class="fa fa-check"></i>禁用</a> ', row.Id, row.Statuz);
                            } else {
                                html += $.Format('<a class="btn btn-primary btn-xs" href="#" onclick="setStatuz(\'{0}\',{1});"><i class="fa fa-check"></i>启用</a> ', row.Id, row.Statuz);
                            }                            
                        }
                        return html;
                    }
                },
                {
                    field: 'Statuz', title: '状态', width: 80, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            if (row.Statuz == "@StatusEnum.Yes.ParseToInt()") {
                                return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                            } else {
                                return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                            }
                        }
                        else return '';
                    }
                },
                {
                    field: 'SchoolStageName', title: '学段', width: 80, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return "<b>总计（人）：</b>";
                    }
                },
                {
                    field: 'StartYear', title: '入学年份', width: 80, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'GradeName', title: '年级', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'ClassDesc', title: '班级', width: 160, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return "";//"<b>" + value + "（个）</b>";
                    }
                },
                {
                    field: 'StudentNum', title: '学生数', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return "<b>" + value + "</b>";
                    }
                },
                {
                    field: 'opt2', title: '学生名单', width: 100, sortable: true, halign: 'center', valign: 'middle', align: 'center',visible:IsShowStudent,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id != undefined) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="showSudentForm(\'{0}\');"><i class="fa fa-edit"></i>管理</a> ', row.Id);
                        }
                        return html;
                    }
                }                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                //去除总计行的checkbox
                
                let rows = $('#gridTable').bootstrapTable('getData');
                console.log(rows)
                if (rows.length > 0) {
                    if (rows && !rows[rows.length - 1].Id) {
                        //最后一个tr的第一个td数据置空：用数据控制的总计行页面放大缩小会导致失效，应用showFooter控制
                        $('#gridTable tbody').find('tr:last').find('td:first').html('')
                    }
                }
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $("#SchoolStage").ysComboBox("setValue", -1);
        $("#GradeId").ysComboBox("setValue", -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function loadClassInfo() {
        /*获取班级信息,单位班级数量，学生数量  GetInfo
         */
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolExtension/GetInfo")',
            type: 'post',
            success: function (obj) {
                if (obj.Tag == 1) {
                    if (obj.Data != undefined) {
                        $("#spanClassInfo").html('<span style="font-size: 20px;">&nbsp;&nbsp;|&nbsp;&nbsp;</span> <span style="    font-size: 18px;"><b>学校班级总数（个）：' + obj.Data.ClassNum + ' &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;学校学生总数（人）：' + obj.Data.StudentNum + '</b></span>');
                    }
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }

    function showEditForm(id) {
        if (id==0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                ys.msgError("请选择需要修改的数据。");
                return;
            }
            else {
                if (selectedRow.length > 1) {
                    ys.msgError("请选择一条修改，不可批量操作。");
                    return;
                }
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: '编辑',
            content: '@Url.Content("~/OrganizationManage/SchoolGradeClass/EditClassForm")' + '?id=' + id,
            width: '768px',
            height:'260px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: '创建班级',
            content: '@Url.Content("~/OrganizationManage/SchoolGradeClass/SchoolGradeClassForm")' + '?id=' + id,
            width: '768px',
            height: '450px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm(id) {
        var ids = id + '';
        var message = "确认要删除当前数据吗？";
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                var selectnum = selectedRow.length;
                if ($('input[name="btSelectAll"]').prop("checked")) {
                    selectnum = (selectnum-1);
                }
                message = '确认要删除选中的' + selectnum + '条数据吗？';
                ids = ys.getIds(selectedRow);
            } else {
                ys.msgSuccess('请选择需要删除的数据！');
                return;
            }
        }
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                        loadClassInfo();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function importForm() {
      ys.openDialog({
            title: "导入班级数据",
            content: '@Url.Content("~/OrganizationManage/SchoolGradeClass/ClassImport")',
            height: "380px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function upgradeForm() { 
        var message = "确认要升级年级信息吗？"; 
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/UpgradeFormJson")',
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        if (obj.ExtendData == 1) {
                            searchGrid();
                            loadClassInfo();
                            var layerid = layer.msg('升级成功，请创建新生入学班级', {
                                icon: 1, time:false, btn: ['确认'], yes: function () {
                                layer.close(layerid);
                                showSaveForm(true);
                            }, area: ['400px'] });
                        } else {
                            ys.msgSuccess(obj.Message);
                        }
                       
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    //设置状态，暂不做批量，正常应该禁用很少，批量容易误操作。
    function setStatuz(optid, currentstatuz) {
        var message = "确认要启用当前数据吗";
        var setstatuz = @StatusEnum.Yes.ParseToInt();
        if (currentstatuz == @StatusEnum.Yes.ParseToInt()) {
            message = "确认要禁用当前数据吗";
            setstatuz = @StatusEnum.No.ParseToInt();
        }
        var ids = optid;
        ys.confirm(message, function () {
            ys.ajax({
                url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/SetStatuzJson")' + '?ids=' + ids + '&statuz='+setstatuz,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                        loadClassInfo();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function showSudentForm(id) {
        var url = '@Url.Content("~/PersonManage/Student/List")' + '?id=' + id;
        createMenuItem(url, "学生管理");
    }

    function showImportStudentForm() {
        ys.openDialog({
            title: "学生导入",
            content: '@Url.Content("~/PersonManage/Student/ImportForm")',
            height: "280px",
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
     //#region
    //加载配置验证信息,是否开启学生管理
        function loadStudentVali() {
            $.ajax({
                url: '@Url.Content("~/SystemManage/ConfigSet/GetListBySysCode")',
                type: 'Post',
                data: { typecode:'1000_STUDENT'},
                async: false,
                success: function (obj) {
                     if (obj.Tag == 1) {
                         if (obj.Data != undefined && obj.Data.length > 0) {
                             for (var i = 0; i < obj.Data.length; i++) {
                                 if (obj.Data[0].ConfigValue == "1") {
                                     $('.btn-student').show();
                                     IsShowStudent=true;
                                 }
                             }
                         }
                     }
                }
            });
        }
    //#endregion
</script>
