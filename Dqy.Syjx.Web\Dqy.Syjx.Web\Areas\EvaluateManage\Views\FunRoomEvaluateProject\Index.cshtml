﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container{width:100% !important;}
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="SchoolStage" col="SchoolStage" style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1005" col="DictionaryId1005" style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006A" col="DictionaryId1006A" style="display:inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006B" col="DictionaryId1006B" style="display:inline-block;width:150px;"></div>
                    </li>
                    <li>
                        <div id="EvaluateStandardId" col="EvaluateStandardId" style="display:inline-block;width:150px;"></div>
                    </li>
                    <li>
                        <input id="VersionName" col="VersionName" type="text" style="display:inline-block;width:100px;" placeholder="评估项目名称" />
                    </li>
                    <li>
                        <input id="TargetName" col="TargetName" type="text" style="display:inline-block;width:100px;" placeholder="指标名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a class="btn btn-success" onclick="showAddProjectForm(0)"><i class="fa fa-plus"></i> 添加项目</a>
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true,0)"><i class="fa fa-plus"></i> 添加版本</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false,0)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm(0)"><i class="fa fa-remove"></i> 删除</a>
            <a id="btnSetDefault" class="btn btn-warning" onclick="setDefaultForm()"><i class="fa fa-cog"></i> 设置默认评估项目</a>
            <a id="btnUpdateResult" class="btn btn-primary" onclick="updateResultForm()"><i class="fa fa-refresh"></i> 更新应用达标结果</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSchoolStage();
        loadSubject(0);
        loadClassA(0);
        loadClassB(0);
        loadEvaluateStandard();
        initGrid();
        
        
    });
    function loadSchoolStage() {
        $('#SchoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1002',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                //var selectid = $('#SchoolStage').ysComboBox('getValue');
                //loadSubject(selectid);
            }
        });
    }
    function loadSubject(selectid) {
        $("#DictionaryId1005").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1005&Pid=' + selectid,
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                //var selectid = $('#DictionaryId1005').ysComboBox('getValue');
                //loadClassA(selectid);
            }

        });
    }
    function loadClassA(selectid) {
        $("#DictionaryId1006A").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1006&OptType=7&Pid=' + selectid,
            defaultName: '一级分类',
            key: 'DictionaryId',
            value: 'DicName',
            onChange: function () {
                var selectid = $('#DictionaryId1006A').ysComboBox('getValue');
                loadClassB(selectid);
            }
        });
    }
    function loadClassB(classid) {
        if (classid > 0) {
            var subjectid = $('#DictionaryId1005').ysComboBox('getValue');
            ys.ajax({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=1006&Pid=' + subjectid + '&DictionaryPid=' + classid,
                type: "get",
                success: function (obj) {
                    if (obj.Tag == 1) {
                        loadDictionaryId1006BData(obj.Data);
                    }
                }
            });
        } else {
            loadDictionaryId1006BData({});
        }
    }
    function loadDictionaryId1006BData(data) {
        $("#DictionaryId1006B").ysComboBox({
            defaultName: '二级分类',
            data: data,
            key: 'DictionaryId',
            value: 'DicName'
        });
    }
    function loadEvaluateStandard() {
        $("#EvaluateStandardId").ysComboBox({
            url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateStandard/GetListJson")',
            defaultName: '版本名称',
            key: 'Id',
            value: 'VersionName'
        });
    }
    function initGrid() {
        var queryUrl = '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            columns: [
                { checkbox: true, visible: true },
                {
                    field: 'EvaluateName', title: '评估项目名称', width: 300, sortable: true, halign: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        if (row.IsDefaultEv == 1) return value + '<span style="color:red">「默认」</span>';
                        else return value;
                    }
                },
                {
                    field: 'SchoolStage', title: '学段', sortable: true, width: 80, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SchoolStageName;
                    }
                },
                {
                    field: 'DictionaryId1006A', title: '一级分类', sortable: true, width: 120, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.ClassOneName;
                    }
                },
                {
                    field: 'DictionaryId1006B', title: '二级分类', sortable: true, width: 160, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.ClassTwoName;
                    }
                },
                { field: 'TargetName', title: '指标名称', sortable: true, halign: 'center', valign: 'middle', },
                {
                    field: 'DictionaryId1005', title: '适用学科', sortable: true, width: 100, halign: 'center', valign: 'middle', align: 'center',
                    formatter: function (value, row, index) {
                        return row.SubjectName;
                    }
                },
                { field: 'VersionName', title: '版本名称', sortable: true, halign: 'center', valign: 'middle' },
                { field: 'Remark', title: '备注', sortable: true, halign: 'center', valign: 'middle', },
                {
                    title: '操作',
                    halign: 'center',
                    align: 'center',
                    width: 150,
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="showSaveForm(false,\'' + row.Id + '\')"><i class="fa fa-edit"></i>修改</a>&nbsp;');
                        actions.push('&nbsp;<a class="btn btn-danger btn-xs" href="#" onclick="deleteForm(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;');
                        return actions.join('');
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }
    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        $('#SchoolStage').ysComboBox('setValue', '-1');
        $('#DictionaryId1005').ysComboBox('setValue', '-1');
        $('#DictionaryId1006A').ysComboBox('setValue', '-1');
        $('#DictionaryId1006B').ysComboBox('setValue', '-1');
        $('#EvaluateStandardId').ysComboBox('setValue', '-1');
        $("#VersionName").val('');
        $("#TargetName").val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function showSaveForm(bAdd,id) {
        if (!bAdd && id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑版本' : '添加版本',
            content: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/VersionForm")' + '?id=' + id,
            width: '768px',
            height: '550px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    /**存储回调函数 */
    var BackFunVersionProjectList;
    function showAddProjectForm(id, backfun) {
        ys.openDialog({
            title: id > 0 ? '修改项目名称' : '添加项目',
            content: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/ProjectForm")' + "?id=" + id,
            width: '768px',
            height: '200px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
                if (backfun != undefined) {
                    BackFunVersionProjectList = backfun;
                }
            }
        });
    }
    function deleteForm(id) {
        var ids = '';
        var tagMsg = '';
        if (id == 0) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (selectedRow.length == 0) {
                ys.msgError("请选择需要删除的数据。");
                return;
            }
            tagMsg = '确认要删除选中的' + selectedRow.length + '条数据吗？';
            ids = ys.getIds(selectedRow);
        } else {
            ids = id;
            tagMsg = '确认要删除当前这条数据吗？';
        }

        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/DeleteFormJson")' + '?ids=' + ids,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    /**刷新VersionForm页面项目列表，重新加载 */
    function reloadVersionProject() {
        if (BackFunVersionProjectList != undefined) {
            BackFunVersionProjectList();
        }
    }

    function setDefaultForm() {
        ys.openDialog({
            title: '设置默认评估项目',
            content: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/SetDefaultForm")',
            width: '768px',
            height: '400px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
    /**更新达标结果 */
    function updateResultForm() {
        var ids = '';
        var tagMsg = '';
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length == 0) {
            tagMsg = '确认要更新全部数据的达标结果吗？<br/><span style="color:red;">(可能会很慢！)</span>';
        } else {
            ids = ys.getIds(selectedRow);
            tagMsg = '确认要更新选中的' + selectedRow.length + '条数据的达标结果吗？';
        }
        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/EvaluateManage/FunRoomEvaluateProject/UpdateResult")' + '?ids=' + ids,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

</script>
