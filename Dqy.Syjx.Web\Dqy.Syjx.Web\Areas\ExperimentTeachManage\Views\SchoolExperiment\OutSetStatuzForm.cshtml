﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}

<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group row" style="flex-wrap:nowrap;">
            <label class="col-sm-6 control-label " style=" width: 150px; text-align: right;line-height:32px;">状态<font class="red"> *</font></label>
            <div class="col-sm-6" style="display:inline-block;">
                <div id="Statuz" col="Statuz"></div>
            </div>
        </div> 
    </form>
</div>
<script type="text/javascript">
    var ids = ys.request("ids");
    $(function () {
        $("#Statuz").ysRadioBox({ data: ys.getJson(@Html.Raw(typeof(StatusEnum).EnumToDictionaryString())), class: "form-control" });
    }); 
    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls();
            if (postData.Statuz != @StatusEnum.Yes.ParseToInt() && postData.Statuz != @StatusEnum.No.ParseToInt()) {
                ys.msgError('验证失败，请选择实验状态！<br/>');
                return false;
            }
            console.log("----" + ids);
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/SchoolExperiment/SaveOutSetStatuzFormJson")',
                type: 'post',
                data: { ids: ids, statuz: postData.Statuz },
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.searchGrid();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

