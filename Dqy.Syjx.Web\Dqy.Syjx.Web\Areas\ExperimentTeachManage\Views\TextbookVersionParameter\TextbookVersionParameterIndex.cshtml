﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
   @* <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            一般只需要配置小学和初中的实验考核数；因为高中不同班级的实验教材不一样，高中按照学校的实验计划数进行考核。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="countyId" col="CountyId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="schoolStage" col="SchoolStage" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="gradeId" col="GradeId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    @*<li>
                        <span id="schoolYearStart" col="SchoolYearStart" style="display:inline-block;width:100px;"></span>
                    </li>*@
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(0)"><i class="fa fa-plus"></i> 单条新增</a>
            <a id="btnDelete" class="btn btn-primary disabled" onclick="showCopyAddForm()"><i class="fa fa-plus"></i> 复制新增</a>
            <a id="btnBatchEdit" class="btn btn-info disabled" onclick="showBatchEditForm()"><i class="fa fa-edit"></i> 批量修改</a>
            <a id="btnExport" class="btn btn-primary" onclick="updateResultForm()"><i class="fa fa-refresh"></i> 更新应用达标结果</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSchoolStage();
        loadGrade();
        loadCourse();
        loadSchoolTerm();
        //loadSchoolYearStart();
        
        
        initGrid();

        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnBatchEdit')) {
                $('#btnBatchEdit').toggleClass('disabled', !ids.length);
            }
        });

        $('#countyId').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetAllCountyListJson")',
            key: 'Id',
            value: 'Name',
            defaultName: '区县'
        });
    });

    function loadSchoolStage() {
        $('#schoolStage').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.SchoolStage.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段'
        });
    }
    function loadGrade(schoolStage) {
        $('#gradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级'
        });
    }
    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()' + '&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学科'
        });
    }
    function loadSchoolTerm() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
    }
    function loadSchoolYearStart() {
        ComBox.SchoolTermYear($('#schoolYearStart'), '', '学年');
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/GetPageListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                [
                    { title: '', align: 'center', colspan: 6 },
                    { title: '必做演示实验', align: 'center', colspan: 2 },
                    { title: '必做分组实验', align: 'center', colspan: 2 },
                    { title: '选做演示实验', align: 'center', colspan: 2 },
                    { title: '选做分组实验', align: 'center', colspan: 2 },
                    { title: '小计', align: 'center', colspan: 2 },
                    { title: '', align: 'center', colspan: 1 }
                ],
                [
                    { checkbox: true, visible: true },
                    { field: 'AreaName', title: '区县', sortable: true, halign: 'center', align: 'center' },
                    { field: 'SchoolStageName', title: '学段', sortable: true, halign: 'center', align: 'center' },
                    { field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center' },
                    { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center' },
                    //{
                    //    field: 'SchoolYearStart', title: '学年', sortable: true, halign: 'center', align: 'center',
                    //    formatter: function (value, row, index) {
                    //        return (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2);
                    //    }
                    //},
                    {
                        field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center',
                        formatter: function (value, row, index) {
                            return value == @SchoolTermEnum.LastSemester.ParseToInt() ? "@SchoolTermEnum.LastSemester.GetDescription()" : "@SchoolTermEnum.NextSemester.GetDescription()";
                        }
                    },
                    { field: 'NeedShowNum', title: '总数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'StandardNeedShowNum', title: '考核数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'NeedGroupNum', title: '总数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'StandardNeedGroupNum', title: '考核数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'OptionalShowNum', title: '总数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'StandardOptionalShowNum', title: '考核数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'OptionalGroupNum', title: '总数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'StandardOptionalGroupNum', title: '考核数', sortable: true, halign: 'center', align: 'center' },

                    { field: 'AllTotal', title: '总数', sortable: true, halign: 'center', align: 'center' },
                    { field: 'CheckTotal', title: '考核数', sortable: true, halign: 'center', align: 'center' },

                    {
                        field: '', title: '操作', halign: 'center', align: 'center',
                        formatter: function (value, row, index) {
                            var html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="edit(this)" value="{0}"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="del(this)" value="{0}"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                            html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="updateNum(this)" value="{0}"><i class="fa fa-refresh"></i>更新实验数量</a> ', row.Id);
                            return html;
                        }
                    }
                ]
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        //清空条件
        $('#schoolStage').ysComboBox('setValue', -1);
        $('#gradeId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        //$('#schoolYearStart').ysComboBox('setValue', -1);
        $('#schoolTerm').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(id) {
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/TextbookVersionParameterForm")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showCopyAddForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
        }
        ys.openDialog({
            title: '复制新增',
            width: '800px',
            height: '300px',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/CopyAddForm")' + '?ids=' + ids,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showBatchEditForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
        }
        ys.openDialog({
            title: '批量修改',
            width: '800px',
            height: '400px',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/BatchEditForm")' + '?ids=' + ids,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function del(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要删除该数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/DeleteFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function updateNum(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要更新该实验数量吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/UpdateExperimentNum")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function edit(obj) {
        showSaveForm($(obj).attr('value'));
    }

    /**更新达标结果 */
    function updateResultForm() {
        var ids = '';
        var tagMsg = '';
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length == 0) {
            tagMsg = '确认要更新全部数据的达标结果吗？<br/><span style="color:red;">(可能会很慢！)</span>';
        } else {
            ids = ys.getIds(selectedRow);
            tagMsg = '确认要更新选中的' + selectedRow.length + '条数据的达标结果吗？';
        }
        ys.confirm(tagMsg, function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionParameter/UpdateResult")' + '?ids=' + ids,
                type: 'get',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>
