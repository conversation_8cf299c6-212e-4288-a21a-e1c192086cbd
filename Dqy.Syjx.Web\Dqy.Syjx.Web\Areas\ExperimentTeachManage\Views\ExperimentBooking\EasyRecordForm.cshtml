﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/css/fileinput.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fileinput/5.0.3/js/fileinput.min.js"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
@BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row" style="height:auto;">
        <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
         
        </div>
    </div>
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="GradeId" col="GradeId" style="display:inline-block;width:110px;"></span>
                    </li>
                    <li>
                        <div id="ExperimentVersionId" col="ExperimentVersionId" style="display: inline-block;width:200px;"></div>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="experimentType" col="ExperimentType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="IsNeedDo" col="IsNeedDo" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <input id="experimentName" col="ExperimentName" style="display:inline-block;width:180px;" placeholder="实验名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        loadSearchCombo();

        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetPlanListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="selectClass(this,\'{0}\')"><i class="fa fa-edit"></i>登记</a> ', row.Id);
                        }
                        return html;
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        var html = "@SchoolTermEnum.LastSemester.GetDescription()";
                        if (value == @SchoolTermEnum.NextSemester.ParseToInt()) {
                            html = "@SchoolTermEnum.NextSemester.GetDescription()";
                        }
                        return html;
                    }
                },
                { field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center', width: 50 },
                { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', width: 50 },
                { field: 'ExperimentName', title: '实验名称', sortable: true, halign: 'center', align: 'left', width: 180 },
                { field: 'VersionName', title: '实验教材', sortable: true, halign: 'center', align: 'left', width: 160  },
          
                {
                    field: 'ExperimentType', title: '实验类型', sortable: true, halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @ExperimentTypeEnum.Demo.ParseToInt():
                                return "@ExperimentTypeEnum.Demo.GetDescription()";
                             case @ExperimentTypeEnum.Group.ParseToInt():
                                return "@ExperimentTypeEnum.Group.GetDescription()";
                            default:
                                return '演示/分组';
                        }
                    }
                },
                {
                    field: 'IsNeedDo', title: '实验要求', sortable: true, halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '@IsNeedEnum.MustDo.GetDescription()';
                        if (@IsNeedEnum.SelectToDo.ParseToInt()== value) {
                            html = '@IsNeedEnum.SelectToDo.GetDescription()';
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $('#schoolTerm').ysComboBox('setValue', -1);
        $('#GradeId').ysComboBox('setValue', -1);
        $('#ExperimentVersionId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#experimentType').ysComboBox('setValue', -1);
        $('#IsNeedDo').ysComboBox('setValue', -1);
        $('#experimentName').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    /**加载搜索条件*/
    function loadSearchCombo() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学科'
        });

        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
        $("#IsNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });
        loadGrade();
        loadVersionBase();
    }

    function loadGrade() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1002",
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var pids = '';
                    $.each(obj.Data, function (i, n) {
                        if (i > 0) {
                            pids += ',';
                        }
                        pids += n.DictionaryId;
                    });
                    $('#GradeId').ysComboBox({
                        url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1003&Pids=" + pids,
                        key: 'DictionaryId',
                        value: 'DicName',
                        defaultName: '年级',
                    });
                }
            }
        });
    }
    function loadVersionBase() {
        var schoolterm = 0;
        if (parseInt($('#SchoolTerm').ysComboBox("getValue")) > 0) {
            schoolterm = parseInt($('#SchoolTerm').ysComboBox("getValue"));
        }
        var startyear = 0;
        if (parseInt($('#schoolYearStart').ysComboBox("getValue")) > 0) {
            startyear = parseInt($('#schoolYearStart').ysComboBox("getValue"));
        }
        var gradeid = 0;
        if (parseInt($('#GradeId').ysComboBox("getValue")) > 0) {
            startyear = parseInt($('#GradeId').ysComboBox("getValue"));
        }
        var courseid = 0;
        if (parseInt($('#courseId').ysComboBox("getValue")) > 0) {
            startyear = parseInt($('#courseId').ysComboBox("getValue"));
        }
         ys.ajax({
             url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/GetVersionListJson")' + '?SchoolYearStart=' + startyear + '&SchoolTerm=' + schoolterm + '&GradeId=' + gradeid + '&CourseId' + courseid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var arrData = [{ VersionName: "校本实验", Id: 99 }];
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        arrData = arrData.concat(obj.Data);
                    }
                    $('#ExperimentVersionId').ysComboBox({
                        data: arrData,
                        key: 'Id',
                        value: 'VersionName',
                        defaultName: '实验教材版本'
                    });
                }
            }
        });
    }

    function selectClass(obj, id) { 
        ys.openDialog({
            title: '选择登记班级',
            content: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/EasyRecordSelectClass")' + '?id=' + id,
            width: '500px',
            height: '300px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        }); 
    }
    function bookingEdit(id){
        var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/EasyRecordEditForm")' + '?id=' + id;
        createMenuItem(url, "简易登记");
    }
</script>

