﻿@{
    Layout = "~/Views/Shared/_FormWhite.cshtml";
}
<style>
    .form-group.dection, .form-group.lookview{
    display:flex;}
</style>
<div class="wrapper animated fadeInRight">
    <form id="form" class="form-horizontal m">
        <div class="form-group dection">
            <label class="col-sm-3 control-label ">维修完成<font class="red"> *</font></label>
            <div class="col-sm-8">
                <div id="Statuz" col="Statuz"></div>
            </div>
        </div>
        <div class="form-group dection">
            <label class="col-sm-3 control-label ">维修内容<font class="red"> *</font></label>
            <div class="col-sm-8">
                <textarea id="ServiceContent" style="max-width:100%" col="ServiceContent" class="form-control"></textarea>
            </div>
        </div>
        <div class="form-group lookview" style="display:none;">
            <label class="col-sm-3 control-label ">维修时间</label>
            <div class="col-sm-8">
                <label class="control-label " id="repartDate"></label>
            </div>
        </div>
        <div class="form-group lookview" style="display:none;">
            <label class="col-sm-3 control-label ">维修内容</label>
            <div class="col-sm-8">
                <textarea id="reppartContent" style="max-width:100%" class="form-control" readonly style="height:70px;"></textarea>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript">
    var id = ys.request("id");
    $(function () {
        if (id > 0) {
            $("#Statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())), class: 'form-control' });
            getForm();
            $('#form').validate({
                rules: {
                    unitId: { required: true }
                }
            });
        } else {
            parent.removeConfirm();
            var ServiceDate = ys.request("ServiceDate");
            var ServiceContent = ys.request("ServiceContent");
            $("#repartDate").text(ys.formatDate(ServiceDate, "yyyy-MM-dd"));
            $("#reppartContent").text(ServiceContent);
            $(".lookview").show();
            $(".dection").hide();
        }
    });

    function getForm() {
        ys.ajax({
            url: '@Url.Content("~/BusinessManage/FunRoomRepair/GetFormJson")' + '?id=' + id,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#form').setWebControls(obj.Data);
                }
            }
        });
    }

    function saveForm(index) {
        if ($('#form').validate().form()) {
            var postData = $('#form').getWebControls({ Id: id });
            ys.ajax({
                url: '@Url.Content("~/BusinessManage/FunRoomRepair/SaveFormJson")',
                type: 'post',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        parent.$('#gridTable').ysTable('refresh'); parent.resetToolbarStatus();
                        parent.layer.close(index);
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        }
    }
</script>

