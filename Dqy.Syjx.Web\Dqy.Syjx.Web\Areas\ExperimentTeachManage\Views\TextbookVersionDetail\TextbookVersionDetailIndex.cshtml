﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    @*<div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            基础实验：是指初中理化生科目强制需要做的实验。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="experimentType" col="ExperimentType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="isNeedDo" col="IsNeedDo" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="isEvaluate" col="IsEvaluate" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" style="width:120px;" placeholder="实验名称、章节" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showImportForm()"><i class="fa fa-upload"></i> 导入</a>
            <a id="btnSetStatuz" class="btn btn-primary disabled" onclick="showSetStatuzForm(false)"><i class="fa fa-edit"></i> 设置状态</a>
            <a id="btnSetIsEval" class="btn btn-primary disabled" onclick="showSetIsEvaluateForm()"><i class="fa fa-edit"></i> 设置是否考核</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var textbookVersionBaseId = ys.request("textbookVersionBaseId");
    var islook = ys.request("islook");; //是否仅查看
    $(function () {
        if (islook == 1) $('#toolbar').hide();

        loadCombo();

        initGrid();

        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnSetStatuz')) {
                $('#btnSetStatuz').toggleClass('disabled', !ids.length);
            }
            if ($('#btnSetIsEval')) {
                $('#btnSetIsEval').toggleClass('disabled', !ids.length);
            }
        });

        
        
    });

    function initGrid() {
        console.log('islook', islook);
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/GetPageListJson")' + '?TextbookVersionBaseId=' + textbookVersionBaseId;
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Id asc',
            columns: [
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', visible: (islook != 1),
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-success btn-xs" href="#" onclick="edit(this)" value="{0}"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                    }
                },
                { checkbox: true, visible: (islook != 1) },
                {
                    field: 'Statuz', title: '状态', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        if (value == "@StatusEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@StatusEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@StatusEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                { field: 'Chapter', title: '章节', sortable: true, halign: 'center', align: 'center' },
                { field: 'ChapterSort', title: '章节排序', sortable: true, halign: 'center', align: 'center' },
                { field: 'ExperimentCode', title: '实验代码', sortable: true, halign: 'center', align: 'center' },
                { field: 'ExperimentName', title: '实验名称', sortable: true, halign: 'center', align: 'left' },
                {
                    field: 'ExperimentType', title: '实验类型', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html='';
                        switch (value) {
                            case @ExperimentTypeEnum.Demo.ParseToInt():
                                html = '@ExperimentTypeEnum.Demo.GetDescription()';
                                break;
                            case @ExperimentTypeEnum.Group.ParseToInt():
                                html = '@ExperimentTypeEnum.Group.GetDescription()';
                                break;
                            default:
                                html='--';
                                break;
                        }
                        return html ;
                    }
                },
                {
                    field: 'IsNeedDo', title: '实验要求', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return value == @IsNeedEnum.MustDo.ParseToInt() ? "@IsNeedEnum.MustDo.GetDescription()" : "@IsNeedEnum.SelectToDo.GetDescription()";
                    }
                },
                { field: 'IsEvaluate', title: '是否考核', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return value == @IsStatusEnum.Yes.ParseToInt() ? "@IsStatusEnum.Yes.GetDescription()" : "<span style='color:red'>@IsStatusEnum.No.GetDescription()</span>";
                    } },
                { field: 'IsBase', title: '是否基础实验', sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return value == @IsStatusEnum.Yes.ParseToInt() ? "@IsStatusEnum.Yes.GetDescription()" : "@IsStatusEnum.No.GetDescription()";
                    } },
                { field: 'EquipmentNeed', title: '所需仪器', sortable: true, halign: 'center', align: 'left', visible: true },
                { field: 'MaterialNeed', title: '实验材料（含试剂）', sortable: true, halign: 'center', align: 'left', visible: true },
                { field: 'Remark', title: '备注', sortable: true, halign: 'center', align: 'left' }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();

        $('#btnSetStatuz').toggleClass('disabled', true);
        $('#btnSetIsEval').toggleClass('disabled', true);
    }

    function showImportForm() {
        ys.openDialog({
            title: '导入<span style="color:red;margin-left:20px;">导入完成后，请务必前往【达标参数设置】页面更新实验数量。</span>',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/ImportForm")' + '?textbookVersionBaseId=' + textbookVersionBaseId,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSetStatuzForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
        }
        ys.openDialog({
            title: '设置状态<span style="color:red;margin-left:20px;">设置完成后，请务必前往【达标参数设置】页面更新实验数量。</span>',
            width: '800px',
            height:'180px',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/SetStatuzForm")' + '?ids=' + ids + '&textbookVersionBaseId=' + textbookVersionBaseId,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSetIsEvaluateForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
        }
        ys.openDialog({
            title: '设置是否考核',
            width: '800px',
            height:'300px',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/SetIsEvaluateForm")' + '?ids=' + ids + '&textbookVersionBaseId=' + textbookVersionBaseId,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function edit(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '编辑',
            content: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/TextbookVersionDetailForm")' + '?id=' + id + '&textbookVersionBaseId=' + textbookVersionBaseId,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/ExperimentTeachManage/TextbookVersionDetail/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function loadCombo() {
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
        $("#isNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });
        $("#isEvaluate").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsStatusEnum).EnumToDictionaryString())), defaultName: '是否考核' });
    }

    function resetGrid() {
        //清空条件
        $('#experimentType').ysComboBox('setValue', -1);
        $('#isNeedDo').ysComboBox('setValue', -1);
        $('#isEvaluate').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
</script>
