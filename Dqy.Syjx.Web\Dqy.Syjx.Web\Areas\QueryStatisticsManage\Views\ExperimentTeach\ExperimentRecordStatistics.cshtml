﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int UnitType = (int)ViewBag.UnitType;

}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: 150px !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="addDetailTabForm()"><i class="fa fa-street-view"></i>&nbsp;按实验明细查看</a>
                    </li>
                    <li>
                        <span id="schoolYearStart" col="SchoolYearStart" style="display: inline-block; width: 80px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display: inline-block; width: 80px;"></span>
                    </li>
                    <li id="liCountyId" style="display:none;">
                        <span id="countyId" col="CountyId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li id="liSchoolStageId" style="display:none;">
                        <span id="schoolStageId" col="SchoolStageId" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li id="liSchoolId" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:150px;"></span>
                    </li>

                    <li>
                        <span id="GradeId" col="GradeId" style="display: inline-block; width: 110px;"></span>
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display: inline-block; width: 100px;"></span>
                    </li>

                    <li>
                        <span id="experimentType" col="ExperimentType" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <span id="isNeedDo" col="IsNeedDo" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>
<script type="text/javascript">

    var Query_SchoolId = ys.request("id");
    var Query_SchoolStartYear = ys.request("startyear");
    var Query_SchoolTerm = ys.request("term");

    var SortName = 'SchoolYearStart ASC,SchoolTerm ASC';
    var Com_SchoolTermYear = new Date().getFullYear();
    var Com_SchoolTerm = 1;
    var BasePageCode = 101023;//实验开出记录(101023)
    $(function () {
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#liCountyId').show();
            $('#liSchoolId').show();
            loadCounty();
            loadSchool(0);
        } else if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            $('#liSchoolId').show();
            loadSchool(-1, 0);
        }
        loadGrade(); //班级

        if (@UnitType == @UnitTypeEnum.City.ParseToInt() || @UnitType == @UnitTypeEnum.County.ParseToInt()) {
            $('#liSchoolStageId').show();
            loadSchoolStage();//学段
        }

        loadSchoolTermYear();//学年
        loadSchoolTerm(); //学期
        loadCourse();//学科
        loadExperimentType();//实验类型
        $("#isNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });//实验要求
        if (Query_SchoolStartYear > 0 && Query_SchoolTerm > 0) {
            Com_SchoolTermYear = Query_SchoolStartYear;
            $("#schoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
            Com_SchoolTerm = Query_SchoolTerm;
            $("#schoolTerm").ysComboBox('setValue', Com_SchoolTerm);
        } else {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
                type: 'get',
                async: false,
                success: function (obj) {
                    if (obj.Tag == 1 && obj.Data != undefined) {
                        if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                            Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                            $("#schoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                        }
                        if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                            Com_SchoolTerm = obj.Data.SchoolTerm;
                            $("#schoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                        }
                    }
                }
            });
        }
        initGrid();
    });

    function addDetailTabForm() { //按实验明细查看
        var crrentSchoolYearStart = $("#schoolYearStart").ysComboBox("getValue");
        var crrrentSchoolTerm = $("#schoolTerm").ysComboBox("getValue");
        var url = '@Url.Content("~/QueryStatisticsManage/ExperimentTeach/ExperimentRecordList")' + '?SchoolYearStart=' + crrentSchoolYearStart + '&SchoolTerm=' + crrrentSchoolTerm;
        createMenuItem(url, "实验明细查看");
    }

    //区县
    function loadCounty() {
        $("#countyId").ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetCountyBoxByCityIdJson")',
            key: "Id",
            value: "Name",
            defaultName: '区县名称',
            onChange: function () {
                var countyId = $("#countyId").ysComboBox("getValue");
                loadSchool(countyId);
            }
        });
    }

    //学校
    function loadSchool(countyId, schoolStageId) {
        if (parseInt(countyId) == -1) {
            $("#schoolId").ysComboBox({
                url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000&SchoolStageId=" + schoolStageId,
                key: 'Id',
                value: 'Name',
                defaultName: '单位名称'
            });
            $(".select2-container").width("100%");
        } else if (parseInt(countyId) == 0) {
            $("#schoolId").ysComboBox({
                data: [],
                key: 'Id',
                value: 'Name',
                defaultName: '单位名称'
            });
            $(".select2-container").width("100%");
        } else if (parseInt(countyId) > 0) {
            $("#schoolId").ysComboBox({
                url: '@Url.Content("~/OrganizationManage/Unit/GetUnitList?")' + 'Pid=' + countyId,
                key: 'Id',
                value: 'Name',
                defaultName: '单位名称'
            });
            $(".select2-container").width("100%");
        }
        if (Query_SchoolId != undefined && Number(Query_SchoolId) && Query_SchoolId > 0) {
            $("#schoolId").ysComboBox("setValue", Query_SchoolId);
        }
    }

    //学段
    function loadSchoolStage() {
        $('#schoolStageId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetStageByUserId")',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学段',
            onChange: function () {
                if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
                    var schoolStageId = $("#schoolStageId").ysComboBox("getValue");
                    loadSchool(-1, schoolStageId);
                }
            }
        });
    }

    //学年
    function loadSchoolTermYear() {
        ComBox.SchoolTermYear($('#schoolYearStart'), undefined, '学年');
    }

    //学期
    function loadSchoolTerm() {
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
    }

    //实验类型
    function loadExperimentType() {
        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
    }

    //班级
    function loadGrade() {
        $('#GradeId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Grade.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '年级'
        });
    }

    //学科
    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetCourseByUserId?nature=1")',
            defaultName: '适用学科',
            key: 'DictionaryId',
            value: 'DicName'
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetExperimentRecordStatistics")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: SortName,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                {
                    field: '', title: '实验明细', halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(\'' + row.CountyId + '\',\'' + row.SchoolId + '\',\'' + row.SchoolYearStart + '\',\'' + row.SchoolTerm + '\',\'' + row.CourseId + '\',\'' + row.SchoolGradeClassId + '\')"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        return html;
                    }
                },
                {
                    field: 'CountyName', title: '区县名称', halign: 'center', align: 'left', visible: (@UnitType == @UnitTypeEnum.City.ParseToInt()), width: 80,
                    formatter: function (value, row, index) {
                        if (row.CountyName) {
                            return row.CountyName
                        }
                    }
                },
                {
                    field: 'SchoolId', title: '单位名称', sortable: true, halign: 'center', align: 'left', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt()), width: 150,
                    formatter: function (value, row, index) {
                        if (row.SchoolName) {
                            return row.SchoolName
                        }
                    }
                },
                {
                    field: 'SchoolStage', title: '学段', sortable: true, halign: 'center', align: 'center', visible: (@UnitType != @UnitTypeEnum.School.ParseToInt()), width: 60,
                    formatter: function (value, row, index) {
                        if (row.SchoolStageName) {
                            return row.SchoolStageName
                        }
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        if (row.SchoolYearStart < 0) {
                            row.SchoolYearStart = 0;
                        }
                        if (row.SchoolYearEnd < 0) {
                            row.SchoolYearEnd = 0;
                        }
                        return (row.SchoolYearStart + '').substr(2) + '~' + (row.SchoolYearEnd + '').substr(2)
                            + (value == @SchoolTermEnum.LastSemester.ParseToInt()
                                ? "@SchoolTermEnum.LastSemester.GetDescription()"
                                : "@SchoolTermEnum.NextSemester.GetDescription()");
                    }
                },
                {
                    field: 'SchoolGradeClassId', title: '班级', sortable: true, halign: 'center', align: 'center', width: 120,
                    formatter: function (value, row, index) {
                        if (row.ClassDesc) {
                            return row.ClassDesc
                        }
                    }
                },
                {
                    field: 'CourseId', title: '学科', sortable: true, halign: 'center', align: 'center', width: 60,
                    formatter: function (value, row, index) {
                        if (row.CourseName) {
                            return row.CourseName
                        }
                    }
                },
                { field: 'NeedShowNum', title: '必做演示', halign: 'center', align: 'center', width: 100 },
                { field: 'NeedGroupNum', title: '必做分组', halign: 'center', align: 'center', width: 100 },
                { field: 'OptionalShowNum', title: '选做演示', halign: 'center', align: 'center', width: 100 },
                { field: 'OptionalGroupNum', title: '选做分组', halign: 'center', align: 'center', width: 100 },
                { field: 'AllNum', title: '小计', halign: 'center', align: 'center', width: 100 }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function resetGrid() {
        //清空条件
        $('#schoolYearStart').ysComboBox('setValue', Com_SchoolTermYear);
        $('#schoolTerm').ysComboBox('setValue', Com_SchoolTerm);
        $('#GradeId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#sourceType').ysComboBox('setValue', -1);
        $('#experimentType').ysComboBox('setValue', -1);
        $('#isNeedDo').ysComboBox('setValue', -1);
        if (@UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#countyId').ysComboBox('setValue', -1);
        }
        if (@UnitType == @UnitTypeEnum.County.ParseToInt() || @UnitType == @UnitTypeEnum.City.ParseToInt()) {
            $('#schoolId').ysComboBox('setValue', -1);
            $('#schoolStageId').ysComboBox('setValue', -1);
        }
        if (@UnitType == @UnitTypeEnum.County.ParseToInt()) {
            loadSchool(-1, 0);
        }
        $('#gridTable').ysTable('search');
        $('#isShowCurrentSchoolYearStart').ysCheckBox('setValue', 1);
        resetToolbarStatus();
    }

    function look(countyId, schoolId, schoolYearStart, schoolTerm, courseId, schoolGradeClassId) {
        var url = '@Url.Content("~/QueryStatisticsManage/ExperimentTeach/ExperimentRecordList?")' + 'CountyId=' + countyId + '&SchoolId=' + schoolId + '&SchoolYearStart=' + schoolYearStart + '&SchoolTerm=' + schoolTerm + '&CourseId=' + courseId + "&SchoolGradeClassId=" + schoolGradeClassId;
        createMenuItem(url, "实验明细");
    }
    //导出
    function exportForm() {
        var url = '@Url.Content("~/QueryStatisticsManage/ExperimentTeach/ExportStatistics")';//实验开出记录(101023)
        var pagination = $('#gridTable').ysTable('getPagination', { "sort": "SchoolYearStart ASC,SchoolTerm ASC", "order": "desc", "offset": 0, "limit": 10 });
        var postData = $('#searchDiv').getWebControls(pagination);
        postData.BasePageCode = BasePageCode;//实验开出记录(101023)
        ys.exportExcel(url, postData);
    }
</script>