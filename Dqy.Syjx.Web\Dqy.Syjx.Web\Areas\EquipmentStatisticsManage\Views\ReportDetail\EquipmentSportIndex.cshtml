﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";

}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="BackupYear" col="BackupYear" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolId" col="SchoolId" style="display: inline-block; width: 180px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var columnArr = [];
    var columnFunRooms = [];
    var Query_DicIds = [1006014, 1006016, 1006018, 1006021, 1006023, 1006027, 1006025, 1006030, 1006032, 1006040, 1006037];
    var Current_QueryYear = new Date().getFullYear();
    $(function () {
        $('#SchoolId').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenHasKindergartenPageList")' + "?PageSize=10000", key: 'Id', value: 'Name',
            defaultName: '单位名称',
            minimumResultsForSearch: 0
        });
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        //获取加载列
        $.ajax({
            url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetDictionaryListJson")',
            data: { dicids: Query_DicIds },
            async:false,
            type: 'post',
            success: function (obj) {
                if (obj.Tag == 1) {
                    columnFunRooms = obj.Data;
                }
            }
        });
        initGrid();

    });

    function initGrid() {
        var columnsGroup = [
            {
                field: 'index', title: '序号', colspan: 1, rowspan: 2, width: 60, halign: 'center', valign: 'middle', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return index + 1;
                    else return '';
                }
            },
            {
                field: 'SchoolCode', title: '<div style="width:80px;">单位编号</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value;
                    else return '';
                }

            },
            {
                field: 'SchoolName', title: '<div style="width:120px;">单位名称</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
                formatter: function (value, row, index) {
                    var html = value;
                    if (html == "<b>总计：<b>") {
                        html = "<div style='text-align:center;'><b>总计：<b></div>";
                    }
                    return html;
                }
            }];
        var columns = [];
        var groupFunRoomNum = 0;
        if (columnFunRooms != undefined && columnFunRooms.length > 0) {
            groupFunRoomNum = columnFunRooms.length;
            for (var i = 0; i < columnFunRooms.length; i++) {
                var itemObj = columnFunRooms[i];
                var itemtitle = itemObj.DicName;
                if (itemtitle.length > 4) {
                    itemtitle = (itemtitle.slice(0, 4) + "<br />" + itemtitle.slice(4));
                }




                columns.push({
                    field: 'Dic_' + itemObj.DictionaryId, title: itemtitle, halign: 'center', align: 'center', valign: 'middle',
                    formatter: function (value, row, index) {
                        if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                    }
                });
            }
        }   

        columns.push(
            {
                field: 'Category_1', title: '幼儿园音<br />乐类', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'Category_2', title: '幼儿园美<br />工类', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'Category_3', title: '幼儿园科<br />学类', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'Category_4', title: '幼儿园建<br />构类', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'Category_5', title: '幼儿园体<br />育类', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'Category_6', title: '幼儿园其<br />他类', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'SportRunwayLength', title: '跑道长度<br />（米）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'SportBasketballNum', title: '篮球场<br />（个）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SportVolleyballNum', title: '排球场<br />（个）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? value.toLocaleString() : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'SportFootballNum', title: '足球场<br />（个）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? value.toLocaleString() : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'SportPingPongNum', title: '乒乓球桌<br />（张）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? value.toLocaleString() : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            },
            {
                field: 'SportRoomNum', title: '室内运动<br />场（个）', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId) return value > 0 ? value.toLocaleString() : '-';
                    else return value > 0 ? ('<b>' + ComBox.ToLocaleString(value) + '</b>') : '-';
                }
            }); 
        if (groupFunRoomNum > 0) {
            columnsGroup.push({ title: '中小学专用教室（间）', align: 'center', colspan: groupFunRoomNum });
        }
        columnsGroup.push({ title: '幼儿园专用教室（间）', align: 'center', colspan: 6 });
        columnsGroup.push({ title: '体育场所', align: 'center', colspan: 6 });

        columnArr = [
            columnsGroup,
            columns
        ];
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            var postData = {
                ModuleId: @BackupsModuleIdEnum.ZbStatistics.ParseToInt(),
                PageCode: "@BackupsPageCodeEnum.ZhunFunroom.ParseToInt().ToString()",
                BackupType: @BackupTypeEnum.DataTitleJson.ParseToInt(),
                BackupYear: thisYear
            };
            $.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetBackupsTitleJson")',
                async: false,
                type: 'get',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var colArr = obj.Data[0].Content;
                        if (typeof (colArr) == "string") {
                            colArr = JSON.parse(colArr);
                        }
                        if (colArr && colArr.length > 0) {
                            columnArr = [];
                            for (var i = 0; i < colArr.length; i++) {
                                columnArr.push(Syjx.GetGridTableColumns(colArr[i]));
                            }
                        }
                    } else {

                    }
                }
            });
        }
        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetSportPageListJson")';

        $('#gridTable').ysTable({
            url: queryUrl,
            sortName:'Sort ASC ,SchoolId DESC ',
            columns: columnArr ,
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                queryString.DicIds = Query_DicIds.join(",");
                return queryString;
            }
        });
    }

    function searchGrid() {
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        if (Current_QueryYear != thisYear && parseInt(thisYear) > 0) {
            Current_QueryYear = thisYear;
            $('#gridTable').bootstrapTable('destroy');
            initGrid();//重新表头。
        } else {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
    }


    function resetGrid() {
        $("#SchoolId").ysComboBox('setValue', -1);
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function exportForm() {
        var url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/SportExport")';
        var postData = $("#searchDiv").getWebControls();
        postData.DicIds = Query_DicIds.join(",");
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/BackupExport")';
            postData.PageCode = '@BackupsPageCodeEnum.ZhunFunroom.ParseToInt().ToString()';
        }
        ys.exportExcel(url, postData);
    }
</script>
