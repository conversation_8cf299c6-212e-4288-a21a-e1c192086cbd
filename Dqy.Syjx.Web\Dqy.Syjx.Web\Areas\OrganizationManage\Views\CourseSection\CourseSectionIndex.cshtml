﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}

<div class="container-div">
    <div class="row" style="height:auto;">
       @* <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、√代表已设置，×代表还未设置；<br />
                        </div>
                    </div>
                </div>
            </div>
        </div>*@

    </div>
    <div class="row">
        <div class="col-sm-12 select-table table-striped">
            <div class="fixed-table-toolbar">
                <div class="bs-bars pull-left">
                    <div id="toolbar" class="btn-group d-flex" role="group">
                        
                        <a id="btnAdd" class="btn btn-primary" onclick="showTimeSlot()"><i class="fa fa-edit"></i> 时段设置</a>
                        <!--帮助文档需要内容,id值必须为“helpBtn”-->
                        <div class="fa fa-question-circle" id="helpBtn" ></div>
                    </div>
                    
                </div>
            </div>
            <table id="gridTable" data-mobile-responsive="true">
               
            </table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {

        loadGrid();
        
        
    });

    function loadGrid(){
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=1015',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var objArray =obj.Data;
                    var columns = [{ field: 'DicName', title: '年级', align: 'center', },
                    //{
                    //    field: 'Opt', title: '时段设置', align: 'center',
                    //    formatter: function (value, row, index) {
                    //        var html = '<i class="fa fa-close"></i>&nbsp;&nbsp;&nbsp;';
                    //        html += $.Format('<a href="#" onclick="showTimeSlot({0},{1})">设置</a>', row.DictionaryPid, row.DictionaryId);
                    //        if (row.DictionaryPid==1){
                    //            html = '<i class="fa fa-check"></i>&nbsp;&nbsp;&nbsp;';
                    //            html += $.Format('<a href="#" onclick="showTimeSlot({0},{1})">修改</a>', row.DictionaryPid, row.DictionaryId);
                    //            html += $.Format('&nbsp;&nbsp;<a href="#" onclick="showTimeCopy({0},{1},\'{2}\')">复制到</a>', row.DictionaryPid, row.DictionaryId, row.DicName);
                    //        }
                    //        return html;
                    //    }
                    //},
                    ];
                    

                    $(objArray).each(function(i,v){
                        columns.push({
                            field: 'Opt' + i, title: '' + v.DicName + '', align: 'center',
                            formatter: function (value, row, index) {
                                var html = '<i class="fa fa-close"></i>&nbsp;&nbsp;&nbsp;';
                                html += $.Format('<a href="#" onclick="showSaveForm({0},{1},{2})">设置</a>', row.DictionaryPid, row.DictionaryId, v.DictionaryId);
                                var WeekIds = row.WeekIds;
                                if (WeekIds != ""){
                                    WeekIds = "," + WeekIds+",";
                                    if (WeekIds.indexOf(","+v.DictionaryId+",") != -1 ){
                                        html = '<i class="fa fa-check"></i>&nbsp;&nbsp;&nbsp;';
                                        html += $.Format('<a href="#" onclick="showSaveForm({0},{1},{2})">修改</a>', row.DictionaryPid, row.DictionaryId, v.DictionaryId);
                                        html += $.Format('&nbsp;&nbsp;<a href="#" onclick="showCopyForm({0},{1},{2})">复制到</a>', row.DictionaryPid, row.DictionaryId, v.DictionaryId);
                                    }
                                }
                                return html;
                            }
                        });
                    });

                    initGrid(columns);
                }
            }
        });
    }

    function initGrid(column) {
        var queryUrl = '@Url.Content("~/SystemManage/StaticDictionary/GetUnitGradeListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            pagination: false,
            columns: column
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
    }

    //function showTimeSlot(id,gradeId){
    //    ys.openDialog({
    //        title:"设置时段",
    //        height: "600px",
    //        content: '@Url.Content("~/OrganizationManage/CourseSection/UnitScheduleForm")' + '?id=' + gradeId,
    //        callback: function (index, layero) {
    //            var iframeWin = window[layero.find('iframe')[0]['name']];
    //            iframeWin.saveForm(index);
    //        }
    //    });
    //}

    function showTimeSlot(){
        ys.openDialog({
            title: "设置时段",
            width: "800px",
            height:"550px",
            content: '@Url.Content("~/OrganizationManage/CourseSection/UnitScheduleForm")',
                callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }


    function showTimeCopy(id,gradeId,gradeName){
       
        var url = '@Url.Content("~/OrganizationManage/CourseSection/UnitScheduleCopyForm")' + '?id=' + gradeId + '&gradeName=' + gradeName;
        ys.openDialog({
            title: "复制时段",
            width: "800px",
            maxmin: true,
            content: url,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function showSaveForm(id, gradeId, dictionaryId) {

        ys.openDialog({
            title: "设置课程表",
            width: "100%",
            height:'100%',
            content: '@Url.Content("~/OrganizationManage/CourseSection/CourseSectionForm")' + '?gradeId=' + gradeId + "&weekId=" + dictionaryId,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
       
    }

    function showCopyForm(id, gradeId, dictionaryId){
        ys.openDialog({
            title: "复制课程表",
            width: "800px",
            content: '@Url.Content("~/OrganizationManage/CourseSection/CourseSectionCopyForm")' + '?gradeId=' + gradeId + "&weekId=" + dictionaryId,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>


