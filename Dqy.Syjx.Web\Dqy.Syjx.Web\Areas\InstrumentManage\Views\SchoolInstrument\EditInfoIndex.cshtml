﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
    int IsSystem = (int)ViewBag.IsSystem;
    string keyWord = "仪器代码、名称";
    if (IsSystem == 1)
    {
        keyWord = "仪器代码、名称、录入人";
    }
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li id="lischoolid" style="display:none;">
                        <span id="schoolId" col="SchoolId" style="display:inline-block;width:150px;"></span>
                    </li>
                    <li class="select-time">
                        <label>采购日期： </label><input id="startDate" col="StartDate" type="text" class="time-input" placeholder="开始时间" style="display:inline-block;width:100px;" />
                        <span>-</span>
                        <input id="endDate" col="EndDate" type="text" class="time-input" placeholder="结束时间" style="display:inline-block;width:100px;" />
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="varietyAttribute" col="VarietyAttribute" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <div id="storagePlace" col="StoragePlace" style="display:inline-block;"></div>
                    </li>
                    <li>
                        <span id="isSelfMade" col="IsSelfMade" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="@keyWord" style="width:150px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning btn-lg" onclick="exportForm()"><i class="fa fa-download"></i> 导出柜签</a>

            <a id="btnExportA6" class="btn btn-warning btn-sm" onclick="exportA6Form()"><i class="fa fa-download"></i> 导出柜签(小)</a>

            <a id="btnRevoke" class="btn btn-danger disabled" onclick="revokeForm()"><i class="fa fa-remove"></i> 批量撤回</a>
           @* <a id="btnRepair" class="btn btn-danger disabled" onclick="repairForm()"><i class="fa fa-edit"></i> 仪器编码修复</a>*@

            <a id="btnCourse" class="btn btn-primary disabled" onclick="editCourse()"><i class="fa fa-edit"></i> 批量修改学科</a>
            <a id="btnSigned" class="btn btn-info disabled" onclick="editSigned()"><i class="fa fa-edit"></i> 批量签出</a>

            
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var SELECTED_IDS = ''; //选中的行Id

    var zhiBiaoRen = "";
    var zhiBiaoRiQi = "";

    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        $("#isSelfMade").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), defaultName: '自制教具' });
        loadCourse();
        loadStoragePlace();
        loadVarietyAttribute();

        if (@IsSystem == 1) {
            $('#lischoolid').show();
            $('#schoolId').ysComboBox({ url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenPageList")' + "?PageSize=10000", key: 'Id', value: 'Name', defaultName: '学校名称' });
        }
        showSection();

        initGrid();




    });

    function showSection(){
        $('#btnCourse').addClass('disabled');
        $('#btnSigned').addClass('disabled');
        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnRevoke')) {
                $('#btnRevoke').toggleClass('disabled', !ids.length);
                //$('#btnRepair').toggleClass('disabled', !ids.length);
            }
            if ($('#btnCourse')) {
                $('#btnCourse').toggleClass('disabled', !ids.length);
            }
            if ($('#btnSigned')) {
                $('#btnSigned').toggleClass('disabled', !ids.length);
            }
        });
    }

    function initGrid() {
        let isShowTotalRow = 1;
        if (@IsSystem == 1) isShowTotalRow = 0;
        var queryUrl = '@Url.Content("~/InstrumentManage/SchoolInstrument/GetPageListJson")' + '?Statuz=@InstrumentInputStatuzEnum.AlreadyInputStorage.ParseToInt()&ListType=1&IsShowTotalRow=' + isShowTotalRow;
        $('#gridTable').ysTable({
            url: queryUrl,
            //showFooter: true,
            columns: [
                { checkbox: true
                },
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt3, visible: (@IsSystem != 1),
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="editForm(this)" value="{0}" t="1"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                            html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="outForm(this)" value="{0}"><i class="fa fa-edit"></i>签出</a> ', row.Id);
                        }
                        return html;
                    }
                },
                {
                    field: 'SchoolName', title: '学校名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.SchoolName, visible: (@IsSystem == 1),
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Course', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible:false,
                   formatter: function (value, row, index) {
                       if (row.Id) return value;
                       else return '';
                   }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            var html = "";
                            if (row.IsDangerChemical == 1) {
                                html += Syjx.GetDangerHtml();
                            }
                            if (row.IsSelfMade == 1) {
                                html += Syjx.GetSelfMadeHtml();
                            }
                            html += value;
                            return html;
                        }
                        else return '';
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                {
                    field: 'StockNum', title: '数量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '<b>'+ value +'</b>';
                    }
                },  //取剩余库存
                {
                    field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Price', title: '单价', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        if (row.Id) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return '';
                    }
                },
                {
                    field: 'VarietyAttributeName', title: '仪器属性', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.VarietyAttributeName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
               
                {
                    field: 'FunRoom', title: '存放地', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.FunRoom,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            if (row.Cupboard) {
                                value += '>' + row.Cupboard;
                            }
                            if (row.Floor) {
                                value += '>' + row.Floor;
                            }
                            return value;
                        }
                        else return '';
                    }
                },
                {
                    field: 'RealName', title: '录入人', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course, visible: (@IsSystem == 1),
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'PurchaseDate', title: '采购日期', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        if (row.Id) return ys.formatDate(value, "yyyy-MM-dd");
                        else return '';
                    }
                },
                //{
                //    field: 'IsSelfMade', title: '自制教具', halign: 'center', align: 'center', sortable: true, width: 50,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value == 1 ? '是' : '否';
                //        else return '';
                //    }
                //},
                {
                    title: '二维码', halign: 'center', valign: 'middle', align: 'center', width: commonWidth.Instrument.Opt1,
                    formatter: function (value, row, index) {
                        if (row.Id)
                        {
                            var actions = [];
                            if (row.QRCode != undefined && row.QRCode.length > 0) {
                                actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="queryQRCodeForm(\'' + row.Id + '\')"><i class="fa fa-check"></i>查看</a>&nbsp;&nbsp;');
                            } else {
                                actions.push('&nbsp;<a class="btn btn-primary btn-xs" href="#" onclick="createQRCodeForm(\'' + row.Id + '\')"><i class="fa fa-edit"></i>生成</a>&nbsp;');
                            }
                            return actions.join('');
                        }
                        else{
                            return '';
                        }
                       
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                //去除总计行的checkbox
                let rows = $('#gridTable').bootstrapTable('getData');
                if(rows.length>0){
                    if (rows && !rows[rows.length - 1].Id) {
                        //最后一个tr的第一个td数据置空：用数据控制的总计行页面放大缩小会导致失效，应用showFooter控制
                        $('#gridTable tbody').find('tr:last').find('td:first').html('')
                    }
                }

                if ($('#btnBatchSubmit')) {
                    $('#btnBatchSubmit').toggleClass('disabled', !$("#gridTable").bootstrapTable("getSelections").length);
                }

                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function editForm(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/SchoolInstrumentForm")' + '?id=' + id;
        createMenuItem(url, "仪器修改");
    }

    function editCourse(){
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        var ids = ys.getIds(selectedRow);
        SELECTED_IDS = ids;
        ys.openDialog({
            title: '批量修改学科',
            width: '800px',
            height: '400px',
            content: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchEditCourseForm")' + '?isVerify=1',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function editSigned(){
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        var ids = ys.getIds(selectedRow);
        console.log("ids:" + ids);
        SELECTED_IDS = ids;
        ys.openDialog({
            title: '批量签出',
            width: '800px',
            height: '400px',
            content: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchInputRoomForm")'+'?isVerify=1',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function outForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '签出',
            width: '800px',
            height: '400px',
            content: '@Url.Content("~/InstrumentManage/SchoolInstrument/SingleInputRoomForm")' + '?id=' + id,
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });
    }

    function loadStoragePlace() {
        $('#storagePlace').ysComboBoxTree({
            url: '@Url.Content("~/BusinessManage/Cupboard/GetCupboardTreeListAllJson")',
            class: "form-control",
            key: 'id',
            value: 'name',
            defaultName: '存放地'
        });
        $('#storagePlace').ysComboBoxTree('setValue', -1);
    }

    function loadVarietyAttribute() {
        $('#varietyAttribute').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.InstrumentNature.ParseToInt()',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '仪器属性'
        });
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#courseId').ysComboBox('setValue', -1);
        $('#storagePlace').ysComboBoxTree('setValue', -1);
        $('#varietyAttribute').ysComboBox('setValue', -1);
        $("#isSelfMade").ysComboBox('setValue', -1);
        $('#keyWord').val('');
        if (@IsSystem == 1) {
            $('#schoolId').ysComboBox('setValue', -1);
        }
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function exportForm() {
        ys.openDialog({
            title: '制表设置',
            content: '@Url.Content("~/InstrumentManage/SchoolInstrument/TabulationSet")' + '?id=0',
            width: '780px',
            height: '450px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function exportA6Form() {
        ys.openDialog({
            title: '制表设置',
            content: '@Url.Content("~/InstrumentManage/SchoolInstrument/TabulationSet")' + '?id=1',
            width: '780px',
            height: '450px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }



    function exportInfo(){
        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/ExportInstrumentCabCode")' + '?UName=' + zhiBiaoRen + '&UDate=' + zhiBiaoRiQi + '';
        var postData = $("#searchDiv").getWebControls();
        ys.exportExcel(url, postData);
    }

    function exportA6Info() {
        var url = '@Url.Content("~/InstrumentManage/SchoolInstrument/ExportInstrumentCabCodeA6")' + '?UName=' + zhiBiaoRen + '&UDate=' + zhiBiaoRiQi + '';
        var postData = $("#searchDiv").getWebControls();
        ys.exportExcel(url, postData);
    }

   
    function revokeForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
            ys.confirm('您确认要批量撤回所选数据吗，请确认已与用户核实？', function () {
                ys.ajax({
                    url: '@Url.Content("~/InstrumentManage/SchoolInstrument/RevokeJson")',
                    type: 'post',
                    data: { Ids: ids },
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    //生成二维码
    function createQRCodeForm(id) {
        var ids = '';
        var msg = '';
        if (id != 0) {
            ids = id;
            msg = '确认要生成该条数据的二维码吗？';
        } else {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (ys.checkRowDelete(selectedRow)) {
                ids = ys.getIds(selectedRow);
                msg = ('确认要生成选中的' + selectedRow.length + '条数据的二维码吗？');
            } else {
                ys.msgError('请选择需要生成二维码的数据！');
                return;
            }
        }
        ys.confirm(msg, function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/SchoolInstrument/CreateQRCodeFormJson")' + '?ids=' + ids,
                type: 'post',
                headers: {
                         'Authorization': 'Bearer tokentokentokentokentokentokentokentoken'
                          },
                success: function (obj) {
                    if (obj.Tag == 1) {
                        console.log("11111111111111111111")
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });

        });
    }

    //查看二维码
    function queryQRCodeForm(id) {
        ys.openDialog({
            title: '二维码查看',
            content: '@Url.Content("~/InstrumentManage/SchoolInstrument/QRCodeForm")' + '?id=' + id,
            width: '450px',
            height: '450px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }


    /**
     *
     *修复仪器编码
     */
    function repairForm() {
        ys.ajax({
            url: '@Url.Content("~/InstrumentManage/SchoolInstrument/BatchEditCode")',
            type: 'post',
            success: function (obj) {
                if (obj.Tag == 1) {
                    ys.msgSuccess(obj.Message);
                }
                else {
                    ys.msgError(obj.Message);
                }
            }
        });
    }
</script>