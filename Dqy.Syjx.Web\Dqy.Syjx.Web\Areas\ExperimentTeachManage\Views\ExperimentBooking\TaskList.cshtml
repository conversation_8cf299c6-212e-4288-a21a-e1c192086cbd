﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    @*<div class="row" style="height:auto;">
        <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-down"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            1、选择“实验来源”，优先选择“实验计划”，如无实验计划则选择“实验目录”；<br/>
                            2、如需多个班级或多个实验同时预约，请到【预约填报】栏中进行预约。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="sourceType" col="SourceType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:80px;"></span>
                    </li>
                    <li>
                        <span id="GradeId" col="GradeId" style="display:inline-block;width:110px;"></span>
                    </li>
                    @*<li>
                            <span id="schoolGradeClassId" col="SchoolGradeClassId" style="display:inline-block;width:110px;"></span>
                        </li>*@
                    <li>
                        <div id="ExperimentVersionId" col="ExperimentVersionId" style="display: inline-block;width:200px;"></div>
                        <input id="SourcePath" col="SourcePath" type="hidden" />
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="experimentType" col="ExperimentType" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="IsNeedDo" col="IsNeedDo" style="display: inline-block; width: 100px;"></span>
                    </li>
                    <li>
                        <span id="IsFinish" col="IsFinish" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="experimentName" col="ExperimentName" style="display:inline-block;width:180px;" placeholder="实验名称" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadSearchCombo();

        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetTaskListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Statuz > -1) {
                            html = '--';
                        } else {
                            html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="confiirmBooking(this,\'{0}\',\'{1}\')"><i class="fa fa-edit"></i>预约</a> ', row.Id, row.SchoolGradeClassId);
                        }
                        return html;
                    }
                },
                {
                    field: 'SchoolTerm', title: '学期', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        var html = "@SchoolTermEnum.LastSemester.GetDescription()";
                        if (value == @SchoolTermEnum.NextSemester.ParseToInt()) {
                            html = "@SchoolTermEnum.NextSemester.GetDescription()";
                        }
                        return html;
                    }
                },
                { field: 'GradeName', title: '年级', sortable: true, halign: 'center', align: 'center', width: 50 },
                { field: 'ClassDesc', title: '班级', sortable: true, halign: 'center', align: 'center', width: 50 ,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (value != undefined && value.length > 0) {
                            html = '（' + value + '）';
                        }
                        return html;
                    }
                },
                { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', width: 50 },
                {
                    field: 'SourceType', title: '实验来源', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @SourceTypeEnum.ExperimentPlan.ParseToInt():
                                return "@SourceTypeEnum.ExperimentPlan.GetDescription()";
                             case @SourceTypeEnum.ExperimentList.ParseToInt():
                                return "@SourceTypeEnum.ExperimentList.GetDescription()";
                            default:
                                return '实验计划/实验目录';
                        }
                    }
                },
                { field: 'VersionName', title: '实验教材版本', sortable: true, halign: 'center', align: 'left', width: 140  },
                { field: 'ExperimentName', title: '实验名称', sortable: true, halign: 'center', align: 'left', width: 140 },
                {
                    field: 'ExperimentType', title: '实验类型', sortable: true, halign: 'center', align: 'center', width: 50,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @ExperimentTypeEnum.Demo.ParseToInt():
                                return "@ExperimentTypeEnum.Demo.GetDescription()";
                             case @ExperimentTypeEnum.Group.ParseToInt():
                                return "@ExperimentTypeEnum.Group.GetDescription()";
                            default:
                                return '演示/分组';
                        }
                    }
                },
                {
                    field: 'IsNeedDo', title: '实验要求', sortable: true, halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '@IsNeedEnum.MustDo.GetDescription()';
                        if (@IsNeedEnum.SelectToDo.ParseToInt()== value) {
                            html = '@IsNeedEnum.SelectToDo.GetDescription()';
                        }
                        return html;
                    }
                },
                {
                    field: 'Statuz', title: '是否完成', sortable: true, halign: 'center', align: 'center', width: 80,
                    formatter: function (value, row, index) {
                        var html = '--';
                        if (row.Statuz > @ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()) {
                            html = '是';
                        } else {
                            html = '否';
                        }
                        return html;
                    }
                },
                {
                    field: 'ArrangerTime', title: '完成时间', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Statuz > @ExperimentBookStatuzEnum.ArrangeNoPass.ParseToInt()) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                }
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    function resetGrid() {
        //清空条件
        $('#sourceType').ysComboBox('setValue', -1);
        $('#schoolTerm').ysComboBox('setValue', -1);
        $('#GradeId').ysComboBox('setValue', -1);
        /*        $('#schoolGradeClassId').ysComboBox('setValue', -1);*/
        $('#ExperimentVersionId').ysComboBox('setValue', -1);
        $('#SourcePath').val('');
        $('#courseId').ysComboBox('setValue', -1);
        $('#experimentType').ysComboBox('setValue', -1);
        $('#IsNeedDo').ysComboBox('setValue', -1);
        $('#IsFinish').ysComboBox('setValue', -1);
        $('#experimentName').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    /**加载搜索条件*/
    function loadSearchCombo() {
        var sourceTypeArr = ys.getJson(@Html.Raw(typeof(SourceTypeEnum).EnumToDictionaryString()));
        $("#sourceType").ysComboBox({ data: sourceTypeArr, defaultName: '实验来源' });
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetChildToListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&Ids=1005002,1005003,1005004,1005005',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '学科'
        });

        $("#experimentType").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperimentTypeEnum).EnumToDictionaryString())), defaultName: '实验类型' });
        $("#IsNeedDo").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsNeedEnum).EnumToDictionaryString())), defaultName: '实验要求' });
        $("#IsFinish").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())), defaultName: '是否完成' });

        @*ys.ajax({
            url: '@Url.Content("~/OrganizationManage/SchoolGradeClass/GetListJson")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#schoolGradeClassId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'ClassName',
                        defaultName: '班级'
                    });
                }
            }
        });*@
        loadGrade();
        loadVersionBase();
    }

    function loadGrade() {
        ys.ajax({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1002",
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var pids = '';
                    $.each(obj.Data, function (i, n) {
                        if (i > 0) {
                            pids += ',';
                        }
                        pids += n.DictionaryId;
                    });
                    $('#GradeId').ysComboBox({
                        url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + "?TypeCode=1003&Pids=" + pids,
                      /*  class: 'form-control col-sm-7',*/
                        key: 'DictionaryId',
                        value: 'DicName',
                        defaultName: '年级',
                        onChange: function () {
                            @*var selectid = $('#GradeId').ysComboBox('getValue');
                            loadCourse(selectid);
                            if (parseInt(selectid) >=  @GradeEnum.GaoYi.ParseToInt()) {
                                $("#divClass").show();
                                loadClass(selectid)
                            } else {
                                $("#divClass").hide();
                            }*@
                        }
                    });
                }
            }
        });
    }
    function loadVersionBase() {
        var schoolterm = 0;
        if (parseInt($('#SchoolTerm').ysComboBox("getValue")) > 0) {
            schoolterm = parseInt($('#SchoolTerm').ysComboBox("getValue"));
        }
        var startyear = 0;
        if (parseInt($('#schoolYearStart').ysComboBox("getValue")) > 0) {
            startyear = parseInt($('#schoolYearStart').ysComboBox("getValue"));
        }
        var gradeid = 0;
        if (parseInt($('#GradeId').ysComboBox("getValue")) > 0) {
            startyear = parseInt($('#GradeId').ysComboBox("getValue"));
        }
        var courseid = 0;
        if (parseInt($('#courseId').ysComboBox("getValue")) > 0) {
            startyear = parseInt($('#courseId').ysComboBox("getValue"));
        }
         ys.ajax({
             url: '@Url.Content("~/ExperimentTeachManage/PlanDetail/GetVersionListJson")' + '?SchoolYearStart=' + startyear + '&SchoolTerm=' + schoolterm + '&GradeId=' + gradeid + '&CourseId' + courseid,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    var arrData = [{ VersionName: "校本实验", Id: 99 }];
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        arrData = arrData.concat(obj.Data);
                    }
                    $('#ExperimentVersionId').ysComboBox({
                        data: arrData,
                        key: 'Id',
                        value: 'VersionName',
                        defaultName: '实验教材版本'
                        ,onChange: function () {
                             //判断是否是校本实验
                            var tempbaseid = $('#ExperimentVersionId').ysComboBox("getValue");
                            $("#SourcePath").val(0);
                            if (tempbaseid != undefined && parseFloat(tempbaseid)> 0) {
                                if (tempbaseid == 99) {
                                    $("#SourcePath").val(2);
                                } else {
                                    $("#SourcePath").val(1);
                                }
                            }
                        }
                    });
                }
            }
        });
    }

    function confiirmBooking(obj, id, taskgradeclassid) {
        ys.confirm('您确认要预约吗？', function () {
            var url = '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/Form")' + '?taskid=' + id + '&taskgradeclassid=' + taskgradeclassid;
            createMenuItem(url, "任务预约填报");
        });
    }
</script>
