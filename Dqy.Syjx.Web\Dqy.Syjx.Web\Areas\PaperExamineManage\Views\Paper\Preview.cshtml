﻿<!--登录页和首页模板-->
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment HostingEnvironment
@{
    var loginCfg = await Dqy.Syjx.Web.CommonLib.Configs.GetWebTitle();
}
<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
    <meta http-equiv="Cache-Control" content="no-siteapp" />

    <meta name="keywords" content="@loginCfg">
    <meta name="description" content="@loginCfg">

    <link rel="bookmark" href='@Url.Content("~/favicon.ico")' />
    <link rel="shortcut icon" href="@Url.Content("~/favicon.ico")" type="image/x-icon" />

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap/css/bootstrap.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/fontawesome/4.7.0/css/fontawesome.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery/3.7.1/jquery.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/bootstrap/js/bootstrap.min.js"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/layer/3.1.1/layer.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/style.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha.min.js"))

    <!--首页需要用到的js开始-->
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha-index.min.js"))
    <script type="text/javascript" src='@Url.Content("~/lib/jquery.metisMenu/1.1.3/metisMenu.js")'></script>
    <script type="text/javascript" src='@Url.Content("~/lib/jquery.slimscroll/1.3.8/jquery.slimscroll.min.js")'></script>

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/skins/custom.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/icheck/1.0.2/icheck.min.js"))
    @*@Dqy.Syjx.Util.BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.css"))
        @Dqy.Syjx.Util.BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/uploadifive/File.min.js"))*@

    <!--首页需要用到的js结束-->
<title>@loginCfg</title>
    <style type="text/css">
        .nav > li:hover .dropdown-menu {
            display: block;
        }
    </style>
    <script type="text/javascript">
        var ctx = '@Url.Content("~/")';
    </script>
</head>
<body class="fixed-sidebar full-height-layout gray-bg login">
    <style type="text/css">
        .tableoption thead tr td {
            background-color: #e8e8e8;
            padding: 3px 5px;
            font-size: 16px;
        }

        .tableoption tr {
            height: 46px;
            text-align: left;
            display: table-row;
            vertical-align: inherit;
            border-color: inherit;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .choicetxt {
            height: 24px;
            padding: 2px 6px;
            font-size: 14px;
        }

        .option-icon {
            height: 30px;
            cursor: pointer;
        }

        .preview-namedesc {
            color: gray;
            padding-left: 25px;
        }

        .preview-picture {
            max-width: 400px;
            max-height: 400px;
            display: flex;
            padding-left: 25px;
        }

            .preview-picture img {
                width: auto;
                height: auto;
                max-width: 100%;
                max-height: 100%;
            }
    </style>
    <div class="exterior__chapternav text-center" style="width: 100%; left: 0px; position: fixed; top: 0; z-index: 1; height: 70px; background-color: #fff;">
        <div class="exterior__inner">
            <div class="left-preview-tip">
                <em>提示：</em>
                <span>此为预览页面，不能参与作答！</span>
            </div>
            <div class="exterior__switch wjx-inlineBlock" style="left: 50%; margin-left: -133px;width:266px;">
                @*<div class="exterior__switch-item chapternavitem-phone pull-left wjx-inlineBlock">
                        <a href="previewmobile.aspx?activity=176623899" id="pre-set-link">
                            <div class="iconfont-boxer"><i class="iconfont"></i></div>

                            <h5>手机预览</h5>
                        </a>
                    </div>*@
                <div class="exterior__separator separator__reset-Lt separator__reset-Rt pull-left"></div>
                <div class="exterior__switch-item chapternavitem-pc current pull-left wjx-inlineBlock">
                    <a href="javascript:void(0)">
                        <div class="iconfont-boxer"><i class="iconfont"></i></div>
                        <h5>电脑预览</h5>
                    </a>
                </div>
                <div class="exterior__switch-item chapternavitem-close pull-left wjx-inlineBlock">
                    <a href="javascript:void(0)" onclick="closeWin();">
                        <div class="iconfont-boxer"><i class="iconfont"></i></div>
                        <h5>关闭预览</h5>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="container-div" style="margin-top:90px;">
        <div class="col-sm-12 search-collapse" id="toolbar" style="padding-bottom: 100px; width: 1100px; margin-left: calc(50% - 550px);">
            <div id="paperTitle" style="text-align: center; padding: 20px;"><h1></h1></div>
            <div id="questions" style="padding: 50px;">

            </div>
            <div style="text-align: center;">
                <a class="btn btn-success" style="font-size: 16px; width: 100px;"><i class="fa fa-save"></i> 提交</a>
            </div>
        </div>
    </div>

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/css/yisha.min.css"))
    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/yisha/js/yisha-init.min.js"))

    @BundlerHelper.Render(HostingEnvironment.ContentRootPath, Url.Content("~/lib/jquery.string.format/jquery.string.format.min.js"))
    <script src='@Url.Content("~/junfei/js/CommonPaper.js")' type="text/javascript"></script>
    <script type="text/javascript">
        var id = ys.request("id");//试卷Id
        $(function () {
            getForm();
        });

        function getForm() {
            if (id > 0) {
                ys.ajax({
                    url: '@Url.Content("~/PaperExamineManage/Paper/GePreviewJson")' + '?id=' + id,
                    type: 'get',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            if (obj.Data != undefined) {
                                //添加试卷标题。
                                $("#paperTitle h1").html(obj.Data.PaperName);


                                if (obj.Data.QuestionList != undefined && obj.Data.QuestionList.length>0) {
                                    $.each(obj.Data.QuestionList, function (index, item) {
                                        loadPreviewQuestion(index + 1, item);
                                    });
                                }

                            }
                        }
                    }
                });
            }
            else {
                $("#questions").html("<span>参数不对，请从页面点击操作。</span>");
            }
        }

        //#region 预览效果

        function loadPreviewQuestion(number,jsondata) {
            var html = '';
            var checkboxTag = '';
            if (jsondata.QuestionTypeNature==2) {
                checkboxTag = '【多选】';
            }
            html += '<div style="line-height: 20px; letter-spacing: 2px; padding: 10px;">';
            html += $.Format(' <span style="font-size:15px;text-wrap:normal;font-weight:bold;">{0}、{1} {2}</span><br />', number, jsondata.Title, checkboxTag);
            html += $.Format('<div id="title-Picture">{0}</div>', getQuestionPicture(jsondata.PicturePathList));
            html += $.Format('<div style="padding:10px 5px;">{0}</div>', getOptions(number, jsondata.QuestionOptionList, jsondata.QuestionTypeNature));
            html += '</div>';
            $("#questions").append(html);
        }

        function getQuestionPicture(jsondata) {
            var html = '';
            if (jsondata != undefined && jsondata.length > 0) {
            for (var i = 0; i < jsondata.length; i++) {
                var thisPath = jsondata[i];
                if (thisPath != undefined && thisPath.length > 0) {
                    html += $.Format('<div class="preview-picture" style="display: inline-block;"><img src="{0}" /></div>', thisPath);
                }
                }
            }
            return html;
        }

        function getOptions(number,jsondata, nature) {
            var html = '';
            //加载选项
            if (jsondata != undefined && jsondata.length > 0) {
                for (var i = 0; i < jsondata.length; i++) {
                    var optionTag = getOptionTag(i);
                    var optionmsg = jsondata[i].Name;
                    if (!(optionmsg != undefined && optionmsg.length > 0)) {
                        optionmsg = '';
                    }
                    html += '<div class="preOption" style="padding-left:25px;padding-bottom: 10px;">';
                    if (nature == 1) {
                        html += '<label class="radio-box" style="padding-left:0px;">';
                        html += $.Format('<input type="radio" name="previewradiobox_{0}" ref="undefined">', number);
                        html += $.Format('&nbsp;&nbsp;{0}：{1}</label>', optionTag, optionmsg);
                        html += '</label>';
                    } else {
                        html += '<label class="check-box">';
                        html += '<div class="icheckbox-blue" onclick="previewcheckbox(this);"><input name="previewcheckbox" type="checkbox" style="position: absolute; opacity: 0;"></div>';
                        html += $.Format('{0}：{1}</label>', optionTag, optionmsg);
                    }
                    if (jsondata[i].PicturePath != undefined && jsondata[i].PicturePath.length > 0) {
                        html += $.Format('<div class="preview-picture"><img src="{0}"/></div>', jsondata[i].PicturePath);
                    }
                    if (jsondata[i].NameDesc != undefined && jsondata[i].NameDesc.length > 0) {
                        html += $.Format('<div class="preview-namedesc">（注：{0}）</div>', jsondata[i].NameDesc);
                    }
                    html += '</div>';
                }
            }
            return html;
        }

        //获取字母
        function getOptionTag(index) {
            var html = '';
            var strArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            if (index > 25) {
                var thisIndex = index % 25;
                var thisNum = index / 25;
                html = (strArr[thisIndex] + '' + thisNum);
            } else {
                html = strArr[index];
            }
            return html;
        }

        function previewcheckbox(obj) {
            var classstr = $(obj).attr('class');
            if (classstr != undefined && classstr.length > 0) {
                if (classstr.indexOf('checked') > -1) {
                    $(obj).removeClass("checked");
                } else {
                    $(obj).addClass('checked');
                }
            }
        }

        //#endregion

        function myfunction() {
            $('.close').on('click', function () {
                if (confirm("您确定要关闭本页吗？")) {
                    window.opener = null;
                    window.open('', '_self');
                    window.close();
                } else { }
            })
        }
    </script>
</body>
</html>

