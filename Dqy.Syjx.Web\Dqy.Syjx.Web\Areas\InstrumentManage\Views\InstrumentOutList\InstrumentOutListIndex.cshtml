﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>出库时间： </label><input id="startDate" col="StartDate" type="text" class="time-input" placeholder="开始时间" style="width:100px;" />
                        <span>-</span>
                        <input id="endDate" col="EndDate" type="text" class="time-input" placeholder="结束时间" style="width:100px;" />
                    </li>
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <span id="receiveUserId" col="ReceiveUserId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="outUserId" col="OutUserId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="width:150px;" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar" style="text-decoration:">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed'});
        loadCourse();
        loadUser();
        initGrid();
        
        
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/InstrumentOutList/GetPageListJson")' + '?IsShowTotalRow=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                //{
                //    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value;
                //        else return '<b>总计：</b>';
                //    }
                //},
                {
                    field: 'opt', title: '操作', halign: 'center', align: 'center', width: commonWidth.Instrument.Opt2,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id)
                            html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="backForm(this)" value="{0}"><i class="fa fa-edit"></i>退回</a> ', row.Id);
                        return html;
                    }
                },
                {
                    field: 'Course', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            var html = "";
                            if (row.IsDangerChemical == 1) {
                                html += Syjx.GetDangerHtml();
                            }
                            if (row.IsSelfMade == 1) {
                                html += Syjx.GetSelfMadeHtml();
                            }
                            html += value;
                            return html;
                        }
                        else return '';
                    }
                },
                {
                    field: 'Model', title: '规格属性', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                {
                    field: 'InitialOutNum', title: '领用量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'BackNum', title: '退回量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            if (value > 0)
                                return '<a href="javascript:void(0);" title="点击查看退回记录" onclick="lookBackRecordForm(this);" value="' + row.Id + '">' + value + '</a>';
                            else
                                return value
                        }
                        else return '';
                    }
                },
                {
                    field: 'OutNum', title: '出库量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '<b>' + value + '</b>';
                    }
                },
                {
                    field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Price', title: '单价', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        if (row.Id) return value > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return '';
                    }
                },
                {
                    field: 'Sum', title: '金额', halign: 'center', align: 'right', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        if (row.Id) return row.Price > 0 ? ComBox.ToLocaleString(value) : '-';
                        else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                    }
                },
               
                {
                    field: 'ReceiveUserName', title: '领用人', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UserName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'OutDate', title: '出库时间', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
                {
                    field: 'OutUserName', title: '登记人', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UserName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Content', title: '用途', halign: 'center', align: 'left', width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function backForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '退回',
            content: '@Url.Content("~/InstrumentManage/InstrumentOutList/InstrumentBackForm")' + '?id=' + id,
            width: '768px',
            height: '350px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function lookContentForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '查看用途',
            content: '@Url.Content("~/InstrumentManage/InstrumentOutList/LookOutContentForm")' + '?id=' + id,
            width: '768px',
            height: '350px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function loadCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });
    }

    function loadUser() {
        $('#outUserId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '登记人'
        });
        $('#receiveUserId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '领用人'
        });
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#courseId').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#outUserId').ysComboBox('setValue', -1);
        $('#receiveUserId').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function lookBackRecordForm(obj) {
        var id = $(obj).attr('value');
        ys.openDialog({
            title: '退回记录',
            content: '@Url.Content("~/InstrumentManage/InstrumentOutList/BackListForm")' + '?id=' + id,
            width: '550px',
            height: '350px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }
</script>
