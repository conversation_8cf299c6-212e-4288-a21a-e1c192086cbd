﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
   @* <div class="row" style="height:auto;">
        <div class="ibox float-e-margins" style="margin-bottom:0px;">
            <div class="ibox-title">
                <h5 class="table-tswz">友情提示</h5>
                <div class="ibox-tools">
                    <a class="collapse-link">
                        <i class="fa fa-chevron-up"></i>
                    </a>
                </div>
            </div>
            <div class="ibox-content" style="padding:0px;display:none;">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="card-body table-tswz">
                            按达标填报是指按照仪器达标缺口数据进行填报。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>*@

    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>填报日期： </label><input id="startDate" col="StartDate" type="text" class="time-input" placeholder="开始时间" style="width:100px" />
                        <span>-</span>
                        <input id="endDate" col="EndDate" type="text" class="time-input" placeholder="结束时间" style="width:100px" />
                    </li>
                    @await Html.PartialAsync("/Areas/InstrumentManage/Shared/DeclarationSearchPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    @await Html.PartialAsync("/Areas/InstrumentManage/Shared/DeclaretionStatuzPartial.cshtml", new ViewDataDictionary(this.ViewData) { })
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn" ></i>
                    </li>
                </ul>
            </div>
        </div>

        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
            @*<a id="btnAdd" class="btn btn-success" onclick="showAddForm();"><i class="fa fa-plus"></i> 日常填报</a>
            <a id="btnStandardadd" class="btn btn-success" onclick="showStandardForm();"><i class="fa fa-plus"></i> 按达标填报</a>
            <a id="btnBatchSubmit" class="btn btn-primary disabled" onclick="batchSubmit();"><i class="fa fa-check"></i> 批量提交</a>*@
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>

<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>


<script type="text/javascript">
    var BasePageCode = 101015;//仪器已报计划(101015)
    $(function () {
        laydate.render({ elem: '#startDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        laydate.render({ elem: '#endDate', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });

        initGrid();

        $("#gridTable").on("check.bs.table uncheck.bs.table check-all.bs.table uncheck-all.bs.table", function () {
            var ids = $("#gridTable").bootstrapTable("getSelections");
            if ($('#btnBatchSubmit')) {
                $('#btnBatchSubmit').toggleClass('disabled', !ids.length);
            }
        });

        
        

    });
    var sum = 0;

    function initGrid() {
        var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetPageListJson")' + '?IsShowTotalRow=1&IsReport=1';
        $('#gridTable').ysTable({
            url: queryUrl,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                {
                    field: 'opt', title: '操作', align: 'center', halign: 'center', width: commonWidth.Instrument.Opt2,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            //if (row.Statuz == @InstrumentAuditStatuzEnum.DeclareIng.ParseToInt() || row.IsGoBack == @IsEnum.Yes.ParseToInt()) {
                            //    html += $.Format('<a class="btn btn-success btn-xs" href="#" onclick="edit(this)" value="{0}"><i class="fa fa-edit"></i>修改</a> ', row.Id);
                            //    html += $.Format('<a class="btn btn-danger btn-xs" href="#" onclick="del(this)" value="{0}"><i class="fa fa-remove"></i>删除</a> ', row.Id);
                            //    html += $.Format('<a class="btn btn-primary btn-xs" href="#" onclick="goNext(this)" value="{0}"><i class="fa fa-save"></i>转交下一步</a> ', row.Id);
                            //}else
                            if (row.Statuz == @InstrumentAuditStatuzEnum.WaitSchoolAudit.ParseToInt()) {
                                html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                                html += $.Format('<a class="btn btn-warning btn-xs" href="#" onclick="withdraw(this)" value="{0}"><i class="fa fa-reply"></i>撤回</a> ', row.Id);
                            }
                            else {
                                html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="look(this)" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                            }
                        }
                        return html;
                    }
                },
                //{ checkbox: true },
                //{
                //    field: 'PurchaseYear', title: '采购年度', align: 'center', halign: 'center', sortable: true, width: 50,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value;
                //        else return '';
                //    }
                //},
                //{
                //    field: 'InstrumentClassName', title: '仪器类别', align: 'left', halign: 'center', sortable: true, width: 140 ,
                //    formatter: function (value, row, index) {
                //        if (row.Id) return value;
                //        else return "<b>总计（元）：</b>";
                //    }
                //},
                
                {
                    field: 'Course', title: '适用学科', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Course,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Code', title: '分类代码', halign: 'center', align: 'center', sortable: true, width: 80, visible: false,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Name', title: '仪器名称', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Name,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            var html = "";
                            if (row.IsDangerChemical == 1) {
                                html += Syjx.GetDangerHtml();
                            }
                            html += value;
                            return html;
                        }
                        else return '';
                    }
                },
                {
                    field: 'Model', title: '规格属性', align: 'left', halign: 'center', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                {
                    field: 'Num', title: '数量', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'UnitName', title: '单位', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.UnitName,
                    formatter: function (value, row, index) {
                        if (row.Id) return value;
                        else return '';
                    }
                },
                {
                    field: 'Price', title: '单价', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.Price,
                    formatter: function (value, row, index) {
                        if (value) return ComBox.ToLocaleString(value);
                        else return '';
                    }
                },
                {
                    field: 'AmountSum', title: '金额', align: 'right', halign: 'center', sortable: true, width: commonWidth.Instrument.AmountSum,
                    formatter: function (value, row, index) {
                        if (row.Id) return ComBox.ToLocaleString(value);
                        else return '<b>' + ComBox.ToLocaleString(value) + '</b>';
                    }
                },
                {
                    field: 'Statuz', title: '计划状态', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.Statuz,
                    formatter: function (value, row, index) {
                        if (row.Id) {
                            if (row.Statuz % 2 == 1)
                                return '<span style="color:red">' + row.StatuzDesc + '</span>';
                            else
                                return row.StatuzDesc;
                        }
                        else return '';
                    }
                },
                {
                    field: 'BaseCreateTime', title: '填报日期', align: 'center', halign: 'center', sortable: true, width: commonWidth.Instrument.BaseCreateTime,
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                }
                
               
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                //去除总计行的checkbox
                let rows = $('#gridTable').bootstrapTable('getData');
                if (rows.length > 0) {
                    if (rows && !rows[rows.length - 1].Id) {
                        //最后一个tr的第一个td数据置空：用数据控制的总计行页面放大缩小会导致失效，应用showFooter控制
                        $('#gridTable tbody').find('tr:last').find('td:first').html('')
                    }
                }

                if ($('#btnBatchSubmit')) {
                    $('#btnBatchSubmit').toggleClass('disabled', !$("#gridTable").bootstrapTable("getSelections").length);
                }

                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showAddForm() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/PurchaseDeclarationForm")';
        createMenuItem(url, "日常填报");
    }

    function showStandardForm() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/InstrumentStandardIndex")';
        createMenuItem(url, "按达标填报");
    }

    function edit(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/PurchaseDeclarationForm")' + '?id=' + id;
        createMenuItem(url, "填报修改");
    }

    function del(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要删除该数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/DeleteFormJson")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function look(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/PurchaseAudit/PurchaseDetail")' + '?id=' + id;
        //createMenuItem(url, "查看计划");
        ys.openDialog({
            title: "查看计划",
            width: "800px",
            content: url,
            btn: ['关闭'],
            yes: function (index) {
                $.modal.close(index);
            },
        });
    }

    function goNext(obj) {
         var id = $(obj).attr('value');
        ys.confirm('确认要转交下一步吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GoNext")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function batchSubmit() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length > 0) {
            var ids = ys.getIds(selectedRow);
            ys.confirm('确认要批量提交吗？', function () {
                ys.ajax({
                    url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/BatchGoNext")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }

    function withdraw(obj) {
        var id = $(obj).attr('value');
        ys.confirm('确认要撤回吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/InstrumentManage/PurchaseDeclaration/Withdraw")' + '?id=' + id,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function resetGrid() {
        //清空条件
        $('#startDate').val('');
        $('#endDate').val('');
        $('#purchaseYear').ysComboBox('setValue', -1);
        $('#instrumentClassId').ysComboBox('setValue', -1);
        $('#courseId').ysComboBox('setValue', -1);
        $('#statuz').ysComboBox('setValue', -1);
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }
    
    //导出已报计划
    function exportForm() {
        var url = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/ExportPurchaseDeclaration")';//仪器已报计划(101015)
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = BasePageCode;//仪器已报计划(101015)
        ys.exportExcel(url, postData);
    }
</script>
