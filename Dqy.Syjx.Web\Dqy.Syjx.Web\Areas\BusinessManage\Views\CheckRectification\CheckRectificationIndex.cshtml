﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
 }
<div class="container-div">
    <div class="row">
       @* <div class="row" style="height:auto;">
            <div class="ibox float-e-margins border-bottom" style="margin-bottom:0px;">
                <div class="ibox-title">
                    <h5 class="table-tswz">友情提示</h5>
                    <div class="ibox-tools">
                        <a class="collapse-link">
                            <i class="fa fa-chevron-down"></i>
                        </a>
                    </div>
                </div>
                <div class="ibox-content" style="padding:0px;display:none;">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card-body table-tswz">
                                点击【填报】添加排查记录
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>*@

        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li class="select-time">
                        <label>日期： </label>
                        <input id="startTime" col="StartTime" type="text" class="time-input" placeholder="开始时间" />
                        <span>至</span>
                        <input id="endTime" col="EndTime" type="text" class="time-input" placeholder="结束时间" />
                    </li>
                   @* <li>
                        <div id="DictionaryId1006A" col="DictionaryId1006A" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="DictionaryId1006B" col="DictionaryId1006B" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="RoomAttribute" col="RoomAttribute" style="display: inline-block;width:100px;"></div>
                    </li>*@
                    <li>
                        <div id="CheckUserId" col="CheckUserId" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="CheckResult" col="CheckResult" style="display: inline-block;width:100px;"></div>
                    </li>
                    <li>
                        <div id="IsRectification" col="IsRectification" style="display: inline-block;width:100px;"></div>
                    </li>
                    @*<li>
                        <input id="Name" col="Name" type="text" placeholder="名称" style="width:120px;" />
                    </li>*@

                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a id="btnCancle" class="btn btn-secondary btn-sm" onclick="clearGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>



        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnAdd" class="btn btn-success" onclick="showSaveForm(true)"><i class="fa fa-plus"></i> 填报</a>
            <a id="btnEdit" class="btn btn-primary disabled" onclick="showSaveForm(false)"><i class="fa fa-edit"></i> 修改</a>
            <a id="btnDelete" class="btn btn-danger disabled" onclick="deleteForm()"><i class="fa fa-remove"></i> 删除</a>
            <a id="btnExport" href='@Url.Content("~/template/实验室安全排查清单.xlsx")' class="btn btn-success"><i class="fa fa-download"></i>下载排查清单</a>
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-upload"></i>导出</a>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var BasePageCode = 101004;
    $(function () {
        laydate.render({ elem: '#startTime', format: 'yyyy-MM-dd', trigger: 'click',position: 'fixed' });
        laydate.render({ elem: '#endTime', format: 'yyyy-MM-dd', trigger: 'click', position: 'fixed' });
        
        
        $("#DictionaryId1006A").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '一级分类',
            onChange: function () {
                var dicA = $("#DictionaryId1006A").ysComboBox("getValue");
                loadDictionaryId1006B(dicA);
            }
        });


        $("#CheckResult").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(CheckResultEnum).EnumToDictionaryString())),
            defaultName: '排查结果',
        });

        $("#IsRectification").ysComboBox({
            data: ys.getJson(@Html.Raw(typeof(IsEnum).EnumToDictionaryString())),
            defaultName: '是否整改',
        });

        loadUserList();
        loadDictionaryId1006B(0);
        loadNature();

        initGrid();

        $(".select2-container").width("100%");
        $(".select-list")
    });

    function clearGrid() {
        $("#DictionaryId1006A").ysComboBox('setValue', -1);
        $("#DictionaryId1006B").ysComboBox('setValue', -1);
        $('#RoomAttribute').ysComboBox('setValue', -1);
        $('#CheckUserId').ysComboBox('setValue', -1);
        $('#CheckResult').ysComboBox('setValue', -1);
        $('#IsRectification').ysComboBox('setValue', -1);
        $("#Name").val("");
        $("#startTime").val("");
        $("#endTime").val("");
        $('#gridTable').ysTable('search');
        $(".select2-container").width("100%");
        resetToolbarStatus();
    }

     function loadUserList() {
        ys.ajax({
            url: '@Url.Content("~/OrganizationManage/User/GetUserListByRoleId?RoleId=16508640061130155")',
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1) {
                    $('#CheckUserId').ysComboBox({
                        data: obj.Data, key: 'Id', value: 'RealName',
                        defaultName: '排查人',
                        
                    });
                } else {
                    $('#CheckUserId').ysComboBox({
                        data: [], key: 'Id', value: 'RealName',
                        defaultName: '排查人',
                    });
                    ys.msgError(obj.Message);
                }
                $(".select2-container").width("100%");
            }
        });
    }

     function loadDictionaryId1006B(dicA) {
        if (parseInt(dicA) > 0) {
            $("#DictionaryId1006B").ysComboBox({
                url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1006&OptType=5' + '&Pid=' + dicA,
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
                
            });
        } else {
            $("#DictionaryId1006B").ysComboBox({
                data: [],
                key: 'DictionaryId',
                value: 'DicName',
                defaultName: '二级分类',
            });
         }
         $(".select2-container").width("100%");
    }

    function loadNature() {
        $("#RoomAttribute").ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetList2Json")' + '?TypeCode=1009',
            key: 'DictionaryId',
            value: 'DicName',
            defaultName: '属性',
        });
    }

    function initGrid() {
        var queryUrl = '@Url.Content("~/BusinessManage/CheckRectification/GetCheckRectificationList")';
        $('#gridTable').ysTable({
            url: queryUrl,
            showExportSetBtn : true,
            showExportSetCode: BasePageCode,
            columns: [
                { checkbox: true, visible: true },
                //{ field: 'ClassNameA', title: '一级分类', width: 120, sortable: true, halign: 'center', align: 'center',},
                //{ field: 'ClassNameB', title: '二级分类', width: 120, sortable: true, halign: 'center', align: 'center', },
                //{ field: 'Name', title: '名称', width: 120, sortable: true, halign: 'center', align: 'left', },
                //{ field: 'NatureName', title: '属性', width: 100, sortable: true, halign: 'center', align: 'center',  },
                { field: 'CheckUserName', title: '排查人', width: 120, sortable: true, halign: 'center', align: 'center', },
                {
                    field: 'CheckTime', title: '排查时间', width: 100, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        return ys.formatDate(value, "yyyy-MM-dd");
                    }
                },
                {
                    field: 'CheckResult', title: '排查结果', width: 80, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        if (value == "@CheckResultEnum.Yes.ParseToInt()") {
                            return '<span class="badge badge-primary">' + "@CheckResultEnum.Yes.GetDescription()" + '</span>';
                        } else {
                            return '<span class="badge badge-warning">' + "@CheckResultEnum.No.GetDescription()" + '</span>';
                        }
                    }
                },
                {
                    field: 'IsRectification', title: '是否整改', width: 80, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = "";
                        if (value != -1) {
                            if (value == "@IsEnum.Yes.ParseToInt()") {
                                html= '<span class="badge badge-primary">' + "@IsEnum.Yes.GetDescription()" + '</span>';
                            } else {
                                html= '<span class="badge badge-warning">' + "@IsEnum.No.GetDescription()" + '</span>';
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'RectificationTime', title: '整改时间', width: 100, sortable: true, halign: 'center', align: 'center',
                    formatter: function (value, row, index) {
                        var html = ys.formatDate(value, "yyyy-MM-dd");
                        if (html != "1970-01-01") {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        } else {
                            html = "";
                        }
                        return html;
                    }
                },
                // {
                //    title: '操作',
                //    align: 'center',
                //    formatter: function (value, row, index) {
                //        var actions = [];
                //        actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="Del(\'' + row.Id + '\')"><i class="fa fa-remove"></i>删除</a>&nbsp;&nbsp;');
                //        actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="Amend(\'' + row.Id + '\')"><i class="fa fa-key"></i>修改</a>');
                //        return actions.join('');
                //    }
                //}
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function showSaveForm(bAdd) {
        var id = 0;
        if (!bAdd) {
            var selectedRow = $('#gridTable').bootstrapTable('getSelections');
            if (!ys.checkRowEdit(selectedRow)) {
                return;
            }
            else {
                id = selectedRow[0].Id;
            }
        }
        ys.openDialog({
            title: id > 0 ? '编辑' : '添加',
            content: '@Url.Content("~/BusinessManage/CheckRectification/CheckRectificationForm")' + '?id=' + id,
            width: '768px',
            callback: function (index, layero) {
                var iframeWin = window[layero.find('iframe')[0]['name']];
                iframeWin.saveForm(index);
            }
        });
    }

    function deleteForm() {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (ys.checkRowDelete(selectedRow)) {
            ys.confirm('确认要删除选中的' + selectedRow.length + '条数据吗？', function () {
                var ids = ys.getIds(selectedRow);
                ys.ajax({
                    url: '@Url.Content("~/BusinessManage/CheckRectification/DeleteFormJson")' + '?ids=' + ids,
                    type: 'post',
                    success: function (obj) {
                        if (obj.Tag == 1) {
                            ys.msgSuccess(obj.Message);
                            searchGrid();
                        }
                        else {
                            ys.msgError(obj.Message);
                        }
                    }
                });
            });
        }
    }



    function exportForm() {
        var url = '@Url.Content("~/BusinessManage/CheckRectification/ExportCheckRectificationJson")';//排查清单（101004）
        var postData = $("#searchDiv").getWebControls();
        postData.BasePageCode = BasePageCode;//排查清单
        ys.exportExcel(url, postData);
    }
</script>
