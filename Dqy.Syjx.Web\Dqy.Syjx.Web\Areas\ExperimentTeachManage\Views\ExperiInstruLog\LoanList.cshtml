﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="schoolYearStart" col="SchoolYearStart" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="schoolTerm" col="SchoolTerm" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="LoanSysUserId" col="LoanSysUserId" style="display:inline-block;width:120px;"></span>
                    </li>
                    <li>
                        <span id="statuz" col="Statuz" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <span id="ExperimentStatuz" col="ExperimentStatuz" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="experimentName" col="ExperimentName" placeholder="实验名称" />
                    </li>
                    <li>
                        <div id="IsShowAll" col="IsShowAll" style="display:inline-block;width:120px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnBatchJ" class="btn btn-success" onclick="openBatchJieForm()"><i class="fa fa-bars"></i> 批量借出</a>
            <a id="btnBatchH" class="btn btn-success" onclick="openBatchHuanForm()"><i class="fa fa-bars"></i> 批量归还</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var Com_SchoolTermYear = 0;
    var Com_SchoolTerm = 1;
    $(function () {
        loadSearchCombo();
        ys.ajax({
            url: '@Url.Content("~/ExperimentTeachManage/ExperimentBooking/GetSchoolTermInfo")',
            type: 'get',
            async: false,
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data != undefined) {
                    if (obj.Data.SchoolTermStartYear != undefined && parseInt(obj.Data.SchoolTermStartYear) > 0) {
                        Com_SchoolTermYear = obj.Data.SchoolTermStartYear;
                        $("#schoolYearStart").ysComboBox('setValue', Com_SchoolTermYear);
                    }
                    if (obj.Data.SchoolTerm == 1 || obj.Data.SchoolTerm == 2) {
                        Com_SchoolTerm = obj.Data.SchoolTerm;
                        $("#schoolTerm").ysComboBox('setValue', Com_SchoolTerm);
                    }
                }
            }
        });
        initGrid();
    });

    function initGrid() {
        var queryUrl = '@Url.Content("~/ExperimentTeachManage/ExperiInstruLog/GetStatuzPageListJson")?OptType=1';//借出
        $('#gridTable').ysTable({
            url: queryUrl,
            columns: [
                {
                    field: '', title: '操作', halign: 'center', align: 'center', width: 120,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Statuz == @ExperiInstruStatuzEnum.FillingIn.ParseToInt()) {
                            html = $.Format('<a class="btn btn-success btn-xs" href="#" onclick="editForm(\'{0}\')" value="{0}"><i class="fa fa-edit"></i>借出</a> ', row.Id);
                        }
                        else if (row.Statuz == @ExperiInstruStatuzEnum.Loaned.ParseToInt()) {
                            html += $.Format('<a class="btn btn-primary btn-xs" href="#" onclick="backForm(\'{0}\')" value="{0}"><i class="fa fa-edit"></i>归还</a> ', row.Id);
                            html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(\'{0}\')" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        }
                        else if (row.Statuz == @ExperiInstruStatuzEnum.Backed.ParseToInt()) {
                            html += $.Format('<a class="btn btn-info btn-xs" href="#" onclick="lookForm(\'{0}\')" value="{0}"><i class="fa fa-eye"></i>查看</a> ', row.Id);
                        }
                        return html;
                    }
                },
                { checkbox: true, visible: true },
                { field: 'LoanName', title: '借出人', sortable: true, halign: 'center', align: 'center', width: 80 },
                {
                    field: 'LoanTime', title: '借出时间', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = ys.formatDate(value, "yyyy-MM-dd");
                        }
                        return html;
                    }
                },
                {
                    field: 'BackTime', title: '归还时间', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (row.Statuz == 3) {
                                html = ys.formatDate(value, "yyyy-MM-dd");
                            }

                        }
                        return html;
                    }
                },
                {
                    field: 'Statuz', title: '借出状态', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        switch (value) {
                            case @ExperiInstruStatuzEnum.FillingIn.ParseToInt():
                                return "@ExperiInstruStatuzEnum.FillingIn.GetDescription()";
                            case @ExperiInstruStatuzEnum.Loaned.ParseToInt():
                                return "@ExperiInstruStatuzEnum.Loaned.GetDescription()";
                            case @ExperiInstruStatuzEnum.Backed.ParseToInt():
                                return "@ExperiInstruStatuzEnum.Backed.GetDescription()";
                        }
                    }
                },
                { field: 'CourseName', title: '学科', sortable: true, halign: 'center', align: 'center', width: 50 },
                {
                    field: 'ExperimentName', title: '实验名称', sortable: true, halign: 'center', align: 'left', width: 200,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            switch (row.ExperimentType) {
                                case @ExperimentTypeEnum.Demo.ParseToInt():
                                    html += Syjx.GetCircleDemoHtml();
                                    break;
                                case @ExperimentTypeEnum.Group.ParseToInt():
                                    html += Syjx.GetCircleGroupHtml();
                                    break;
                                default:
                                    html += '';
                                    break;
                            }
                            html += row.ExperimentName;
                        }
                        return html;
                    }
                },
                {
                    field: 'SectionShow', title: '上课时间', sortable: true, halign: 'center', align: 'center', width: 110,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (parseInt(row.SectionIndex) > 0) {
                                html = $.Format("{0}(第{1}节)", ys.formatDate(row.ClassTime, "yyyy-MM-dd"), row.SectionIndex);
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'FunRoomName', title: '上课地点', sortable: true, halign: 'center', align: 'left', width: 140,
                    formatter: function (value, row, index) {
                        var html = '';
                        if (row.Id) {
                            html = '--';
                            if (parseInt(row.FunRoomId) == 1) {
                                html = '普通教室';
                            } else {
                                if (value != undefined && value.length > 0) {
                                    html = value;
                                }
                            }
                        }
                        return html;
                    }
                },
                {
                    field: 'ExperimentStatuz', title: '实验状态', sortable: true, halign: 'center', align: 'center', width: 100,
                    formatter: function (value, row, index) {
                        var html = '';
                        switch (value) {
                            case @ExperimentBookStatuzEnum.WaitRecord.ParseToInt():
                                html = "@ExperimentBookStatuzEnum.WaitRecord.GetDescription()";
                                break;
                            case @ExperimentBookStatuzEnum.AlreadyRecord.ParseToInt():
                                html = "@ExperimentBookStatuzEnum.AlreadyRecord.GetDescription()";
                                break;
                            default:
                                html = '--';
                                break;
                        }
                        return html;
                    }
                },
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        $('#gridTable').ysTable('search');
    }

    function resetGrid() {
        //清空条件
        $('#schoolYearStart').ysComboBox('setValue', -1);
        $('#schoolTerm').ysComboBox('setValue', -1);
        $("#LoanSysUserId").ysComboBox('setValue', -1);
        $('#statuz').ysComboBox('setValue', -1);
        $('#ExperimentStatuz').ysComboBox('setValue', -1);
        $('#experimentName').val('');
        $('#IsShowAll input[name="IsShowAll_checkbox"]').prop('checked', false);
        $("#IsShowAll .icheckbox-blue").removeClass('checked');
        $('#gridTable').ysTable('search');
    }

    function loadSearchCombo() {
        ComBox.SchoolTermYear($('#schoolYearStart'), undefined, '学年');
        $("#schoolTerm").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(SchoolTermEnum).EnumToDictionaryString())), defaultName: '学期' });
        //有科学学科的人列表。
        $('#LoanSysUserId').ysComboBox({
            url: '@Url.Content("~/InstrumentManage/InstrumentLend/GetUserListJson")',
            key: 'Id',
            value: 'RealName',
            defaultName: '借出人'
        });
        var experimentStatuz=[];
        experimentStatuz.push({id:20,name:'待登记'});
        experimentStatuz.push({ id: 100, name: '已登记' });
        $('#ExperimentStatuz').ysComboBox({
            data: experimentStatuz,
            key: 'id',
            value: 'name',
            defaultName: '实验状态'
        });
        $("#statuz").ysComboBox({ data: ys.getJson(@Html.Raw(typeof(ExperiInstruStatuzEnum).EnumToDictionaryString())), defaultName: '状态' });
        $('#IsShowAll').ysCheckBox({
            data: [{ Key: 1, Value: '显示全部' }]
        });
    }

    function editForm(objid) {
        var url = '@Url.Content("~/ExperimentTeachManage/ExperiInstruLog/LoanForm")' + '?id=' + objid;
        var tagName = '实验仪器借用填报';
        createMenuItem(url, tagName);
    }

    function lookForm(objid) {
        var url = '@Url.Content("~/ExperimentTeachManage/ExperiInstruLog/DetailForm")' + '?id=' + objid;
        var tagName = '借用详情';
        createMenuItem(url, tagName);
    }

    function deleteForm(objid) {
        ys.confirm('确认要删除当前实验借用仪器数据吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperiInstruLog/DeleteFormJson")' + '?id=' + objid,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }

    function backForm(objid) {
        ys.confirm('确认归还当前实验仪器吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperiInstruLog/BackFormJson")' + '?id=' + objid,
                type: 'post',
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }


    function openBatchJieForm(objid) {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length == 0) {
            ys.msgError("您没有选择任何行！");
            return false;
        }
        var ids=[];
         $.each(selectedRow, function (i, obj) {
            if (obj.Id != null) {
                ids.push( obj.Id);
            }
        });
        ys.confirm('确认要借出选中实验的所有仪器吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperiInstruLog/BatchLoanFormJson")',
                type: 'post',
                data:{ids:ids},
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }


    function openBatchHuanForm(objid) {
        var selectedRow = $('#gridTable').bootstrapTable('getSelections');
        if (selectedRow.length == 0) {
            ys.msgError("您没有选择任何行！");
            return false;
        }
        var ids=[];
         $.each(selectedRow, function (i, obj) {
            if (obj.Id != null) {
                ids.push( obj.Id);
            }
        });
        ys.confirm('确认归还当前选中实验的所有仪器吗？', function () {
            ys.ajax({
                url: '@Url.Content("~/ExperimentTeachManage/ExperiInstruLog/BatchBackFormJson")',
                type: 'post',
                data:{ids:ids},
                success: function (obj) {
                    if (obj.Tag == 1) {
                        ys.msgSuccess(obj.Message);
                        searchGrid();
                    }
                    else {
                        ys.msgError(obj.Message);
                    }
                }
            });
        });
    }
</script>
