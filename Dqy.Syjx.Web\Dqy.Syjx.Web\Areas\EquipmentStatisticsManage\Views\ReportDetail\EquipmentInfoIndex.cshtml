﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";

}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }

   /*  .table {
        table-layout: fixed;
    } */
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <div id="BackupYear" col="BackupYear" style="display: inline-block; width: 100px;"></div>
                    </li>
                    <li>
                        <div id="SchoolId" col="SchoolId" style="display: inline-block; width: 180px;"></div>
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
            <a id="btnExport" class="btn btn-warning" onclick="exportForm()"><i class="fa fa-download"></i> 导出</a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    var columnArr = [];
    var Current_QueryYear = new Date().getFullYear();
    $(function () {
        $('#SchoolId').ysComboBox({
            url: '@Url.Content("~/OrganizationManage/Unit/GetChildrenHasKindergartenPageList")' + "?PageSize=10000", key: 'Id', value: 'Name',
            defaultName: '单位名称',
            minimumResultsForSearch: 0
        });
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        initGrid();
    });

    function initGrid() {

        var columns = [
            {
                field: 'MediaFunroomNum', title: '总数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'MediaInteractiveNum', title: '其中交互<br />式', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'MedialaboratoryNum', title: '总数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'MediaInteractiveLabNum', title: '其中交互<br />式', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'MediaOtherNum', title: '总数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'MediaInteractiveOtherNum', title: '其中交互<br />式', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'ComputerStudentNum', title: '总数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'ComputerPadNum', title: '其中平板', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'StudentNum', title: '学生数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return  '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'StudentComputerAvg', title: '生机比', halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    var content = '';
                    if (row.SchoolId > 0) {
                        content = value > 0 ? ComBox.ToLocaleString(value) + ' : 1' : '-';
                    } else {
                        content = '';
                    }
                    return '<div style="white-space: nowrap; text-align: center;">' + content + '</div>';
                }
            },
            {
                field: 'ComputerTeacherNum', title: '总数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'ComputerPortableNum', title: '其中平板', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'TeacherNum', title: '教师数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'TeacherComputerAvg', title: '师机比', halign: 'center', align: 'center', valign: 'middle', 
                formatter: function (value, row, index) {
                    var content = '';
                    if (row.SchoolId > 0) {
                        content = value > 0 ? ComBox.ToLocaleString(value) + ' : 1' : '-';
                    } else {
                        content = '';
                    }                    
                    return '<div style="white-space: nowrap; text-align: center;">' + content + '</div>';
                }
            },
            {
                field: 'SysNetworkWired', title: '有线网络<br />系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysNetworkWireless', title: '无线网络<br>系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysCampusBroadcasting', title: '校园广播<br>系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysStandardizedRoom', title: '标准化考<br/>场', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysOutdoorsLEDBig', title: '户外LED<br/>大屏系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysElectronicClassCard', title: '电子班牌<br/>系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysSecurityMonitor', title: '安防监控<br>系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysCampusVisitor', title: '校园访客<br>系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysOneCard', title: '一卡通系<br/>统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysLoT', title: '物联网系<br/>统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysCampusTV', title: '校园电视<br/>台', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'SysNormalizedBroadcast', title: '录播系统', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'NetworkAdminNum', title: '人数', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
            {
                field: 'NetworkFulltimeNum', title: '其中专职', sortable: true, halign: 'center', align: 'center', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value > 0 ? value.toLocaleString() : '-';
                    else return '<b>' + value.toLocaleString() + '</b>';
                }
            },
        ];
        var columnsGroup = [
            {
                field: 'index', title: '序号', colspan: 1, rowspan: 2, width: 60, halign: 'center', valign: 'middle', align: 'center',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return index + 1;
                    else return '';
                }
            },
            {
                field: 'SchoolCode', title: '<div style="width:80px;">单位编号</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
                formatter: function (value, row, index) {
                    if (row.SchoolId > 0) return value;
                    else return '';
                }

            },
            {
                field: 'SchoolName', title: '<div style="width:120px;">单位名称</div>', colspan: 1, rowspan: 2, sortable: true, halign: 'center', align: 'left', valign: 'middle',
                formatter: function (value, row, index) {
                    var html = value;
                    if (html == "<b>总计：<b>") {
                        html = "<div style='text-align:center;'><b>总计：<b></div>";
                    }
                    return html;
                }
            },
            { title: '普通教室多媒体（套）', align: 'center', colspan: 2, valign: 'middle' },
            { title: '专用教室多媒体（套）', align: 'center', colspan: 2, valign: 'middle' },
            { title: '其它场所多媒体（套）', align: 'center', colspan: 2 , valign: 'middle'},
            { title: '学生计算机（台）', align: 'center', colspan: 4, valign: 'middle' },
            { title: '教师计算机（台）', align: 'center', colspan: 4, valign: 'middle' },
            { title: '信息化系统（套）', align: 'center', colspan: 12, valign: 'middle' },
            { title: '网络管理员（人）', align: 'center', colspan: 2, valign: 'middle' },
        ];
        columnArr = [
            columnsGroup,
            columns
        ];
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            var postData = {
                ModuleId: @BackupsModuleIdEnum.ZbStatistics.ParseToInt(),
                PageCode: "@BackupsPageCodeEnum.InfelTechnology.ParseToInt().ToString()",
                BackupType: @BackupTypeEnum.DataTitleJson.ParseToInt(),
                BackupYear: thisYear
            };
            $.ajax({
                url: '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetBackupsTitleJson")',
                async: false,
                type: 'get',
                data: postData,
                success: function (obj) {
                    if (obj.Tag == 1) {
                        var colArr = obj.Data[0].Content;
                        if (typeof (colArr) == "string") {
                            colArr = JSON.parse(colArr);
                        }
                        if (colArr && colArr.length > 0) {
                            columnArr = [];
                            for (var i = 0; i < colArr.length; i++) {
                                columnArr.push(Syjx.GetGridTableColumns(colArr[i]));
                            }
                        }
                    } else {

                    }
                }
            });
        }
        var queryUrl = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/GetInfoPageListJson")';

        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Sort ASC ,SchoolId DESC ',
            columns: columnArr,
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            }
        });
    }

    function searchGrid() {
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        if (Current_QueryYear != thisYear && parseInt(thisYear) > 0) {
            Current_QueryYear = thisYear;
            $('#gridTable').bootstrapTable('destroy');
            initGrid();//重新表头。
        } else {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
    }


    function resetGrid() {
        $("#SchoolId").ysComboBox('setValue', -1);
        ComBox.loadSearchYear($("#BackupYear"), "年度");
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }


    function exportForm() {
        var url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/InfoExport")';
        var postData = $("#searchDiv").getWebControls();
        var thisYear = $("#BackupYear").ysComboBox('getValue');
        var thisCurrentYear = new Date().getFullYear();
        if (thisCurrentYear != thisYear) {
            url = '@Url.Content("~/EquipmentStatisticsManage/ReportDetail/BackupExport")';
            postData.PageCode = '@BackupsPageCodeEnum.InfelTechnology.ParseToInt().ToString()';
        }
        ys.exportExcel(url, postData);
    }
</script>

