﻿@{
    Layout = "~/Views/Shared/_Index.cshtml";
}
<style type="text/css">
    .select2-container {
        width: 100% !important;
    }

    .select-list li input {
        width: auto !important;
    }

    .modelShow {
        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
<div class="container-div">
    <div class="row">
        <div id="searchDiv" class="col-sm-12 search-collapse">
            <div class="select-list">
                <ul>
                    <li>
                        <span id="instrumentEvaluateProjectId" col="InstrumentEvaluateProjectId" style="display:inline-block;width:200px;"></span>
                    </li>       
                    <li>
                        <span id="courseId" col="CourseId" style="display:inline-block;width:100px;"></span>
                    </li>
                    <li>
                        <input id="keyWord" col="KeyWord" placeholder="仪器代码、名称" style="width:150px" />
                    </li>
                    <li>
                        <a id="btnSearch" class="btn btn-primary btn-sm" onclick="searchGrid()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                    </li>
                    <li>
                        <a class="btn btn-secondary btn-sm" onclick="resetGrid()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </li>
                    <!--帮助文档需要内容,id值必须为“helpBtn”-->
                    <li>
                        <i class="fa fa-question-circle" id="helpBtn"></i>
                    </li>
                </ul>
            </div>
        </div>
        <div class="btn-group d-flex" role="group" id="toolbar">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="gridTable" data-mobile-responsive="true"></table>
        </div>
    </div>
</div>
<!--帮助文档需要内容,id值必须为“tipMsg”-->
<div id="tipMsg"></div>

<script type="text/javascript">
    $(function () {
        loadInstrumentEvaluateProjectVersionId();
        getCourse();
        
        
    });

    function initGrid() {
        if (!$('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            ys.msgError('请先选择评估项目名称！');
            return false;
        }
        var queryUrl = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/GetInstrumentStandardListJson")';
        $('#gridTable').ysTable({
            url: queryUrl,
            sortName: 'Id asc',
            columns: [
                {
                    field: 'opt', title: '操作', align: 'center', halign: 'center', width: commonWidth.Instrument.Opt1,
                    formatter: function (value, row, index) {
                        return $.Format('<a class="btn btn-success btn-xs" href="#" onclick="edit(this)" value="{0}"><i class="fa fa-edit"></i> 填报</a> ', row.Id);
                    }
                },
                //{ field: 'StageName', title: '学段', halign: 'center', align: 'center', sortable: true, width: 50 },
                { field: 'CourseName', title: '适用学科', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Course },
                //{ field: 'InstrumentCode', title: '仪器代码', halign: 'center', align: 'center', sortable: true, width: 80 },
                { field: 'Name', title: '仪器名称', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Name,
                     formatter: function (value, row, index) {
                        var html = "";
                        if (row.IsDangerChemical == 1) {
                            html += Syjx.GetDangerHtml();
                        }
                        if (row.IsSelfMade == 1) {
                            html += Syjx.GetSelfMadeHtml();
                        }
                        html += value;
                        return html;
                    }
                },
                {
                    field: 'Model', title: '规格型号', halign: 'center', align: 'left', sortable: true, width: commonWidth.Instrument.Model,
                    formatter: function (value, row, index) {
                        var html = value == null ? "" : value;
                        html = `<span class='modelShow' data-toggle='tooltip' data-placement='top' data-content='${html}'>${html}</span>`;
                        return html;
                    }
                },
                { field: 'StandardNum', title: '指标量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'StockNum', title: '存量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                { field: 'UnitName', title: '单位', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.UnitName },
                {
                    field: 'AllocateType', title: '配备要求', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.AllocateType,
                    formatter: function (value, row, index) {
                        return value == 1 ? '必配' : '选配';
                    }
                },
                {
                    field: 'DifferenceNum', title: '差额', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num,
                    formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red;">' + value + '</span>';
                        }
                        else {
                            return value;
                        }
                    }
                },
                { field: 'InputNum', title: '已填报量', halign: 'center', align: 'center', sortable: true, width: commonWidth.Instrument.Num },
                
            ],
            queryParams: function (params) {
                var pagination = $('#gridTable').ysTable('getPagination', params);
                var queryString = $('#searchDiv').getWebControls(pagination);
                return queryString;
            },
            onLoadSuccess: function () {
                $(".modelShow").popover({
                    trigger: 'hover',
                    html: true
                });
            }
        });
    }

    function loadInstrumentEvaluateProjectVersionId() {
        ys.ajax({
            url: '@Url.Content("~/EvaluateManage/InstrumentEvaluateProject/GetProjectByUser")',
            data: null,
            type: 'get',
            success: function (obj) {
                if (obj.Tag == 1 && obj.Data) {
                    $('#instrumentEvaluateProjectId').ysComboBox({
                        data: obj.Data,
                        key: 'Id',
                        value: 'EvaluateName',
                        defaultName: '评估项目名称'
                    });
                    if (obj.Data != undefined && obj.Data.length > 0) {
                        $('#instrumentEvaluateProjectId').ysComboBox('setValue', obj.Data[0].Id);
                    }
                    initGrid();
                }
                else {
                    ComBox.LoadPageMessage('您管理的学科', '/BusinessManage/UserSchoolStageSubject/StageSubjectInput', '实验员授权', @RoleEnum.SchoolManager.ParseToInt());
                }
            }
        });
    }

    function getCourse() {
        $('#courseId').ysComboBox({
            url: '@Url.Content("~/SystemManage/StaticDictionary/GetListJson")' + '?TypeCode=@DicTypeCodeEnum.Course.ParseToInt()&OptType=4',
            key: 'DictionaryId',
            value: 'DicName',
            dataName: 'Data',
            defaultName: '适用学科'
        });
    }

    function searchGrid() {
        if ($('#instrumentEvaluateProjectId').ysComboBox('getValue') > 0) {
            $('#gridTable').ysTable('search');
            resetToolbarStatus();
        }
        else {
            ys.msgError('请先选择评估项目名称查询！');
            return false;
        }
    }

    function resetGrid() {
        //清空条件
        $('#courseId').ysComboBox('setValue', -1);
        $('#keyWord').val('');
        $('#gridTable').ysTable('search');
        resetToolbarStatus();
    }

    function edit(obj) {
        var id = $(obj).attr('value');
        var url = '@Url.Content("~/InstrumentManage/PurchaseDeclaration/StandardDeclarationForm")' + '?attendstaticid=' + id;
        createMenuItem(url, "达标填报");
    }
</script>
